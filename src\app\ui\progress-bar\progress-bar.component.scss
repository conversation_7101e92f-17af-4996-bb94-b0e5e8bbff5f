:host {
  position: relative !important;
  width: 0;
  height: 0;
  display: block;
  left: 0;
  pointer-events: none;
  max-width: 100%;
  z-index: 2;
  overflow: visible;

  &:after {
    content: '';
    background: inherit;
    position: absolute;
    width: 100%;
    height: 3px;
    top: -5px;
    left: 2px;
    right: 2px;
    border-radius: 4px;
  }

  //margin-bottom: -2px;

  ////  background-color: #999999;
  //

  //&:after {
  //  content: '';
  //  position: absolute;
  //  bottom: 50%;
  //  right: -1px;
  //  transform: translateY(50%);
  //  background: inherit;
  //  width: 2px;
  //  height: 9px;
  //  border: 1px solid inherit;
  //}

  .task.is-done & {
    //opacity: 0.6;
  }

  .task.is-current & {
    //background-color: $green !important;
  }
}
