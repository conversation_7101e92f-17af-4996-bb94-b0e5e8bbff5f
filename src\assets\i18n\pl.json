{"ANDROID": {"NOTIFICATIONS": {"SYNC_CONFLICT_MSG": "Sprawdź i zdecyduj, co zrobić.", "SYNC_CONFLICT_TITLE": "Wystąpił konflikt synchronizacji"}, "PERMANENT_NOTIFICATION_MSGS": {"INITIAL": "Aplikacja działająca w tle, aby um<PERSON><PERSON><PERSON><PERSON><PERSON> synchronizację, jeśli jest włączona", "NO_ACTIVE_TASKS": "Brak aktywnych zadań", "SYNCING": "Synchronizacja"}}, "APP": {"B_INSTALL": {"IGNORE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "INSTALL": "<PERSON><PERSON><PERSON><PERSON>", "MSG": "<PERSON><PERSON><PERSON> z<PERSON>ć Super Productivity jako PWA?"}, "B_OFFLINE": "Zostałeś odłączony od internetu. Synchronizacja nie będ<PERSON>.", "UPDATE_MAIN_MODEL": "Super Productivity posiada ważną aktualizację! Wymagana jest migracja da<PERSON>, która spowoduje że starsze wersje aplikacji nie będą mogły ich używać.", "UPDATE_MAIN_MODEL_NO_UPDATE": "No model update chosen. Please note that you either have to downgrade to the last version, if you do not want to perform the model upgrade.", "UPDATE_WEB_APP": "Nowa wersja dostępna. Załadować ją?"}, "BL": {"NO_TASKS": "Backlog nie zawiera zadań"}, "CONFIRM": {"AUTO_FIX": "Twoje dane są popsute. <PERSON><PERSON> ch<PERSON>z je automatycznie naprawić? Może wystąpić utrata danych.", "RELOAD_AFTER_IDB_ERROR": "Nie można uzyskać dostępu do bazy danych :( Możliwe przyczyny to aktualizacja aplikacji w tle lub mało miejsca na dysku. <PERSON><PERSON><PERSON> zainstalowałeś aplikację na Linuksie jako snap, chcesz również włącz<PERSON><PERSON> świadomość odświeżania \"snap set core experimental.refresh-app-awareness=true\", dopóki nie naprawią tego problemu po swojej stronie. Naciśnij OK, aby ponownie załadować aplikację (może wymagać ręcznego ponownego uruchomienia aplikacji na niektórych platformach).", "RESTORE_FILE_BACKUP": "Wygląda na to, że aplikacja nie posiada danych, ale istnieje kopia zapasowa w \"{{dir}}\". <PERSON><PERSON> ch<PERSON> j<PERSON> od<PERSON> z {{from}}?", "RESTORE_FILE_BACKUP_ANDROID": "Wygląda na to, <PERSON>e NIE MA DANYCH, ale dostępna jest kopia zapasowa. <PERSON><PERSON> ch<PERSON> j<PERSON> za<PERSON>?", "RESTORE_STRAY_BACKUP": "Ostatnia synchronizacja zawiera błąd. <PERSON><PERSON>z odtworzyć ostatnią kopię zapasową?"}, "DATETIME_SCHEDULE": {"LATER_TODAY": "Później d<PERSON>siaj", "NEXT_WEEK": "Następny tydzień", "PLACEHOLDER": "<PERSON><PERSON><PERSON> wy<PERSON>ć datę", "PRESS_ENTER_AGAIN": "Naciśnij enter aby zapusać", "TOMORROW": "<PERSON><PERSON>"}, "F": {"ATTACHMENT": {"DIALOG_EDIT": {"ADD_ATTACHMENT": "<PERSON><PERSON><PERSON>", "EDIT_ATTACHMENT": "<PERSON><PERSON><PERSON><PERSON>", "LABELS": {"FILE": "Ścieżka do pliku", "IMG": "Zdjęcie", "LINK": "Url"}, "SELECT_TYPE": "W<PERSON>bierz typ", "TYPES": {"FILE": "Plik (otwiera domyślną aplikacje)", "IMG": "<PERSON><PERSON><PERSON><PERSON><PERSON> (otwiera się jako thumbnail)", "LINK": "Link (otwiera się w nowej karcie)"}}}, "BOARDS": {"DEFAULT": {"DONE": "<PERSON><PERSON><PERSON>", "EISENHAUER_MATRIX": "<PERSON><PERSON><PERSON>", "IMPORTANT": "Ważne", "IN_PROGRESS": "W trakcie realizacji", "KANBAN": "Ka<PERSON><PERSON>", "NOT_URGENT_IMPORTANT": "Niepilne i Ważne", "NOT_URGENT_NOT_IMPORTANT": "Niepilne i Nieważne", "TO_DO": "Do zrobienia", "URGENT": "<PERSON><PERSON><PERSON>", "URGENT_IMPORTANT": "Pilne i Ważne", "URGENT_NOT_IMPORTANT": "Pilne i Nieważne"}, "FORM": {"ADD_NEW_PANEL": "<PERSON><PERSON><PERSON>y panel", "BACKLOG_TASK_FILTER_ALL": "Wszystkie", "BACKLOG_TASK_FILTER_NO_BACKLOG": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BACKLOG_TASK_FILTER_ONLY_BACKLOG": "<PERSON><PERSON><PERSON>", "BACKLOG_TASK_FILTER_TYPE": "Zadania listy prac", "COLUMNS": "<PERSON><PERSON><PERSON>", "TAGS_EXCLUDED": "Wykluczone tagi", "TAGS_REQUIRED": "<PERSON><PERSON><PERSON><PERSON> tagi", "TASK_DONE_STATE": "Stan zadania zakończonego", "TASK_DONE_STATE_ALL": "Wszystkie", "TASK_DONE_STATE_DONE": "<PERSON><PERSON><PERSON>", "TASK_DONE_STATE_UNDONE": "Niezrobione"}, "V": {"ADD_NEW_BOARD": "<PERSON><PERSON><PERSON>ą tablicę", "CONFIRM_DELETE": "<PERSON>zy na pewno chcesz usunąć tę tablicę?", "CREATE_NEW_TAG_BTN": "Utwórz tag", "CREATE_NEW_TAG_MSG": "1 nowy tag musi zostać utworzony, aby ta tablica działała", "CREATE_NEW_TAGS_BTN": "Utwórz tagi", "CREATE_NEW_TAGS_MSG": "{{nr}} nowe tagi muszą zostać utworzone, aby ta tablica działała", "EDIT_BOARD": "<PERSON><PERSON><PERSON><PERSON>", "NO_PANELS_BTN": "Skonfiguruj tab<PERSON>", "NO_PANELS_MSG": "Ta tablica nie ma skonfigurowanych paneli. Dodaj do niej kilka paneli."}}, "CALDAV": {"DIALOG_INITIAL": {"TITLE": "Ustaiwenia synchronizacji ka<PERSON> (CalDav) dla projektu"}, "FORM": {"CALDAV_CATEGORY_FILTER": "Kategoria do filtrowania zadań CalDav dla (zostaw puste dla żadnego)", "CALDAV_PASSWORD": "<PERSON><PERSON>", "CALDAV_RESOURCE": "Nazwa zasobu CalDav do synchronizacji (kalendarz)", "CALDAV_URL": "CalDav URL (podstawowy URL)", "CALDAV_USER": "Twój użytkownik CalDav", "IS_TRANSITION_ISSUES_ENABLED": "Automatycznie wypełniaj zadania CalDav po zakończeniu zadania"}, "FORM_SECTION": {"HELP": "<p>Here you can configure SuperProductivity to list uncompleted CalDav todos for a specific project in the task creation panel in the daily planning view. They will be listed as suggestions and will provide a link to the todo as well as more information about it.</p> <p>In addition you can automatically add and sync all uncompleted todos to your task backlog.</p>", "TITLE": "CalDav"}, "ISSUE_CONTENT": {"ASSIGNEE": "Osoba <PERSON>", "AT": "na", "ATTACHMENTS": "Załączniki", "CHANGED": "Zmienione", "COMMENTS": "<PERSON><PERSON><PERSON><PERSON>", "COMPONENTS": "Komponenty", "DESCRIPTION": "Opis", "LABELS": "<PERSON><PERSON><PERSON>", "LIST_OF_CHANGES": "Lista zmian", "MARK_AS_CHECKED": "Zaznacz aktualizacje", "ON": "W", "RELATED": "Powiązane", "STATUS": "Status", "STORY_POINTS": "Punkty historii", "SUB_TASKS": "Podzadania", "SUMMARY": "Podsumowanie", "WORKLOG": "Dziennik pracy", "WRITE_A_COMMENT": "Napisz komentarz"}, "S": {"CALENDAR_NOT_FOUND": "CalDav: Calendar \"{{calendarName}}\" not found", "CALENDAR_READ_ONLY": "CalDav: Calendar \"{{calendarName}}\" is readonly", "ISSUE_NOT_FOUND": "CalDav: Todo \"{{issueId}}\" seems to be deleted on server."}}, "CALENDARS": {"BANNER": {"ADD_AS_TASK": "<PERSON><PERSON><PERSON> jako <PERSON>", "FOCUS_TASK": "Zadanie główne", "TXT": "<strong>{{title}}</strong> z<PERSON><PERSON>na się od <strong>{{start}}</strong>!", "TXT_MULTIPLE": "<strong>{{title}}</strong> zac<PERSON>na się od <strong>{{start}}</strong>!<br> (i {{nrOfOtherBanners}} inne zdarzenia są należne)", "TXT_PAST": "<strong>{{title}}</strong> r<PERSON><PERSON><PERSON><PERSON>ł si<PERSON> w <strong>{{start}}</strong>!", "TXT_PAST_MULTIPLE": "<strong>{{title}}</strong> r<PERSON><PERSON><PERSON><PERSON>ł się w <strong>{{start}}</strong>!<br> (i {{nrOfOtherBanners}} inne zdarzenia są należne)"}, "S": {"CAL_PROVIDER_ERROR": "<PERSON><PERSON><PERSON><PERSON> dostawcy kalendarza: {{errTxt}}"}}, "CONFIG": {"S": {"UPDATE_SECTION": "Updated settings for <strong>{{sectionKey}}</strong>"}}, "D_RATE": {"A_HOW": "Jak i gdzie oceniać", "BTN_DONT_BOTHER": "Nie zawracaj mi więcej głowy", "TITLE": "🙈 Proszę nam w<PERSON>, ale...", "TXT": "Ogromnie pomógłbyś projektowi, <strong>wystawiając mu dobrą ocenę, jeśli Ci się spodoba!</strong>"}, "DOMINA_MODE": {"FORM": {"HELP": "Powtarza skonfigurowaną frazę co X podczas śledzenia czasu wykonywania zadania.", "L_INTERVAL": "Interwał powtarzania frazy", "L_TEXT": "Tekst", "L_TEXT_DESCRIPTION": "Np: \"Work on ${currentTaskTitle}!\"", "L_VOICE": "<PERSON><PERSON><PERSON><PERSON> gł<PERSON>", "L_VOICE_DESCRIPTION": "<PERSON><PERSON><PERSON><PERSON> gł<PERSON>", "TITLE": "<PERSON><PERSON>"}}, "DROPBOX": {"S": {"ACCESS_TOKEN_ERROR": "Dropbox: Unable to generate Access Token from Auth Code", "ACCESS_TOKEN_GENERATED": "Dropbox: Access Token generated from Auth Code", "AUTH_ERROR": "Dropbox: Invalid access token provided", "AUTH_ERROR_ACTION": "Żeton zmiany", "OFFLINE": "Dropbox: Unable to sync, because offline", "SYNC_ERROR": "Dropbox: Error while syncing", "UNABLE_TO_GENERATE_PKCE_CHALLENGE": "Dropbox: <PERSON><PERSON> m<PERSON> wygenerować wyzwania PKCE."}}, "FINISH_DAY_BEFORE_EXIT": {"C": {"FINISH_DAY_BEFORE_EXIT": "Na liście zadań na dziś znajduje się {{nr}} wykonanych zadań, które nie zostały jeszcze przeniesione do archiwum. <PERSON><PERSON> naprawd<PERSON> chcesz zakończyć pracę bez zakończenia dnia?"}}, "FOCUS_MODE": {"B": {"SESSION_RUNNING": "Se<PERSON>ja skupienia trwa", "TO_FOCUS_OVERLAY": "Do nakładki skupienia"}, "BACK_TO_PLANNING": "Powrót do planowania", "CONGRATS": "Gratulacje za ukończenie tej sesji!", "CONTINUE_FOCUS_SESSION": "Kontynuacja sesji fokusowej", "COUNTDOWN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FINISH_TASK_AND_SELECT_NEXT": "Zakończ zadanie i wybierz następny", "FLOWTIME": "Flowtime", "FOR_TASK": "dla zadania", "GET_READY": "Przygotuj się na sesję skupienia!", "GO_TO_PROCRASTINATION": "Uzy<PERSON><PERSON> pomoc, gdy zwlekasz", "GOGOGO": "<PERSON><PERSON>, dalej, dalej!!!", "NEXT": "Następny", "ON": "na", "OPEN_ISSUE_IN_BROWSER": "Otwórz problem w przeglądarce", "POMODORO_BACK": "Powró<PERSON>", "POMODORO_DISABLE": "Wyłącz Pomodoro", "POMODORO_INFO": "Se<PERSON>je skupienia nie mogą być używane razem z włączonym pomodoro timerem.", "PREP_GET_MENTALLY_READY": "Przygotuj się psychicznie, aby być skupionym i produktywnym", "PREP_SIT_UPRIGHT": "Siedzenie (lub stanie) w pozycji wyprostowanej", "PREP_STRETCH": "Wykonaj lekkie rozciąganie", "SELECT_ANOTHER_TASK": "Wybierz inne zadanie", "SELECT_TASK": "<PERSON><PERSON><PERSON><PERSON>, na którym chcesz się skupić", "SESSION_COMPLETED": "Sesja skupienia zakończona!", "SET_FOCUS_SESSION_DURATION": "Ustaw czas trwania sesji sku<PERSON>nia", "SHOW_HIDE_NOTES_AND_ATTACHMENTS": "Wyświetlanie/ukrywanie notatek i załączników do zadań", "START_FOCUS_SESSION": "Rozpoczęcie sesji skupienia", "START_NEXT_FOCUS_SESSION": "Rozpoczęcie następnej sesji fokusowej", "WORKED_FOR": "Pracowałeś dla"}, "GITEA": {"DIALOG_INITIAL": {"TITLE": "Konfiguracja Gitea dla projektu"}, "FORM": {"FILTER_USER": "Nazwa użytkownika (np. w celu odfiltrowania własnych zmian)", "HOST": "Host (np.: https://try.gitea.io)", "REPO_FULL_NAME": "Nazwa użytkownika lub nazwa organizacji/projektu", "REPO_FULL_NAME_DESCRIPTION": "Można go znaleźć jako c<PERSON> adresu URL podczas przeglądania projektu w przeglądarce.", "SCOPE": "<PERSON><PERSON><PERSON>", "SCOPE_ALL": "Wszystkie", "SCOPE_ASSIGNED": "Przydzielony do mnie", "SCOPE_CREATED": "Stworzony przeze mnie", "TOKEN": "<PERSON><PERSON> dos<PERSON>"}, "FORM_SECTION": {"HELP": "<p><PERSON><PERSON><PERSON> można skonfigurować SuperProductivity tak, aby wyświetlał listę otwartych zgłoszeń Gitea dla określonego repozytorium w panelu tworzenia zadań w widoku planowania dziennego. Będą one wyświetlane jako sugestie i będą zawierać link do zgłoszenia oraz więcej informacji na jego temat.</p> <p>Ponadto możesz automatycznie dodawać i importować wszystkie otwarte zgłoszenia.</p><p>Aby ominąć limity użytkowania i uzyskać dostęp, moż<PERSON>z podać token dostępu.", "TITLE": "<PERSON><PERSON><PERSON>"}, "ISSUE_CONTENT": {"ASSIGNEE": "Odbiorca", "AT": "na", "DESCRIPTION": "Opis", "LABELS": "Etykiety", "MARK_AS_CHECKED": "Oznaczanie aktualizacji jako sprawdzonych", "PROJECT": "Projekt", "STATUS": "Status", "SUMMARY": "Podsumowanie", "WRITE_A_COMMENT": "Napisz komentarz"}, "S": {"ERR_UNKNOWN": "Gitea: <PERSON><PERSON><PERSON><PERSON> błąd {{statusCode}} {{errorMsg}}. A<PERSON> Przek<PERSON> limit szyb<PERSON>?"}}, "GITHUB": {"DIALOG_INITIAL": {"TITLE": "Skonfiguruj GitHub dla projektu"}, "FORM": {"FILTER_USER": "Username (e.g. to filter out changes by yourself)", "INVALID_TOKEN_MESSAGE": "Nieprawidłowy token GitHub. Musi zaczynać się od \"ghp_\".", "IS_ASSIGNEE_FILTER": "Importuj tylko sprawy przypisane do mnie do zaległości", "REPO": "username/repositoryName", "TOKEN": "Access Token"}, "FORM_SECTION": {"HELP": "<p>Here you can configure SuperProductivity to list open GitHub issues for a specific repository in the task creation panel in the daily planning view. They will be listed as suggestions and will provide a link to the issue as well as more information about it.</p> <p>In addition you can automatically add and sync all open issues to your task backlog.</p><p>To get by usage limits and to access you can provide a an access token. <a href='https://docs.github.com/en/free-pro-team@latest/developers/apps/scopes-for-oauth-apps'>More info about its scopes can be found here</a>.", "TITLE": "GitHub"}, "ISSUE_CONTENT": {"ASSIGNEE": "Prz<PERSON><PERSON>any", "AT": "at", "DESCRIPTION": "Opis", "LABELS": "<PERSON><PERSON><PERSON>", "LAST_COMMENT": "Ostatni komentarz", "LOAD_ALL_COMMENTS": "Załaduj wszystkie {{nr}} komentarze", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "Załaduj opis i wszystkie komentarze", "MARK_AS_CHECKED": "Zaznacz zmiany jako sprawdzone", "STATUS": "Status", "SUMMARY": "Podsumowanie", "WRITE_A_COMMENT": "Napisz komentarz"}, "S": {"CONFIG_ERROR": "GitHub: <PERSON><PERSON><PERSON><PERSON> podczas mapowania danych. Czy nazwa repozytorium jest poprawna?", "ERR_UNKNOWN": "GitHub: Unknown error {{statusCode}} {{errorMsg}}. Api Rate limit exceeded?"}}, "GITLAB": {"DIALOG_INITIAL": {"TITLE": "Skonfiguruj GitLab dla projektu"}, "*********************": {"PAST_DAY_INFO": "Wstępnie wypełniony czas trwania zawiera nieśledzone dane z poprzednich dni.", "T_ALREADY_TRACKED": "<PERSON><PERSON>", "T_TITLE": "<PERSON><PERSON><PERSON>", "T_TO_BE_SUBMITTED": "Do przesłania", "TITLE": "Prześlij czas spędzony na zgłoszeniach do GitLab", "TOTAL_MSG": "Zgłosisz <em>{{totalTimeToSubmit}}</em> czasu pracy w dniu dzisie<PERSON> do <em>{{nrOfTasksToSubmit}}</em> różnych zagadnień."}, "FORM": {"FILTER": "Filtr niesta<PERSON>rdowy", "FILTER_DESCRIPTION": "Zob<PERSON>z https://docs.gitlab.com/ee/api/issues.html#list-issues. Wielokrotność może być łączona przez &", "FILTER_USER": "Username (e.g. to filter out changes by yourself)", "GITLAB_BASE_URL": "Custom GitLab base URL", "PROJECT": "project ID or user name/project", "PROJECT_HINT": "np. johannes<PERSON>/super-produkt<PERSON><PERSON><PERSON><PERSON><PERSON>", "SCOPE": "<PERSON><PERSON><PERSON>", "SCOPE_ALL": "Wszystkie", "SCOPE_ASSIGNED": "Przypisane do mnie", "SCOPE_CREATED": "Utworzone przeze mnie", "SOURCE": "Źródło", "SOURCE_GLOBAL": "Wszystkie", "SOURCE_GROUP": "Grupa", "SOURCE_PROJECT": "Projekt", "SUBMIT_TIMELOGS": "Przesyłanie dzienników czasowych do Gitlab", "SUBMIT_TIMELOGS_DESCRIPTION": "Wyświetlanie okna dialogowego śledzenia czasu po kliknięciu dnia zakończenia", "TOKEN": "Access Token"}, "FORM_SECTION": {"HELP": "<p>Here you can configure SuperProductivity to list open GitLab (either its the online version or a self-hosted instance) issues for a specific project in the task creation panel in the daily planning view. They will be listed as suggestions and will provide a link to the issue as well as more information about it.</p> <p>In addition you can automatically add and sync all open issues to your task backlog.</p>", "TITLE": "GitLab"}, "ISSUE_CONTENT": {"ASSIGNEE": "Prz<PERSON><PERSON>any", "AT": "at", "DESCRIPTION": "Opis", "LABELS": "<PERSON><PERSON><PERSON>", "MARK_AS_CHECKED": "Zaznacz zmiany jako sprawdzone", "PROJECT": "Projekt", "STATUS": "Status", "SUMMARY": "Podsumowanie", "WRITE_A_COMMENT": "Napisz komentarz"}, "S": {"ERR_UNKNOWN": "GitLab: Unknown error {{statusCode}} {{errorMsg}}"}}, "ISSUE": {"CROSS_ORIGIN_BROWSER_WARNING": "Ta integracja prawdopodobnie nie będzie d<PERSON>łać w Twojej przeglądarce. Pobierz wersję Super Productivity na komputer lub Androida!", "DEFAULT": {"ISSUE_STR": "<PERSON><PERSON><PERSON><PERSON>", "ISSUES_STR": "<PERSON><PERSON><PERSON>"}, "DEFAULT_PROJECT_DESCRIPTION": "Projekt przypisany do zadań utworzonych ze spraw.", "DEFAULT_PROJECT_LABEL": "Domyślny projekt super produktywności", "HOW_TO_GET_A_TOKEN": "<PERSON><PERSON> token?", "ISSUE_CONTENT": {"ASSIGNEE": "Osoba <PERSON>", "AT": "na", "ATTACHMENTS": "Załączniki", "AUTHOR": "Autor", "CATEGORY": "Kategoria", "CHANGED": "Zmienione", "COMMENTS": "<PERSON><PERSON><PERSON><PERSON>", "COMPONENTS": "Komponenty", "DESCRIPTION": "Opis", "DONE_RATIO": "Wskaźnik ukończenia", "DUE_DATE": "<PERSON><PERSON><PERSON>", "LABELS": "Etykiety", "LAST_COMMENT": "Ostatni komentarz", "LIST_OF_CHANGES": "Lista zmian", "LOAD_ALL_COMMENTS": "Załaduj wszystkie {{nr}} komentarze", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "Załaduj opis i wszystkie komentarze", "LOCATION": "Lokalizacja", "MARK_AS_CHECKED": "Oznacz aktualizacje jako sprawdzone", "ON": "W", "PRIORITY": "Priorytet", "RELATED": "Powiązane", "START": "Rozpocznij", "STATUS": "Status", "STORY_POINTS": "Punkty historii", "SUB_TASKS": "Podzadania", "SUMMARY": "Podsumowanie", "TIME_SPENT": "<PERSON>zas spędzony", "TYPE": "<PERSON><PERSON>", "VERSION": "<PERSON><PERSON><PERSON>", "WORKLOG": "Dziennik pracy", "WRITE_A_COMMENT": "Napisz komentarz"}, "S": {"ERR_GENERIC": "{{issueProviderName}} Błąd: {{errTxt}}", "ERR_NETWORK": "{{issueProviderName}}: Żądanie nie powiodło się z powodu błędu sieci po stronie klienta.", "ERR_NOT_CONFIGURED": "{{issueProviderName}}: Nieprawidłowo skonfigurowane", "IMPORTED_MULTIPLE_ISSUES": "{{issueProviderName}}: Zaimportowany {{nr}} nowy {{issuesStr}}", "IMPORTED_SINGLE_ISSUE": "{{issueProviderName}}: Importowane {{issueStr}} \"{{issueTitle}}\"", "ISSUE_DELETED_OR_CLOSED": "{{issueProviderName}}: {{issueStrC}} \"{{issueTitle}}\" wydaje się być usunięty lub zamknięty", "ISSUE_NO_UPDATE_REQUIRED": "{{issueProviderName}}: aktualizacja nie jest wymagana", "ISSUE_UPDATE_MULTIPLE": "{{issueProviderName}}: Zaktualizowane dane dla {{nr}} {{issuesStr}}", "ISSUE_UPDATE_SINGLE": "{{issueProviderName}}: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane dla \"{{issueTitle}}\"", "MANUAL_UPDATE_ISSUE_SUCCESS": "{{issueProviderName}}: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane dla \"{{issueTitle}}\"", "MISSING_ISSUE_DATA": "{{issueProviderName}}: Znaleziono zadania z brakującymi danymi {{issueStr}} . Przeładowanie.", "NEW_COMMENT": "{{issueProviderName}}: <PERSON><PERSON> komentarz dla \"{{issueTitle}}\"", "POLLING_BACKLOG": "{{issueProviderName}}: odpy<PERSON><PERSON>ie o nowe {{issuesStr}}", "POLLING_CHANGES": "{{issueProviderName}}: <PERSON><PERSON><PERSON> odpytywania dla {{issuesStr}}."}}, "JIRA": {"BANNER": {"BLOCK_ACCESS_MSG": "Jira: To prevent shut out from api, access has been blocked by Super Productivity. You probably should check your jira settings!", "BLOCK_ACCESS_UNBLOCK": "Odblokuj"}, "CFG_CMP": {"ALWAYS_ASK": "Always open dialog", "DO_NOT": "Don't transition", "DONE": "Status for completing task", "ENABLE": "Enable Jira integration", "ENABLE_TRANSITIONS": "Enable Transition Handling", "IN_PROGRESS": "Status for starting task", "LOAD_SUGGESTIONS": "Load Suggestions", "MAP_CUSTOM_FIELDS": "Map Custom Fields", "MAP_CUSTOM_FIELDS_INFO": "Unfortunately some of Jira's data is saved under custom fields which are different for every installation. If you want to include this data you need to select the proper custom field for it.", "OPEN": "Status for pausing task", "SELECT_ISSUE_FOR_TRANSITIONS": "Select issue to load available transitions", "STORY_POINTS": "Story Points", "TRANSITION": "Obsługa przejścia"}, "DIALOG_CONFIRM_ASSIGNMENT": {"MSG": "<strong>{{summary}}</strong> is currently assigned to <strong>{{assignee}}</strong>. Do you want to assign it to yourself?", "OK": "<PERSON><PERSON><PERSON><PERSON> to!"}, "DIALOG_INITIAL": {"TITLE": "Ustawenia <PERSON> dla projektu"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "Choose status to assign", "CURRENT_ASSIGNEE": "Current Assignee:", "CURRENT_STATUS": "Current Status:", "TASK_NAME": "Nazwa zadania:", "TITLE": "Jira: Update Status", "UPDATE_STATUS": "Update Status"}, "DIALOG_WORKLOG": {"CHECKBOXES": {"ALL_TIME": "Zawsze używaj całego czasu spędzonego na zadaniu jako domyślnego", "ALL_TIME_MINUS_LOGGED": "Zawsze używaj tylko czasu spędzonego minus czas zarejestrowany jako do<PERSON>lny", "TIME_SPENT_TODAY": "Zawsze używaj tylko czasu spędzonego dzisiaj jako do<PERSON>", "TIME_SPENT_YESTERDAY": "Zawsze używaj tylko czasu spędzonego wczoraj jako do<PERSON>ego"}, "CURRENTLY_LOGGED": "Currently logged time: ", "INVALID_DATE": "The entered value is not a date!", "SAVE_WORKLOG": "Save Worklog", "STARTED": "Started", "SUBMIT_WORKLOG_FOR": "Submit a worklog to Jira for", "TIME_SPENT": "Time Spent", "TIME_SPENT_TOOLTIP": "Do<PERSON>j r<PERSON>", "TITLE": "Jira: Submit Worklog"}, "FORM_ADV": {"AUTO_ADD_BACKLOG_JQL_QUERY": "JQL used for adding tasks automatically to backlog", "IS_ADD_WORKLOG_ON_SUB_TASK_DONE": "Open dialog to submit worklog to jira when sub task is done", "IS_CHECK_TO_RE_ASSIGN_TICKET_ON_TASK_START": "Check if the currently worked on issue is assigned to current user", "IS_WORKLOG_ENABLED": "Open dialog to submit worklog to jira when task is done", "SEARCH_JQL_QUERY": "JQL Query for limiting searching tasks", "WORKLOG_DEFAULT_ALL_TIME": "Wypełnij cały czas spędzony na wykonywaniu zadania", "WORKLOG_DEFAULT_ALL_TIME_MINUS_LOGGED": "Wypełnij cały czas spędzony minus czas zarejestrowany", "WORKLOG_DEFAULT_TIME_MODE": "<PERSON><PERSON><PERSON><PERSON><PERSON> czasu dla okna dialogowego", "WORKLOG_DEFAULT_TODAY": "Wypełnij tylko czas spędzony dzisiaj", "WORKLOG_DEFAULT_YESTERDAY": "Wypełnij tylko czas spędzony wczoraj"}, "FORM_CRED": {"ALLOW_SELF_SIGNED": "Allow self signed certificate", "HOST": "Host (e.g.: http://my-host.de:1234)", "PASSWORD": "Token / Password", "USE_PAT": "Zamiast tego użyj osobistego tokena dostępu (LEGACY)", "USER_NAME": "Email / Username", "WONKY_COOKIE_MODE": "Wonky Cookie Fallback Authentication (desktop app only)"}, "FORM_SECTION": {"ADV_CFG": "Advanced Config", "HELP_ARR": {"H1": "Basic configuration", "H2": "Worklog settings", "H3": "Default transitions", "P1_1": "Please provide a login name (can be found on your profile page) and an <a target=\"_blank\" href=\"https://confluence.atlassian.com/cloud/api-tokens-938839638.html\" target=\"_blank\">API token</a> or password if you can't generate one for some reason. Please not that newer versions of jira sometimes only work with the token. ", "P1_2": "You also need to specify a JQL query which is used for the suggestions to add tasks from <PERSON><PERSON>. If you need help check out this link <a target=\"_blank\" href=\"https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html\" target=\"_blank\">https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html</a>.", "P1_3": "You can also configure, if you want to automatically (e.g. every time you visit the planning view), to add all new tasks specified by a custom JQL query to the backlog.", "P1_4": "Another option is \"Check if current ticket is assigned to current user\". If enabled and you're starting, a check will be made if you're currently assigned to that ticket on <PERSON><PERSON>, if not an Dialog appears in which you can chose to assign the ticket to yourself.", "P2_1": "There are several options to determine when and how you want to submit a worklog. Enabling <em>'Open worklog dialog for adding a worklog to Jira when task is done'</em> opens a dialog to add an worklog every time you mark a Jira Task as done. So keep in mind that worklogs will be added on top of everything tracked so far. So if you mark a task as done for a second time, you might not want to submit the complete worked time for the task again.", "P2_2": "<em>'Open worklog dialog when sub task is done and not for tasks with sub tasks themselves'</em> opens a worklog dialog every time when you mark a sub task of a Jira issue as done. Because you already track your time via the sub tasks, no dialog is opened once you mark the Jira task itself as done.", "P2_3": "<em>'Send updates to worklog automatically without dialog'</em> does what it says. Because marking a task as done several times leads to the whole worked time being tracked twice, this is not recommended.", "P3_1": "Here you can reconfigure your default transitions. Jira enables a wide configuration of transitions usually coming into action as different columns on your Jira agile board we can't make assumptions about where and when to transition your tasks and you need to set it manually."}}, "ISSUE_CONTENT": {"ASSIGNEE": "Prz<PERSON><PERSON>any", "AT": "at", "ATTACHMENTS": "Załączniki", "CHANGED": "zamiana", "COMMENTS": "<PERSON><PERSON><PERSON><PERSON>", "COMPONENTS": "Komponenty", "DESCRIPTION": "Opis", "LIST_OF_CHANGES": "Lista zmian", "MARK_AS_CHECKED": "Zaznacz zmiany jako sprawdzone", "ON": "on", "RELATED": "Powiązane", "STATUS": "Status", "STORY_POINTS": "Story Points", "SUB_TASKS": "Podzadania", "SUMMARY": "Podsumowanie", "WORKLOG": "<PERSON><PERSON> pracy", "WRITE_A_COMMENT": "Napisz komentarz"}, "S": {"ADDED_WORKLOG_FOR": "Jira: Added worklog for {{issueKey}}", "EXTENSION_NOT_LOADED": "Super Productivity Extension not loaded. Reloading the page might help", "INSUFFICIENT_SETTINGS": "Insufficient Settings provided for <PERSON><PERSON>", "INVALID_RESPONSE": "Jira: Odpowied<PERSON> zawierała nieprawidłowe dane", "ISSUE_NO_UPDATE_REQUIRED": "<PERSON><PERSON>: \"{{issueText}}\" already up to date", "MANUAL_UPDATE_ISSUE_SUCCESS": "Jira: Updated data for \"{{issueText}}\"", "MISSING_ISSUE_DATA": "Jira: Tasks with missing issue data found. Reloading.", "NO_AUTO_IMPORT_JQL": "Jira: No search query defined for auto import", "NO_VALID_TRANSITION": "Jira: No valid transition configured", "TIMED_OUT": "Jira: Request timed out", "TRANSITION": "<PERSON><PERSON>: Set issue \"{{issueKey}}\" to \"{{name}}\"", "TRANSITION_SUCCESS": "<PERSON><PERSON>: Set issue {{issue<PERSON><PERSON>}} to <strong>{{chosenTransition}}</strong>", "TRANSITIONS_LOADED": "Jira: Transitions loaded. Use the selects below to assign them", "UNABLE_TO_REASSIGN": "<PERSON><PERSON>: Unable to reassign ticket to yourself, because you didn't specify a username. Please visit the settings."}, "STEPPER": {"CREDENTIALS": "Credentials", "DONE": "You are now done.", "LOGIN_SUCCESS": "Login successful!", "TEST_CREDENTIALS": "Test Credentials", "WELCOME_USER": "Welcome {{user}}!"}}, "MARKDOWN_PASTE": {"CONFIRM_ADD_TO_SUB_TASK_NOTES": "<PERSON><PERSON> w<PERSON> listę markdown do notatek podzadania \"{{parentTaskTitle}}\"?", "CONFIRM_PARENT_TASKS": "<PERSON><PERSON> <strong>{{tasksCount}} nowe zadania</strong> z wklejonej listy markdown?", "CONFIRM_PARENT_TASKS_WITH_SUBS": "<PERSON><PERSON> <strong>{{tasksCount}} nowe zadania i {{subTasksCount}} podzadania</strong> z wklejonej listy markdown?", "CONFIRM_SUB_TASKS": "<PERSON><PERSON> {{tasksCount}} nowe podzadania z wklejonej listy markdown?", "CONFIRM_SUB_TASKS_WITH_PARENT": "<PERSON><PERSON> <strong>{{tasksCount}} nowe podzadania pod \"{{parentTaskTitle}}\"</strong> z wklejonej listy markdown?", "DIALOG_TITLE": "Wykryto wklejoną listę Markdown!"}, "METRIC": {"BANNER": {"CHECK": "I did it!"}, "CMP": {"AVG_BREAKS_PER_DAY": "Avg. breaks per day", "AVG_TASKS_PER_DAY_WORKED": "Avg. tasks per day worked", "AVG_TIME_SPENT_ON_BREAKS": "Avg. time spent on breaks", "AVG_TIME_SPENT_PER_DAY": "Avg. time spent per day", "AVG_TIME_SPENT_PER_TASK": "Avg. time spent per task", "COUNTING_SUBTASKS": "(counting subtasks)", "DAYS_WORKED": "Days worked", "GLOBAL_METRICS": "Global Metrics", "IMPROVEMENT_SELECTION_COUNT": "Number of times an improvement factor was selected", "MOOD_PRODUCTIVITY_OVER_TIME": "Mood and productivity over time", "NO_ADDITIONAL_DATA_YET": "No additional data collected yet. Use the form on the daily summary \"Evaluation\" panel to do so.", "OBSTRUCTION_SELECTION_COUNT": "Number of times an obstructing factor was selected", "SIMPLE_CLICK_COUNTERS_OVER_TIME": "Liczniki kliknięć w czasie", "SIMPLE_COUNTERS": "Proste liczniki", "SIMPLE_STOPWATCH_COUNTERS_OVER_TIME": "<PERSON><PERSON> o<PERSON> czas", "TASKS_DONE_CREATED": "Tasks (done/created)", "TIME_ESTIMATED": "Time Estimated", "TIME_SPENT": "Time Spent"}, "EVAL_FORM": {"ADD_NOTE_FOR_TOMORROW": "Add Note for tomorrow", "DISABLE_REPEAT_EVERY_DAY": "Disable repeat every day", "ENABLE_REPEAT_EVERY_DAY": "Repeat every day", "HELP_H1": "Why should I care?", "HELP_LINK_TXT": "Go to metrics section", "HELP_P1": "Time for a little self evaluation! Your answers here are saved and provide you with a little bit of statistics on how you work in the metrics section. Furthermore the suggestions for tomorrow will appear above your task list the next day.", "HELP_P2": "This is intended to be less about calculating exact metrics or becoming machine like efficient in all you do than it is about improving how you feel about your work. It can be helpful to evaluate pain points in your daily routine, as well as it is to find factors that help you out. Being just a little bit systematic about it hopefully helps to get a better grip on these and to improve what you can.", "IMPROVEMENTS": "What improved your productivity?", "IMPROVEMENTS_TOMORROW": "What could you do to improve tomorrow?", "MOOD": "How do you feel?", "MOOD_HINT": "1: <PERSON>w<PERSON> – 10: <PERSON><PERSON>ndi<PERSON>", "NOTES": "Notes for tomorrow", "OBSTRUCTIONS": "What hindered your productivity?", "PRODUCTIVITY": "How efficient did you work?", "PRODUCTIVITY_HINT": "1: Haven't even started – 10: Enormously efficient"}, "S": {"SAVE_METRIC": "<PERSON><PERSON> successfully saved"}}, "NOTE": {"D_ADD": {"NOTE_LABEL": "Enter some text to save as note..."}, "D_FULLSCREEN": {"VIEW_PARSED": "Wyświetlanie jako <PERSON> (nieedytowalny) markdown", "VIEW_SPLIT": "Wyświetlanie przeanalizowanego i nieprzeanalizowanego markdown w widoku podzielonym", "VIEW_TEXT_ONLY": "Wyświetlanie jako nieprzeanalizowany tekst"}, "NOTE_CMP": {"DISABLE_PARSE": "Disable markdown parsing", "ENABLE_PARSE": "Enable markdown parse"}, "NOTES_CMP": {"ADD_BTN": "Add new Note", "DROP_TO_ADD": "Drop here to add new note", "NO_NOTES": "Obecnie nie ma żadnych notatek"}, "S": {"NOTE_ADDED": "Uwaga zapisana"}}, "OPEN_PROJECT": {"CFG_CMP": {"ALWAYS_ASK": "Zawsze otwarte okno dialogowe", "DO_NOT": "<PERSON><PERSON> zmieniaj <PERSON>ę", "DONE": "Status ukończenia zadania", "ENABLE": "Włącz integrację z Openproject", "ENABLE_TRANSITIONS": "Włącz obsługę przejścia", "IN_PROGRESS": "Status uruchamiania zadania", "OPEN": "Status wstrzymania zadania", "PROGRESS_ON_SAVE": "Domyślny postęp przy zapisie", "SELECT_ISSUE_FOR_TRANSITIONS": "<PERSON><PERSON><PERSON><PERSON>, aby za<PERSON><PERSON><PERSON>ć dostępne przejścia", "TRANSITION": "Zarządzanie przejściami"}, "DIALOG_INITIAL": {"TITLE": "Konfiguracja OpenProject dla projektu"}, "DIALOG_TRACK_TIME": {"ACTIVITY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CURRENTLY_LOGGED": "Aktualnie zarejestrowany czas:", "INVALID_DATE": "Wprowadzona wartość nie jest datą!", "POST_TIME": "Post Time", "STARTED": "Roz<PERSON>czę<PERSON>", "SUBMIT_TIME_FOR": "Prześ<PERSON>j czas do OpenProject dla", "TIME_SPENT": "<PERSON>zas spędzony", "TITLE": "OpenProject: Submit Worklog"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "Wybierz status do przypisania", "CURRENT_ASSIGNEE": "Obecny cesjonariusz:", "CURRENT_STATUS": "Obecny status:", "PERCENTAGE_DONE": "Postęp:", "TASK_NAME": "Nazwa zadania:", "TITLE": "OpenProject: Status aktualizacji", "UPDATE_STATUS": "Status aktualizacji"}, "FORM": {"FILTER_USER": "Nazwa użytkownika filtra", "HOST": "Host (np.: https://www.openproject.org/)", "IS_SHOW_TIME_TRACKING_DIALOG": "Wyświetlanie okna dialogowego śledzenia czasu w celu raportowania do OpenProject", "IS_SHOW_TIME_TRACKING_DIALOG_DESCRIPTION": "Wymaga włączenia modułu śledzenia czasu dla projektu OpenProject.", "IS_SHOW_TIME_TRACKING_DIALOG_FOR_EACH_SUB_TASK": "Wyświetlanie okna dialogowego śledzenia czasu po ukończeniu zadań podrzędnych", "PROJECT_ID": "Id projektu", "PROJECT_ID_DESCRIPTION": "Można znaleźć jako c<PERSON> adresu URL, podczas przeglądania projektu w przeglądarce.", "SCOPE": "<PERSON><PERSON><PERSON>", "SCOPE_ALL": "Wszystkie", "SCOPE_ASSIGNED": "Przypisane do mnie", "SCOPE_CREATED": "Utworzone przeze mnie", "TOKEN": "<PERSON><PERSON> dos<PERSON>"}, "FORM_SECTION": {"HELP": "<p>Tu<PERSON>j moż<PERSON> skonfigurować SuperProductivity do wyświetlania listy otwartych pakietów roboczych OpenProject. Na<PERSON><PERSON><PERSON> p<PERSON>, że aby to działało w przeglądarce, nale<PERSON><PERSON> prawdopodobnie skonfigurować CORS dla serwera OpenProject, aby umożliwić dostęp z app.super-productivity.com</p>", "TITLE": "OpenProject"}, "ISSUE_CONTENT": {"ASSIGNEE": "Osoba <PERSON>", "ATTACHMENTS": "Załączniki", "DESCRIPTION": "Opis", "MARK_AS_CHECKED": "Oznacz aktualizacje jako sprawdzone", "STATUS": "Status", "SUMMARY": "Podsumowanie", "TYPE": "<PERSON><PERSON>", "UPLOAD_ATTACHMENT": "Prześlij do zadania"}, "ISSUE_STRINGS": {"ISSUE_STR": "pakiet roboczy", "ISSUES_STR": "paki<PERSON> robocze"}, "S": {"ERR_NO_FILE": "<PERSON><PERSON> wybrano pliku", "ERR_UNKNOWN": "OpenProject: Nieznany błąd {{statusCode}} {{errorMsg}}. Czy CORS jest poprawnie skonfigurowany dla serwera?", "POST_TIME_SUCCESS": "OpenProject: Pomyślnie utworzono wpis czasu dla {{issueTitle}}", "TRANSITION": "OpenProject: <PERSON><PERSON><PERSON> wydanie \"{{issue<PERSON>ey}}\" na \"{{name}}\"", "TRANSITION_SUCCESS": "OpenProject: <PERSON><PERSON><PERSON> wydanie {{issue<PERSON><PERSON>}} na <strong>{{chosenTransition}}</strong>", "TRANSITIONS_LOADED": "Załadowane przejścia. Użyj poniższych opcji wyboru, aby je przypisać"}}, "PLANNER": {"D": {"ADD_PLANNED": {"ADD_TO_TODAY": "Dodaj do Dzisiaj", "RE_PLAN_ALL": "Zmień termin wszystkiego", "TITLE": "Dodawanie zaplanowanych zadań do Dzisiaj"}}, "EDIT_REPEATED_TASK": "Ed<PERSON><PERSON>j p<PERSON>zające się zadanie '{{taskName}}'", "NO_TASKS": "Brak zadań", "PLAN_VIEW": {"NO_SCHEDULED_ITEMS": "Brak zaplanowanych pozycji"}, "S": {"REMOVED_PLAN_DATE": "<PERSON><PERSON><PERSON>to datę planu dla zadania '{{taskTitle}}'", "TASK_ALREADY_PLANNED": "Zadanie jest już zaplanowane dla {{date}}", "TASK_PLANNED_FOR": "<PERSON><PERSON><PERSON>lanowa<PERSON> na <strong>{{date}}</strong>{{extra}}"}, "TASK_DRAWER": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "POMODORO": {"BACK_TO_WORK": "Wróć do pracy!", "BREAK_IS_DONE": "Twoja przerwa się skończyła!", "ENJOY_YOURSELF": "<PERSON><PERSON><PERSON>b sobie przerwę, por<PERSON><PERSON><PERSON> się trochę, wr<PERSON><PERSON> za:", "FINISH_SESSION_X": "Zakończ<PERSON>ł<PERSON>ś sesję <strong>{{nr}}</strong> z powodzeniem!", "NOTIFICATION": {"BREAK_TIME": "Pomodoro: Czas na przerwę {{nr}}!", "BREAK_X_START": "Pomodoro: Przer<PERSON> {{nr}} się zaczęła!", "NO_TASKS": "Przed uruchomieniem minutnika Pomodoro należy dodać zadania.", "SESSION_X_START": "Pomodoro: <PERSON><PERSON><PERSON> {{nr}} się zaczęła!"}, "S": {"RESET": "Reset do pierwszej sesji Pomodoro", "SESSION_SKIP": "Przejdź do końca bieżącej sesji Pomodoro", "SESSION_X_START": "Pomodoro: <PERSON><PERSON><PERSON> {{nr}} się zaczęła!"}, "SKIP_BREAK": "Pomiń przerwę", "START_BREAK": "Zacznij przerwę"}, "PROCRASTINATION": {"BACK_TO_WORK": "Wróć do pracy!", "COMP": {"INTRO": "People with high procrastination levels usually have low self-compassion. So practice it! It improves your feeling of self-worth, fosters positive emotions and can help you overcome procrastination, of course. Try a little exercise:", "L1": "Sit down for bit and stretch yourself, if you like, calm down a little bit", "L2": "Try to listen to the thoughts and feelings that arise", "L3": "Are you responding to yourself in a way that you would respond to a friend?", "L4": "If the answer is no, imagine your friend in your situation. What you would say to them? What you would do for them?", "OUTRO": "More exercises <a target=\"_blank\" href=\"https://drsoph.com/blog/2018/9/17/3-exercises-in-self-compassion\" target=\"_blank\">can be found here</a> or on <a target=\"_blank\" href=\"https://www.google.com/search?q=self+compassion+exercises&oq=self+compassion+excers&aqs=chrome.1.69i57j0l5.4303j0j7&sourceid=chrome&ie=UTF-8\" target=\"_blank\">google</a>.", "TITLE": "Self Compassion"}, "CUR": {"INTRO": "Procrastination is interesting, isn't it? It doesn't make sense to do it. Not in your long term interest at all. But still everybody does it. Enjoy and explore!", "L1": "What feelings are eliciting your temptation to procrastinate?", "L2": "Where do you feel them in your body?", "L3": "What do they remind you of?", "L4": "What happens to the thought of procrastinating as you observe it? Does it intensify? Dissipate? Cause other emotions to arise?", "L5": "How are the sensations in your body shifting as you continue to rest your awareness on them?", "PROCRASTINATION_TRIGGERS_TEXT": "<PERSON>ą bardzo skuteczną metodą jest zapisywanie tego, co wywołało u ciebie chęć zwlekania. Na przykład ja osobiście często mam ochotę szybko przeskoczyć na reddit lub moją ulubioną stronę z wiadomościami, gdy tylko moje okno przeglądarki znajdzie się w centrum uwagi. Odkąd zacząłem zapisywać swoje wyzwalacze w prostym, pustym dokumencie tekstowym, uświadomiłem sobie, jak zakorzeniony był ten wzorzec i pomogło mi to eksperymentować z różnymi środkami zaradczymi.", "PROCRASTINATION_TRIGGERS_TITLE": "Zapisywanie czynników wywołujących prokrastynację", "TITLE": "Curiosity"}, "H1": "Cut yourself some slack!", "INTRO": {"AVOIDING": "Unikanie zadania", "FEAR": "Strach przed porażką", "STRESSED": "Stres związany z brakiem realizacji zadań", "TITLE": "Wprowadzenie"}, "P1": "First of all relax! Everybody does it once in a while. And if you're not doing what you should, you should at least enjoy it! Then check out the sections below for something helpful.", "P2": "Remember: Procrastination is an emotion regulation problem, not a time management problem.", "REFRAME": {"INTRO": "Think about what might be positive about the task despite its flaws.", "TITLE": "Reframing", "TL1": "What might be interesting about it?", "TL2": "What is to gain if you complete it?", "TL3": "How will you feel about it if you complete it?"}, "SPLIT_UP": {"INTRO": "Split up the task into as many small chunks as you can.", "OUTRO": "Done? Then think about it. What would be – strictly theoretical – the first thing you would do <i>if</i> you were to start working on the task? Just think about it...", "TITLE": "Split it up!"}}, "PROJECT": {"D_CREATE": {"CREATE": "Stwórz projekt", "EDIT": "Edycja projektu", "SETUP_CALDAV": "Setup Caldav Integration", "SETUP_GIT": "Setup GitHub Integration", "SETUP_GITEA_PROJECT": "Konfiguracja integracji Gitea", "SETUP_GITLAB": "Setup GitLab Integration", "SETUP_JIRA": "Setup Jira Integration", "SETUP_OPEN_PROJECT": "Konfiguracja integracji z OpenProject", "SETUP_REDMINE_PROJECT": "Konfiguracja integracji z Redmine"}, "D_DELETE": {"MSG": "<PERSON>zy na pewno chcesz usunąć projekt \"{{title}}\"?"}, "FORM_BASIC": {"L_ENABLE_BACKLOG": "Włączanie rejestru projektów", "L_IS_HIDDEN_FROM_MENU": "Ukryj projekt w menu", "L_TITLE": "Nazwa projektu", "TITLE": "Podstawowe ustawienia"}, "FORM_THEME": {"D_IS_DARK_THEME": "Won`t be used if system supports global dark mode.", "HELP": "Theme settings for your project.", "L_BACKGROUND_IMAGE_DARK": "Background Image Url (Dark Theme)", "L_BACKGROUND_IMAGE_LIGHT": "Background Image Url (Light Theme)", "L_COLOR_ACCENT": "Accent Color", "L_COLOR_PRIMARY": "Primary Color", "L_COLOR_WARN": "Warn/Error Color", "L_HUE_ACCENT": "<PERSON><PERSON><PERSON><PERSON> for dark text on accent color background", "L_HUE_PRIMARY": "<PERSON><PERSON><PERSON><PERSON> for dark text on primary color background", "L_HUE_WARN": "T<PERSON><PERSON><PERSON> for dark text on warn color background", "L_IS_AUTO_CONTRAST": "Auto set text colors for best readability", "L_IS_DISABLE_BACKGROUND_GRADIENT": "Disable colored background gradient", "L_IS_REDUCED_THEME": "Use reduced UI (no boxes around tasks)", "L_THEME_COLOR": "Theme Color", "L_TITLE": "Title", "TITLE": "Theme"}, "S": {"ARCHIVED": "Archived Project", "CREATED": "Created project <strong>{{title}}</strong>. You can select it from the menu on the top left.", "DELETED": "Deleted Project", "E_EXISTS": "Project \"{{title}}\" already exists", "E_INVALID_FILE": "Invalid data for project file", "ISSUE_PROVIDER_UPDATED": "Updated project settings for <strong>{{issue<PERSON>roviderKey}}</strong>", "UNARCHIVED": "Unarchived project", "UPDATED": "Updated project settings"}}, "QUICK_HISTORY": {"NO_DATA": "Brak danych za bieżący rok", "PAGE_TITLE": "Krótka historia", "WEEK_TITLE": "<PERSON><PERSON>zień {{nr}} ({{timeSpent}})"}, "REDMINE": {"DIALOG_INITIAL": {"TITLE": "Konfiguracja Redmine dla projektu"}, "FORM": {"API_KEY": "Klucz dostępu API", "HOST": "Host (np.: https://redmine.org)", "PROJECT_ID": "Identyfikator projektu", "PROJECT_ID_DESCRIPTION": "Można znaleźć jako c<PERSON> adresu URL, podczas przeglądania projektu w przeglądarce.", "SCOPE": "<PERSON><PERSON><PERSON>", "SCOPE_ALL": "Wszystkie", "SCOPE_ASSIGNED": "Przypisane do mnie", "SCOPE_CREATED": "Utworzone przeze mnie"}, "FORM_SECTION": {"HELP": "<p>W tym miejscu można skonfigurować SuperProductivity do wyświetlania listy otwartych zgłoszeń Redmine (w wersji online lub w instancji hostowanej samodzielnie) dla określonego projektu w panelu tworzenia zadań w widoku planowania dziennego. Będą one wymienione jako sugestie i będą zawierać link do zgłoszenia, a także więcej informacji na jego temat.</p><p>Ponadto można automatycznie importować wszystkie otwarte sprawy.</p>", "TITLE": "Redmine"}, "ISSUE_CONTENT": {"AUTHOR": "Autor", "DESCRIPTION": "Opis", "MARK_AS_CHECKED": "Oznacz aktualizacje jako sprawdzone", "PRIORITY": "Priorytet", "STATUS": "Status"}, "S": {"ERR_UNKNOWN": "Redmine: <PERSON><PERSON><PERSON><PERSON> bł<PERSON>d {{statusCode}} {{errorMsg}}."}}, "REMINDER": {"COUNTDOWN_BANNER": {"HIDE": "<PERSON><PERSON><PERSON><PERSON>", "START_NOW": "Zacznij teraz", "TXT": "<strong>{{title}}<\\/strong> z<PERSON><PERSON>na si<PERSON> o <strong>{{start}}<\\/strong>!", "TXT_MULTIPLE": "<strong>{{title}}</strong> zac<PERSON>na się od <strong>{{start}}</strong>!<br> (i {{nrOfOtherBanners}} inne zadania są wymagalne)"}, "S_ACTIVE_TASK_DUE": "<PERSON><PERSON><PERSON>, nad którym obecnie pracujesz, jest już gotowe!<br/> ({{title}})", "S_REMINDER_ERR": "Error for reminder interface"}, "SCHEDULE": {"CONTINUED": "continued", "D_INITIAL": {"TEXT": "<p>The idea of the Schedule is to provide a better picture of how one's planned tasks play out over time. It is automatically generated from your Tasks and distinguishes two different things: <strong>Scheduled Tasks</strong>, which are shown at their planned time and <strong>Regular Tasks</strong> which should flow around those fixed events. All tasks consider the time estimates you have assigned to them.</p><p>In addition to this you can also provide a work start and end time. If configured regular tasks will never show up outside of these. Please note that the schedule only includes the upcoming 30 days.</p>", "TITLE": "Schedule"}, "END": "Work End", "LUNCH_BREAK": "Przerwa na lunch", "NO_TASKS": "Currently there are no tasks. Please add some tasks via the + Button in the top bar.", "NOW": "Now", "PLAN_END_DAY": "Plan na koniec dnia", "PLAN_START_DAY": "Plan na początek dnia", "START": "Work Start", "TASK_PROJECTION_INFO": "Przyszła projekcja zaplanowanego powtarzalnego zadania"}, "SEARCH_BAR": {"INFO": "Click on the list icon to search for archived tasks", "INFO_ARCHIVED": "Click on the archive icon to search for normal tasks", "NO_RESULTS": "Nie znaleziono zadań pasujących do twojego wyszukiwania", "PLACEHOLDER": "Search for task or task description", "PLACEHOLDER_ARCHIVED": "Search for archived tasks", "TOO_MANY_RESULTS": "Too many results, please narrow your search"}, "SIMPLE_COUNTER": {"D_CONFIRM_REMOVE": {"MSG": "Deleting a simple counter, will also delete all past data tracked on it. Are you sure you want to proceed?", "OK": "Do it!"}, "D_EDIT": {"CURRENT_STREAK": "Aktualna passa", "DAILY_GOAL": "<PERSON>l dzienny", "DAYS": "Dni", "L_COUNTER": "Count"}, "FORM": {"ADD_NEW": "Add simple counter", "HELP": "Here you can configure simple buttons which will appear at the top right. They can either be timers or just a simple counter, which is counted up, by clicking on it.", "L_COUNTDOWN_DURATION": "Czas trwania odliczania", "L_DAILY_GOAL": "Codzienny cel dla udanej passy", "L_ICON": "Icon", "L_ICON_ON": "Icon when toggled", "L_IS_ENABLED": "Enabled", "L_TITLE": "Title", "L_TRACK_STREAKS": "Track Streaks", "L_TYPE": "Type", "L_WEEKDAYS": "<PERSON><PERSON> p<PERSON>, aby sprawd<PERSON><PERSON> smugę", "TITLE": "Simple Counters", "TYPE_CLICK_COUNTER": "Click Counter", "TYPE_REPEATED_COUNTDOWN": "Powtarzane odliczanie", "TYPE_STOPWATCH": "Stopwatch"}, "S": {"GOAL_REACHED_1": "Osiągnąłeś swój dzisiejszy cel!", "GOAL_REACHED_2": "Aktualny czas trwania passy:"}}, "SYNC": {"A": {"ARCHIVE_ONLY_UPLOADED": "Twoje dane zostały przesłane tylko częściowo. Spróbuj ponownie później! W przeciwnym razie nie będzie można zsynchronizować danych z innymi urządzeniami.", "POSSIBLE_LEGACY_DATA": "Super Productivity usprawniło synchronizację, używając teraz dwóch oddzielnych plików zamiast jednego, co pozwala na znacznie mniejszy transfer danych. Zaleca się aktualizację wszystkich instancji Super Productivity i najpierw synchronizację danych z instancji aplikacji, w której dane są najnowsze. <PERSON><PERSON><PERSON> są to dane z urządzenia lokalnego, zignoruj to ostrzeżenie i po prostu prześlij je, potwierdzając następne okno dialogowe.", "REMOTE_MODEL_VERSION_NEWER": "Wersja modelu zdalnego jest nowsza niż lokalna. Zaktualizuj swoją lokalną aplikację do najnowszej wersji!"}, "C": {"EMPTY_SYNC": "You're trying to sync an empty data object. If you are trying to setup syncing from a new app instance, just press OK to load the data from the server. Otherwise, please check your data.", "FORCE_UPLOAD": "Upload local data anyway?", "FORCE_UPLOAD_AFTER_ERROR": "An Error occurred while uploading your local data. Try to force the update?", "MIGRATE_LEGACY": "<PERSON>ykryto starsze dane podczas <PERSON>owania, czy ch<PERSON>z spróbować je zmigrować?", "NO_REMOTE_DATA": "No remote data found. Upload local to Remote?", "TRY_LOAD_REMOTE_AGAIN": "Try to re-load data from remote once again?", "UNABLE_TO_LOAD_REMOTE_DATA": "Nie można załadować danych ze zdalnego. <PERSON><PERSON> ch<PERSON>z spróbować nadpisać zdalne dane danymi lokalnymi? Wszystkie zdalne dane zostaną utracone w tym procesie."}, "D_AUTH_CODE": {"FOLLOW_LINK": "Please open the following link and copy the auth code provided there into the the input field below.", "GET_AUTH_CODE": "Get Authorization Code", "L_AUTH_CODE": "Enter Auth Code", "TITLE": "Login: {{provider}}"}, "D_CONFLICT": {"LAMPORT_CLOCK": "<PERSON><PERSON><PERSON><PERSON>", "LAST_CHANGE": "Last Change:", "LAST_SYNC": "Last Sync:", "LOCAL": "Local", "LOCAL_REMOTE": "Local -> Remote", "REMOTE": "Remote", "TEXT": "<p>Update from remote. Both local and remote data seem to be modified.</p>", "TIMESTAMP": "Znacznik czasu", "TITLE": "Sync: Conflicting Data", "USE_LOCAL": "Use local", "USE_REMOTE": "Use remote"}, "D_DECRYPT_ERROR": {"BTN_OVER_WRITE_REMOTE": "Zmienianie i nadpisywanie zdalne", "CHANGE_PW_AND_DECRYPT": "Zmień i spróbuj odszyfrować", "P1": "Twoje dane są zaszyf<PERSON>, a odszyfrowanie nie powiodło się. Podaj poprawne hasło!", "P2": "Możesz także zmienić hasło, które zastąpi wszystkie zdalne dane.", "PASSWORD": "<PERSON><PERSON><PERSON>"}, "D_INCOMPLETE_SYNC": {"BTN_CLOSE_APP": "Zamknij <PERSON>", "BTN_DOWNLOAD_BACKUP": "Pobierz lokalną kopię zapasową", "BTN_FORCE_UPLOAD": "Wymuś przesyłanie lokalne", "P1": "Dane zdalnej synchronizacji są niespójne!", "P2": "Model, które<PERSON> problem:", "P3": "Masz 2 opcje:", "P4": "1. Przejdź do drugiego urządzenia i spróbuj przeprowadzić tam pełną synchronizację.", "P5": "2. <PERSON>ast<PERSON><PERSON> dane zdalne danymi lokalnymi. Wszystkie zdalne zmiany zostaną utracone!", "P6": "Zaleca się utworzenie kopii zapasowej nadpisanych danych!!", "T1": "Ostatnia synchronizacja była niekompletna!", "T2": "Dane archiwum nie zostały poprawnie przesłane podczas ostatniej synchronizacji:", "T3": "Dostępne są 2 opcje:", "T4": "1. Przejdź do drugiego urządzenia i dokończ synchronizację.", "T5": "2. <PERSON><PERSON><PERSON> też nadpisać dane zdalne danymi lokalnymi. Wszystkie zdalne zmiany\n    zostaną utracone!", "T6": "Zaleca się utworzenie kopii zapasowej nadpisywanych danych!!!"}, "D_INITIAL_CFG": {"SAVE_AND_ENABLE": "Zapisz i Włącz synchronizację", "TITLE": "Skonfiguruj synchronizację"}, "D_PERMISSION": {"DISABLE_SYNC": "Wyłącz synchronizację", "PERM_FILE": "Zezwolenie", "TEXT": "<p>Uprawnienia do lokalnej synchronizacji plików zostały cofnięte.</p>", "TITLE": "Synchronizacja: Odmowa uprawnień do pliku lokalnego"}, "FORM": {"DROPBOX": {"L_ACCESS_TOKEN": "Access Token (generated from Auth Code)"}, "GOOGLE": {"L_SYNC_FILE_NAME": "Sync File Name"}, "L_ENABLE_COMPRESSION": "Włącz kompresję (szybszy transfer danych)", "L_ENABLE_ENCRYPTION": "Włączenie szyfrowania end-to-end (eksperymentalne) - uniemożliwienie dostępu do danych dostawcy synchronizacji.", "L_ENABLE_SYNCING": "Enable Syncing", "L_ENCRYPTION_NOTES": "WAŻNE UWAGI: <PERSON><PERSON> w<PERSON><PERSON><PERSON>, na<PERSON><PERSON><PERSON> to samo hasło na innych urządzeniach <strong>PRZED</strong> następną synchronizacją. <PERSON><PERSON><PERSON><PERSON> be<PERSON>pie<PERSON> hasło. Na<PERSON><PERSON><PERSON> rów<PERSON>, że <strong>NIE będzie można uzyskać dostępu do danych, jeśli zapomni się tego hasła. Nie ma możliwości odzyskania</strong>ponieważ tylko ty posiadasz klucz. Szyfrowanie będzie prawdopodobnie wystarczająco dobre, aby udar<PERSON><PERSON><PERSON> wi<PERSON><PERSON><PERSON><PERSON><PERSON> ataków, ale <strong>nie ma gwarancji.</strong>", "L_ENCRYPTION_PASSWORD": "<PERSON><PERSON><PERSON> (NIE ZAPOMNIJ)", "L_SYNC_INTERVAL": "Sync Interval", "L_SYNC_PROVIDER": "Sync Provider", "LOCAL_FILE": {"L_SYNC_FILE_PATH_PERMISSION_VALIDATION": "Wymaga uprawnień dostępu do plików", "L_SYNC_FOLDER_PATH": "Ścieżka folderu synchronizacji"}, "TITLE": "Sync", "WEB_DAV": {"CORS_INFO": "<strong>Making it work on MOBILE and in the browser:</strong> To make this work for mobile or the browser you need to whitelist Super Productivity for CORS requests for your Nextcloud instance. This can have negative security implications! Please <a href='https://github.com/nextcloud/server/issues/3131'>refer to this thread for more information</a>. One approach to make this work on mobile is whitelisting \"https://app.super-productivity.com\" via the nextcloud app <a href='https://apps.nextcloud.com/apps/webapppassword'>webapppassword<a>. Use at your own risk!</p>", "L_BASE_URL": "Base Url", "L_PASSWORD": "Password", "L_SYNC_FOLDER_PATH": "Ścieżka folderu synchronizacji", "L_USER_NAME": "Username"}}, "S": {"ALREADY_IN_SYNC": "<PERSON><PERSON> zsynchronizowane", "ALREADY_IN_SYNC_NO_LOCAL_CHANGES": "Brak zmian lokalnych - już zsynchronizowane", "BTN_CONFIGURE": "Konfiguruj", "BTN_FORCE_OVERWRITE": "Wymuś nadpisanie", "ERROR_DATA_IS_CURRENTLY_WRITTEN": "<PERSON> z<PERSON> są obecnie zapisywane", "ERROR_FALLBACK_TO_BACKUP": "Something went wrong while importing the data. Falling back to local backup.", "ERROR_INVALID_DATA": "Error while syncing. Invalid data", "ERROR_NO_REV": "Brak prawidłowego rev dla zdalnego pliku", "ERROR_UNABLE_TO_READ_REMOTE_DATA": "Błąd podczas synchronizacji. Nie można odczytać danych zdalnych. <PERSON><PERSON> może włączono szyfrowanie, a hasło lokalne nie jest zgodne z hasłem używanym do szyfrowania danych zdalnych?", "IMPORTING": "Importing data", "INCOMPLETE_CFG": "Authentication for sync failed. Please check your Config!", "INITIAL_SYNC_ERROR": "Initial Sync failed", "SUCCESS_DOWNLOAD": "Zsynchronizowane dane ze zdalnej", "SUCCESS_IMPORT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane", "SUCCESS_VIA_BUTTON": "Dane zostały pomyślnie zsynchronizowane", "UNKNOWN_ERROR": "Unknown Sync Error: {{err}}", "UPLOAD_ERROR": "Unknown Upload Error (Settings correct?): {{err}}"}}, "TAG": {"D_CREATE": {"CREATE": "Create Tag", "EDIT": "Edit Tag"}, "D_DELETE": {"CONFIRM_MSG": "Do you really want to delete the tag \"{{tagName}}\"? It will be removed from all tasks. This cannot be undone."}, "D_EDIT": {"ADD": "Add tags for \"{{title}}\"", "EDIT": "Edit tags for \"{{title}}\"", "LABEL": "Tags"}, "FORM_BASIC": {"L_COLOR": "Color (if undefined primary theme color is used)", "L_ICON": "Icon", "L_TITLE": "Tag Name", "TITLE": "Basic Settings"}, "S": {"UPDATED": "Tag Settings were updated"}, "TTL": {"ADD_NEW_TAG": "<PERSON><PERSON><PERSON> nowy tag"}}, "TASK": {"ADD_TASK_BAR": {"ADD_EXISTING_TASK": "Add existing task \"{{taskTitle}}\"", "ADD_ISSUE_TASK": "Add issue #{{issueNr}} from {{issueType}}", "ADD_TASK_TO_BOTTOM_OF_BACKLOG": "Dodaj zadanie do dolnej części zaległości", "ADD_TASK_TO_BOTTOM_OF_TODAY": "Dodaj zadanie do dolnej części listy", "ADD_TASK_TO_TOP_OF_BACKLOG": "Dodanie zadania na początku listy zaległości", "ADD_TASK_TO_TOP_OF_TODAY": "Dodanie zadania na początek listy", "CREATE_TASK": "Create new task", "EXAMPLE": "Example: \"Some task title +projectName #some-tag #some-other-tag 10m/3h\"", "START": "Press enter one more time to start", "TOGGLE_ADD_TO_BACKLOG_TODAY": "Przełączanie dodawania zadania do zaległości / dzi<PERSON><PERSON><PERSON><PERSON> listy", "TOGGLE_ADD_TOP_OR_BOTTOM": "Przełączanie dodawania zadania na górę i dół listy"}, "ADDITIONAL_INFO": {"ADD_ATTACHMENT": "Add attachment", "ADD_SUB_TASK": "Add Sub Task", "ATTACHMENTS": "Attachments ({{nr}})", "DUE": "Zaplanowane na", "FROM_PARENT": "(from Parent)", "LOCAL_ATTACHMENTS": "Local Attachments", "NOTES": "Description", "PARENT": "Parent", "REMINDER": "Reminder", "REPEAT": "Repeat", "SCHEDULE_TASK": "Schedule Task", "SUB_TASKS": "Sub Tasks ({{nr}})", "TIME": "Time", "TITLE_PLACEHOLDER": "Wprowadź tytuł"}, "B": {"ADD_HALF_HOUR": "Add 1/2 hour", "ESTIMATE_EXCEEDED": "Time estimate exceeded for  \"{{title}}\""}, "CMP": {"ADD_SUB_TASK": "Add sub task", "ADD_TO_MY_DAY": "Add to My Day", "ADD_TO_PROJECT": "Add to a Project", "CONVERT_TO_PARENT_TASK": "Convert to parent Task", "DELETE": "Delete Task", "DELETE_REPEAT_INSTANCE": "Usuwanie powtarzających się instancji zadań", "DROP_ATTACHMENT": "Drop here to attach to \"{{title}}\"", "EDIT_SCHEDULED": "Edit reminder", "EDIT_TAGS": "Edit tags", "EDIT_TASK_TITLE": "<PERSON><PERSON><PERSON><PERSON> tytuł", "FOCUS_SESSION": "Rozpocznij sesję skupienia", "MARK_DONE": "Mark as done", "MARK_UNDONE": "Mark as undone", "MOVE_TO_BACKLOG": "Move to backlog", "MOVE_TO_OTHER_PROJECT": "Move to other Project", "MOVE_TO_REGULAR": "Move to today's list", "MOVE_TO_TOP": "Przejście na górę listy", "OPEN_ATTACH": "Attach file or link", "OPEN_ISSUE": "Open in browser", "OPEN_TIME": "Time Tracking", "REMOVE_FROM_MY_DAY": "Remove from My Day", "REPEAT_EDIT": "Edit repeat task config", "SCHEDULE": "Schedule task", "SHOW_UPDATES": "Show updates", "TOGGLE_ATTACHMENTS": "Show/Hide attachments", "TOGGLE_DETAIL_PANEL": "Show/Hide additional info", "TOGGLE_DONE": "Mark as done/undone", "TOGGLE_SUB_TASK_VISIBILITY": "Toggle sub task visibility", "TOGGLE_TAGS": "Przełącz tagi", "TRACK_TIME": "Start tracking time", "TRACK_TIME_STOP": "Pause tracking time", "UNSCHEDULE_TASK": "Anuluj harmonogram zadania", "UPDATE_ISSUE_DATA": "Update issue data"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAG": {"MSG": "<PERSON><PERSON> ch<PERSON>z utworzyć nowy tag {{tagsTxt}}?", "OK": "Utwórz tag"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAGS": {"MSG": "<PERSON><PERSON> ch<PERSON>z utworzyć nowe tagi {{tagsTxt}}?", "OK": "Tworzenie tagów"}, "D_REMINDER_VIEW": {"ADD_ALL_TO_TODAY": "Dodaj wszystkie do dzisiaj", "ADD_TO_TODAY": "Add to today", "DONE": "<PERSON><PERSON><PERSON>", "DUE_TASK": "Due task", "DUE_TASKS": "Due tasks", "FOR_CURRENT": "Task is due. Do you want to start working on it?", "FOR_OTHER": "Task is due. Do you want to start working on it?", "FROM_PROJECT": "From Project: \"{{title}}\"", "FROM_TAG": "From Tag: \"{{title}}\"", "RESCHEDULE_EDIT": "<PERSON><PERSON><PERSON><PERSON> (Zmień harmonogram)", "RESCHEDULE_UNTIL_TOMORROW": "Re-schedule for tomorrow", "SNOOZE": "Snooze", "SNOOZE_ALL": "Snooze all", "START": "Start", "SWITCH_CONTEXT_START": "Switch Context & Start", "UNSCHEDULE": "Odwołaj harmonogram", "UNSCHEDULE_ALL": "Anuluj harmonogram wszystkich"}, "D_SCHEDULE_TASK": {"MOVE_TO_BACKLOG": "Move task to backlog until scheduled", "QA_NEXT_MONTH": "Harmonogram na następny miesiąc", "QA_NEXT_WEEK": "Harmonogram na następny tydzień", "QA_REMOVE_TODAY": "Usuń zadanie z dnia dzisiejszego", "QA_TODAY": "Harmonogram na dziś", "QA_TOMORROW": "Harmonogram na jutro", "REMIND_AT": "Remind at", "RO_1H": "1 hour before it starts", "RO_5M": "5 minutes before it starts", "RO_10M": "10 minutes before it starts", "RO_15M": "15 minutes before it starts", "RO_30M": "30 minutes before it starts", "RO_NEVER": "Never", "RO_START": "when it starts", "SCHEDULE": "Schedule", "UNSCHEDULE": "Unschedule"}, "D_TIME": {"ADD_FOR_OTHER_DAY": "Add time spent for other day", "DELETE_FOR": "Delete entry for day", "ESTIMATE": "Estimate", "TIME_SPENT": "Time Spent", "TIME_SPENT_ON": "Time Spent {{date}}", "TITLE": "Time spent / Estimates"}, "D_TIME_FOR_DAY": {"ADD_ENTRY_FOR": "Add new entry for {{date}}", "DATE": "Date for new entry", "HELP": "Examples:<br> 30m => 30 minutes<br> 2h => 2 hours<br> 2h 30m => 2 hours and 30 minutes", "TINE_SPENT": "Time Spent", "TITLE": "Add for Day"}, "N": {"ESTIMATE_EXCEEDED": "Time estimate exceeded!", "ESTIMATE_EXCEEDED_BODY": "You exceeded your estimated time for \"{{title}}\"."}, "S": {"CANNOT_ASSIGN_PROJECT_FOR_REPEATABLE_TASK": "Nie można przypisać projektu za pomocą krótkiej składni dla powtarzalnych zadań!", "CREATED_FOR_PROJECT": "Prz<PERSON>esi<PERSON>nie \"{{taskTitle}}\" do projektu \"{{projectTitle}}\"", "CREATED_FOR_PROJECT_ACTION": "Przejdź do projektu", "DELETED": "Deleted task \"{{title}}\"", "FOUND_MOVE_FROM_BACKLOG": "Moved task <strong>{{title}}</strong> from backlog to todays task list", "FOUND_MOVE_FROM_OTHER_LIST": "Added task <strong>{{title}}</strong> from <strong>{{contextTitle}}</strong> to current list", "FOUND_RESTORE_FROM_ARCHIVE": "Restored task <strong>{{title}}</strong> related to issue from archive", "LAST_TAG_DELETION_WARNING": "You're trying to remove the last tag of a non project task. This is not allowed!", "MOVED_TO_ARCHIVE": "Przeniesiono zadania {{nr}} do archiwum", "MOVED_TO_PROJECT": "Prz<PERSON>esi<PERSON>nie \"{{taskTitle}}\" do projektu \"{{projectTitle}}\"", "MOVED_TO_PROJECT_ACTION": "Przejdź do projektu", "REMINDER_ADDED": "Scheduled task \"{{title}}\"", "REMINDER_DELETED": "Deleted reminder for task", "REMINDER_UPDATED": "Updated reminder for task \"{{title}}\"", "TASK_CREATED": "Created task \"{{title}}\""}, "SELECT_OR_CREATE": "Select or create task", "SUMMARY_TABLE": {"ESTIMATE": "Estimate", "SPENT_TODAY": "Spent Today", "SPENT_TOTAL": "Spent Total", "TASK": "Task", "TOGGLE_DONE": "un-/mark as done"}}, "TASK_REPEAT": {"ADD_INFO_PANEL": {"CUSTOM": "Niestandardowa konfiguracja powtarzania", "CUSTOM_AND_TIME": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {{timeStr}}", "CUSTOM_WEEKLY": "{{daysStr}}", "CUSTOM_WEEKLY_AND_TIME": "{{daysStr}}, {{timeStr}}", "DAILY": "Każdego dnia", "DAILY_AND_TIME": "<PERSON><PERSON><PERSON><PERSON>, {{timeStr}}", "EVERY_X_DAILY": "Co {{x}} dni", "EVERY_X_DAILY_AND_TIME": "Co {{x}} dni, {{timeStr}}", "EVERY_X_MONTHLY": "Co {{x}} <PERSON><PERSON><PERSON>cy", "EVERY_X_MONTHLY_AND_TIME": "Co {{x}} mi<PERSON><PERSON><PERSON>, {{timeStr}}", "EVERY_X_YEARLY": "Co {{x}} lat", "EVERY_X_YEARLY_AND_TIME": "Co {{x}} lat, {{timeStr}}", "MONDAY_TO_FRIDAY": "Od poniedziałku do piątku", "MONDAY_TO_FRIDAY_AND_TIME": "Poniedziałek-p<PERSON><PERSON><PERSON>k, {{timeStr}}", "MONTHLY_CURRENT_DATE": "<PERSON><PERSON><PERSON><PERSON>nie {{dateDayStr}}dnia", "MONTHLY_CURRENT_DATE_AND_TIME": "<PERSON><PERSON><PERSON><PERSON><PERSON> w dniu {{dateDayStr}}th, {{timeStr}}", "WEEKLY_CURRENT_WEEKDAY": "Co tydzień na {{weekdayStr}}", "WEEKLY_CURRENT_WEEKDAY_AND_TIME": "Tygodniowo na {{weekdayStr}}, {{timeStr}}", "YEARLY_CURRENT_DATE": "Rocznie na {{dayAndMonthStr}}", "YEARLY_CURRENT_DATE_AND_TIME": "R<PERSON><PERSON>nie {{dayAndMonthStr}}, {{timeStr}}"}, "D_CONFIRM_MOVE_TO_PROJECT": {"MSG": "<PERSON>la tego powtarzalnego zadania utworzono {{tasksNr}} instancji. <PERSON><PERSON> ch<PERSON>z przeni<PERSON> je wszystkie do projektu \"{{projectName}}\"?", "OK": "Zaktualizuj wszystkie instancje"}, "D_CONFIRM_REMOVE": {"MSG": "Removing the repeat config will convert all previous instances of this task to just regular tasks. Are you sure you want to proceed", "OK": "Remove completely"}, "D_CONFIRM_UPDATE_INSTANCES": {"CANCEL": "Tylko przyszłe zadania", "MSG": "<PERSON>la tego powtarzalnego zadania utworzono {{tasksNr}} instancji. <PERSON><PERSON> ch<PERSON>z zaktualizować wszystkie z nich o nowe ustawienia domyślne, czy tylko przyszłe zadania?", "OK": "Zaktualizuj wszystkie instancje"}, "D_EDIT": {"ADD": "Add Repeat Task Config", "ADVANCED_CFG": "<PERSON><PERSON>wanso<PERSON> konfiguracja", "EDIT": "Edit Repeat Task Config", "HELP1": "Repeating tasks are meant for daily chores, e.g.: \"Organization\", \"Daily Meeting\", \"Code Review\", \"Checking emails\" or similar tasks which are likely to occur again and again.", "HELP2": "Once configured a repeating task will be recreated on every day selected below as soon as you open your project and will be automatically marked as completed at the end of the day. They will be handled as different instances. So you can freely add sub tasks etc.", "HELP3": "Tasks imported from Jira or Git Issues cannot be repeated. All reminders will also be deleted on a repeating task.", "HELP4": "Uwaga dotycząca pola zamówienia: Odnosi się do kolejności tworzenia zadań powtarzalnych. Działa tylko w przypadku zadań powtarzalnych, które są tworzone w tym samym czasie. <PERSON><PERSON><PERSON><PERSON> warto<PERSON> oz<PERSON>, że zadanie będzie znajdować się wyżej na liście, a wyższa, że będzie znajdować się niżej. Wartość większa niż 0 oznacza, że elementy są tworzone na dole normalnych zadań.", "TAG_LABEL": "Tags to add"}, "F": {"C_DAY": "Dzień", "C_MONTH": "<PERSON><PERSON><PERSON><PERSON>", "C_WEEK": "Tydzień", "C_YEAR": "Rok", "DEFAULT_ESTIMATE": "Default Estimate", "FRIDAY": "Friday", "IS_ADD_TO_BOTTOM": "Move task to bottom of list", "MONDAY": "Monday", "NOTES": "Domy<PERSON>lne noty", "ORDER": "Zamówienie", "ORDER_DESCRIPTION": "Kolejność tworzenia zadań powtarzalnych. Wpływa tylko na zadania powtarzalne utworzone w tym samym czasie. Niż<PERSON>a wartość oznacza, że zadanie zostanie utworzone wyżej na liście, a wyższa liczba, że zostanie utworzone niżej. Wartość większa niż 0 oznacza, że elementy będą tworzone na dole normalnych zadań.", "Q_CUSTOM": "Niestandardowa konfiguracja powtórzeń", "Q_DAILY": "Codziennie", "Q_MONDAY_TO_FRIDAY": "Od poniedziałku do piątku", "Q_MONTHLY_CURRENT_DATE": "Ka<PERSON><PERSON><PERSON> w dniu {{dateDayStr}}th", "Q_WEEKLY_CURRENT_WEEKDAY": "Co tydzień w dniu {{weekdayStr}}", "Q_YEARLY_CURRENT_DATE": "Każdego roku w {{dayAndMonthStr}}", "QUICK_SETTING": "Powtórz kon<PERSON>", "REMIND_AT": "Przypomnienie na stronie", "REMIND_AT_PLACEHOLDER": "W<PERSON>bierz, kiedy przypomnieć", "REPEAT_CYCLE": "Powtar<PERSON><PERSON> c<PERSON>", "REPEAT_EVERY": "Powtarzać co", "SATURDAY": "Saturday", "START_DATE": "Data rozpoczęcia", "START_TIME": "Zaplanowany czas rozpoczęcia", "START_TIME_DESCRIPTION": "Np. 15:00. Pozostaw puste dla zadania całodniowego", "SUNDAY": "Sunday", "THURSDAY": "Thursday", "TITLE": "Title for task", "TUESDAY": "Tuesday", "WEDNESDAY": "Wednesday"}}, "TASK_VIEW": {"CUSTOMIZER": {"ENTER_PROJECT": "Wprowadź projekt", "ENTER_TAG": "Wprowadź tag", "ESTIMATED_TIME": "Szacowany czas", "FILTER_BY": "<PERSON><PERSON><PERSON><PERSON>", "FILTER_DEFAULT": "Brak filtra", "FILTER_ESTIMATED_TIME": "Szacowany czas", "FILTER_PROJECT": "Projekt", "FILTER_SCHEDULED_DATE": "Data zaplanowana", "FILTER_TAG": "Tag", "FILTER_TIME_SPENT": "<PERSON>zas spędzony", "GROUP_BY": "<PERSON><PERSON><PERSON><PERSON>", "GROUP_DEFAULT": "Brak grupy", "GROUP_PROJECT": "Projekt", "GROUP_SCHEDULED_DATE": "Data zaplanowana", "GROUP_TAG": "Tag", "RESET_ALL": "Zresetuj wszystko", "SCHEDULED_DEFAULT": "Dowolna data", "SCHEDULED_NEXT_MONTH": "Następny miesiąc", "SCHEDULED_NEXT_WEEK": "Następny tydzień", "SCHEDULED_THIS_MONTH": "<PERSON> mi<PERSON>", "SCHEDULED_THIS_WEEK": "W tym tygodniu", "SCHEDULED_TODAY": "<PERSON><PERSON><PERSON><PERSON>", "SCHEDULED_TOMORROW": "<PERSON><PERSON>", "SORT_BY": "<PERSON><PERSON><PERSON><PERSON>", "SORT_CREATION_DATE": "Data utworzenia", "SORT_DEFAULT": "Domyślny", "SORT_NAME": "Nazwa", "SORT_SCHEDULED_DATE": "Data zaplanowana", "TIME_1HOUR": "> 1 godzina", "TIME_2HOUR": "> 2 godziny", "TIME_10MIN": "> 10 minut", "TIME_30MIN": "> 30 minut", "TIME_DEFAULT": "Dowolny czas trwania", "TIME_SPENT": "<PERSON>zas spędzony", "TITLE": "Dostosuj widok zadań"}}, "TIME_TRACKING": {"B": {"ALREADY_DID": "<PERSON>ż zrobiłem!", "SNOOZE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> na {{time}}"}, "B_TTR": {"ADD_TO_TASK": "Dodaj do Zadania", "MSG": "<PERSON><PERSON> czasu od {{time}}"}, "D_IDLE": {"ADD_ENTRY": "Dodaj wpis do śledzenia", "BREAK": "Przerwa", "CREATE_AND_TRACK": "<em>Stwórz</em> i przypisz do", "IDLE_FOR": "<PERSON>e pracowałeś przez:", "RESET_BREAK_REMINDER_TIMER": "Zresetuj timer przypomnienia o przerwie", "SIMPLE_CONFIRM_COUNTER_CANCEL": "Pomiń", "SIMPLE_CONFIRM_COUNTER_OK": "Ścieżka", "SIMPLE_COUNTER_CONFIRM_TXT": "Wybrałeś pomijanie, ale aktywowałeś {{nr}} przyciski prostego licznika. <PERSON><PERSON><PERSON><PERSON> czas bezczynności?", "SIMPLE_COUNTER_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>, a<PERSON> do {{title}}.", "SIMPLE_COUNTER_TOOLTIP_DISABLE": "<PERSON><PERSON><PERSON><PERSON>, aby NIE śledzić do {{title}}.", "SKIP": "Pomiń", "SPLIT_TIME": "Podział czasu na wiele zadań i przerwy", "TASK": "<PERSON><PERSON><PERSON>", "TRACK_TO": "Przypisz do"}, "D_TRACKING_REMINDER": {"CREATE_AND_TRACK": "<em>Stwórz</em> i przypisz do", "IDLE_FOR": "<PERSON>e pracowałeś przez:", "NOTIFICATION_TITLE": "Śledź swój czas!", "TASK": "<PERSON><PERSON><PERSON>", "TRACK_TO": "Przypisz do:", "UNTRACKED_TIME": "<PERSON>zas nie przypisany:"}}, "WORKLOG": {"CMP": {"DAYS_WORKED": "Days, worked:", "MONTH_WORKED": "Month worked:", "REPEATING_TASK": "Powtarzające zadanie", "RESTORE_TASK_FROM_ARCHIVE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>archiwizowane Zadanie", "TASKS": "Zadania", "TOTAL_TIME": "Całkowity czas spędzony:", "WEEK_NR": "Tydzień {{nr}}", "WORKED": "Pracowa<PERSON>"}, "D_CONFIRM_RESTORE": "Are you sure you want to move the task <strong>\"{{title}}\"</strong> into your todays task list?", "D_EXPORT_TITLE": "Worklog Export {{start}}–{{end}}", "D_EXPORT_TITLE_SINGLE": "Worklog Export {{day}}", "EXPORT": {"ADD_COL": "Add column", "COPY_TO_CLIPBOARD": "Copy to clipboard", "DONT_ROUND": "don't round", "EDIT_COL": "Edit column", "GROUP_BY": "Group by", "O": {"DATE": "Date", "ENDED_WORKING": "Ended Working", "ESTIMATE_AS_CLOCK": "Estimate as clock (e.g. 5:23)", "ESTIMATE_AS_MILLISECONDS": "Estimate as milliseconds", "ESTIMATE_AS_STRING": "Estimate as string (e.g. 5h 23m)", "FULL_HALF_HOURS": "full half hours", "FULL_HOURS": "full hours", "FULL_QUARTERS": "full quarters", "NOTES": "Task Descriptions", "PARENT_TASK": "Parent Task", "PARENT_TASK_TITLES_ONLY": "Parent Task Titles only", "PROJECTS": "Project Names", "STARTED_WORKING": "Started Working", "TAGS": "Tags", "TASK_SUBTASK": "Task/Subtask", "TIME_AS_CLOCK": "Time as clock (e.g. 5:23)", "TIME_AS_MILLISECONDS": "Time as milliseconds", "TIME_AS_STRING": "Time as string (e.g. 5h 23m)", "TITLES_AND_SUB_TASK_TITLES": "Titles and Sub Task Titles", "WORKLOG": "Worklog"}, "OPTIONS": "Options", "ROUND_END_TIME_TO": "Round end time to", "ROUND_START_TIME_TO": "Round start time to", "ROUND_TIME_WORKED_TO": "Round time worked to", "SAVE_TO_FILE": "Save to file", "SEPARATE_TASKS_BY": "Separate tasks by", "SHOW_AS_TEXT": "Show as text"}, "WEEK": {"EXPORT": "Export Week Data", "NO_DATA": "No tasks this week yet.", "TITLE": "Title"}}}, "FILE_IMEX": {"DIALOG_CONFIRM_URL_IMPORT": {"INITIATED_MSG": "Rozpoczęto automatyczny import danych.", "SOURCE_URL_DOMAIN": "<PERSON><PERSON>", "TITLE": "Potwierdź import danych z URL", "WARNING_MSG": "Kontynuacja spowoduje nadpisanie obecnych danych i konfiguracji aplikacji zawartością z podanego URL. Tej operacji nie można cofnąć.", "WARNING_TITLE": "Ostrzeżenie"}, "EXPORT_DATA": "Export Data", "IMPORT_FROM_FILE": "Import from file", "IMPORT_FROM_URL": "Importuj z URL", "IMPORT_FROM_URL_DIALOG_DESCRIPTION": "Wprowadź pełny URL pliku kopii zapasowej JSON Super Productivity, który chcesz zaimportować.", "IMPORT_FROM_URL_DIALOG_TITLE": "Importuj z URL", "OPEN_IMPORT_FROM_URL_DIALOG": "Importuj z URL", "PRIVACY_EXPORT": "Eksport anonimowych danych (do wysłania <NAME_EMAIL> w celu debugowania)", "S_BACKUP_DOWNLOADED": "Kopia zapasowa pobrana do folderu dokumentów Androida", "S_ERR_IMPORT_FAILED": "Importowanie danych nie powiodło się", "S_ERR_INVALID_DATA": "Import failed: Invalid JSON", "S_ERR_INVALID_URL": "Import nie powiódł się: Podano nieprawidłowy URL", "S_ERR_NETWORK": "Import nie powiódł się: Błąd sieci podczas pobierania danych z URL", "S_IMPORT_FROM_URL_ERR_DECODE": "Błąd: <PERSON><PERSON> moż<PERSON> zdekodować parametru URL do importu. Upew<PERSON>j <PERSON>, że jest poprawnie sformatowany.", "URL_PLACEHOLDER": "Wprowadź URL do importu"}, "G": {"ADD": "<PERSON><PERSON><PERSON>", "ADVANCED_CFG": "<PERSON><PERSON>wanso<PERSON> konfiguracja", "CANCEL": "Cancel", "CLOSE": "Close", "CONFIRM": "Potwierdź", "DELETE": "Delete", "DISMISS": "<PERSON><PERSON><PERSON>", "DO_IT": "Do it!", "DURATION_DESCRIPTION": "np. \"5h 23m\", co daje wynik 5 godzin w 23 minuty.", "EDIT": "Edit", "ENABLED": "Włą<PERSON><PERSON>", "EXAMPLE_VAL": "click to edit", "EXTENSION_INFO": "Please <a target=\"_blank\" href=\"https://chrome.google.com/webstore/detail/super-productivity/ljkbjodfmekklcoibdnhahlaalhihmlb\"> download the chrome extension</a> in order to allow communication with the Jira Api and Idle Time Handling. Note that this doesn't work for mobile.", "HIDE": "<PERSON><PERSON><PERSON><PERSON>", "ICON_INP_DESCRIPTION": "Obsługiwane są również wszystkie emotikony utf-8!", "INBOX_PROJECT_TITLE": "Skrzynka odbiorcza", "LOGIN": "<PERSON><PERSON>", "LOGOUT": "Logout", "MINUTES": "{{m}} minutes", "MOVE_BACKWARD": "Przesuń wstecz", "MOVE_FORWARD": "Przesuń do przodu", "NEXT": "Next", "NO_CON": "You are currently offline. Please reconnect to the internet.", "NONE": "None", "OK": "Ok", "OVERDUE": "Zaległy", "PREVIOUS": "Previous", "REMOVE": "Remove", "RESET": "Reset", "SAVE": "Save", "SUBMIT": "Prześ<PERSON>j", "TITLE": "Title", "TODAY_TAG_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "TRACKING_INTERVAL_DESCRIPTION": "Śledzenie czasu przy użyciu tego interwału w milisekundach. <PERSON><PERSON><PERSON><PERSON> to zmienić, aby zmniejszyć liczbę zapisów na dysku. Zobacz błąd #2355.", "UNDO": "Undo", "UPDATE": "Update", "WITHOUT_PROJECT": "Without Project", "YESTERDAY": "Yesterday"}, "GCF": {"AUTO_BACKUPS": {"HELP": "Auto save all data to your app folder in order to have it ready in case something goes wrong.", "LABEL_IS_ENABLED": "Enable automatic backups", "LOCATION_INFO": "Backups are saved to:", "TITLE": "Automatic Backups"}, "CALENDARS": {"BROWSER_WARNING": "Ze względu na ograniczenia dotyczące różnych źródeł <b>prawdopodobnie NIE będzie to działać z wersją Super Productivity na przeglądarkę.<br /> Prosimy <a href=\"https://super-productivity.com/download/\">o pobranie wersji desktopowej</a> , aby korzystać z tej funkcji!</b>", "CAL_PATH": "Adres URL źródła iCal", "CAL_PROVIDERS": "Dostawcy kalendarza (eksperymentalni i opcjonalni)", "CHECK_UPDATES": "Sprawdzanie zdalnych aktualizacji co X", "DEFAULT_PROJECT": "Domyślny projekt dla dodanych zadań kalendarza", "HELP": "<PERSON><PERSON><PERSON><PERSON> <PERSON> kalendarze, aby otr<PERSON><PERSON><PERSON><PERSON> przypomnienia i dodawać je jako zadania w Super Productivity. Integracja działa przy użyciu formatu iCal. Aby to zadziałało, kalendarze muszą być dostępne przez Internet lub za pośrednictwem systemu plików.", "SHOW_BANNER_THRESHOLD": "Pokaż powiadomienie X przed zdarzeniem (puste dla wyłączonego)"}, "EVALUATION": {"IS_HIDE_EVALUATION_SHEET": "Hide evaluation sheet on daily summary", "TITLE": "Evaluation & Metrics"}, "FOCUS_MODE": {"HELP": "Tryb skupienia otwiera ekran bez rozpraszania uwagi, aby pomóc skupić się na bieżącym zadaniu.", "L_ALWAYS_OPEN_FOCUS_MODE": "<PERSON><PERSON><PERSON> otwarty tryb ostrości podczas śledzenia", "L_SKIP_PREPARATION_SCREEN": "Pomiń ekran przygotowania (rozciąganie itp.)", "TITLE": "<PERSON><PERSON>"}, "IDLE": {"HELP": "<div><p>When idle time handling is enabled a dialog will open after a specified amount of time to check if and on which task you want to track your time, when you have been idle.</p></div>", "IS_ENABLE_IDLE_TIME_TRACKING": "Enable idle time handling", "IS_ONLY_OPEN_IDLE_WHEN_CURRENT_TASK": "Only trigger idle time dialog when a current task is selected", "MIN_IDLE_TIME": "Trigger idle after X", "TITLE": "Idle Handling"}, "IMEX": {"HELP": "<p>Here you can export all your data as a <strong>JSON</strong> for backups, but also to use it in a different context (e.g. you might want to export your projects in the browser and import them into the desktop version). </p> <p>The import expects valid JSON to be copied into the text area. <strong>NOTE: Once you hit the import button all your current settings and data will be overwritten!</strong></p>", "TITLE": "Import/Export"}, "KEYBOARD": {"ADD_NEW_NOTE": "Add new note", "ADD_NEW_TASK": "Add New Task", "APP_WIDE_SHORTCUTS": "Global Shortcuts (application wide)", "COLLAPSE_SUB_TASKS": "Collapse Sub Tasks", "EXPAND_SUB_TASKS": "Expand Sub Tasks", "GLOBAL_ADD_NOTE": "Add new note", "GLOBAL_ADD_TASK": "Add new task", "GLOBAL_SHOW_HIDE": "Show/Hide Super Productivity", "GLOBAL_TOGGLE_TASK_START": "Toggle time tracking for last active task", "GO_TO_DAILY_AGENDA": "Go to Agenda", "GO_TO_FOCUS_MODE": "Go to Focus Mode", "GO_TO_SCHEDULE": "Go to Schedule", "GO_TO_SCHEDULED_VIEW": "Go to scheduled Tasks", "GO_TO_SETTINGS": "Go to Settings", "GO_TO_WORK_VIEW": "Go to Work View", "HELP": "<p>Here you can configure all keyboard shortcuts.</p> <p>Click on the text input and enter the desired keyboard combination. Hit enter to save and Escape to abort.</p> <p>There are three types of shortcuts:</p> <ul> <li> <strong>Global shortcuts:</strong> When the app is running it will trigger the action from every other application. </li> <li> <strong>Application level shortcuts:</strong> Will trigger from every screen of the application, but not if you're currently editing a text field. </li> <li> <strong>Task level shortcuts:</strong> They will only trigger if you have selected a task via mouse or keyboard and usually trigger an action specifically related to that one task. </li> </ul><p>You can <strong>press Escape to remove a shortcut.</strong>", "MOVE_TASK_DOWN": "Move Task down in List", "MOVE_TASK_TO_BOTTOM": "Przenieś zadanie na dół listy", "MOVE_TASK_TO_TOP": "Przenieś zadanie na górę listy", "MOVE_TASK_UP": "Move Task up in List", "MOVE_TO_BACKLOG": "Move Task to Task Backlog", "MOVE_TO_REGULARS_TASKS": "Move Task to Today\"s Task List", "OPEN_PROJECT_NOTES": "Show/Hide Project Notes", "SAVE_NOTE": "<PERSON><PERSON><PERSON><PERSON>ę", "SELECT_NEXT_TASK": "Select next Task", "SELECT_PREVIOUS_TASK": "Select previous Task", "SHOW_SEARCH_BAR": "Show search bar", "SYSTEM_SHORTCUTS": "Global Shortcuts (system wide)", "TASK_ADD_ATTACHMENT": "Attach file or link", "TASK_ADD_SUB_TASK": "Add sub Task", "TASK_DELETE": "Delete Task", "TASK_EDIT_TAGS": "Edit Tags", "TASK_EDIT_TITLE": "Edit Title", "TASK_MOVE_TO_PROJECT": "Open move task to project menu", "TASK_OPEN_CONTEXT_MENU": "Open task context menu", "TASK_OPEN_ESTIMATION_DIALOG": "Edit estimation / time spent", "TASK_PLAN_FORDAY": "Plan na dzień", "TASK_SCHEDULE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TASK_SHORTCUTS": "Zadania", "TASK_SHORTCUTS_INFO": "The following shortcuts apply for the currently selected task (selected via tab or mouse).", "TASK_TOGGLE_DETAIL_PANEL_OPEN": "Show/Hide additional task info", "TASK_TOGGLE_DONE": "Toggle Done", "TITLE": "Skróty <PERSON>zo<PERSON>", "TOGGLE_BACKLOG": "Pokaż/Ukryj Zadanie Backlogu", "TOGGLE_BOOKMARKS": "Pokaż/Ukryj Pasek Zakładek", "TOGGLE_ISSUE_PANEL": "Pokaż/ukryj panel emisji", "TOGGLE_PLAY": "Start/Stop Zadania", "TOGGLE_SIDE_NAV": "Pokaż i Zaznacz/Ukryj Panel boczny", "TOGGLE_TASK_VIEW_CUSTOMIZER_PANEL": "Przełącz panel filtrów/grup/sortowania", "TRIGGER_SYNC": "Synchronizacja w<PERSON>zwalania (j<PERSON><PERSON><PERSON>)", "ZOOM_DEFAULT": "Zoom default (Desktop only)", "ZOOM_IN": "Zoom in (Desktop only)", "ZOOM_OUT": "Zoom out (Desktop only)"}, "LANG": {"AR": "عرب<PERSON>", "CZ": "Czechy", "DE": "De<PERSON>ch", "EN": "<PERSON><PERSON><PERSON>", "ES": "Español", "FA": "فار<PERSON>ی", "FR": "<PERSON><PERSON><PERSON><PERSON>", "HR": "Hrvatski", "ID": "Indonezyjski", "IT": "<PERSON><PERSON><PERSON><PERSON>", "JA": "日本語", "KO": "한국어", "LABEL": "<PERSON><PERSON>bierz język", "NB": "Norsk Bokmål", "NL": "Nederlands", "PL": "<PERSON><PERSON>", "PT": "Português", "RU": "<PERSON><PERSON><PERSON><PERSON>", "SK": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "Język", "TR": "Türkçe", "UK": "Українська", "ZH": "中文(简体)", "ZH_TW": "中文(繁體)"}, "MISC": {"DEFAULT_PROJECT": "Domyślny projekt dla niezdefiniowanych zadań", "FIRST_DAY_OF_WEEK": "Pierwszy dzień tygodnia", "HELP": "<p><strong><PERSON><PERSON> w<PERSON><PERSON>sz powidomień na pulpicie?</strong> Dla windowsa sprawdź czy w Ustawienia > System > Powiadomienia i akcje zaznaczone zostały odpowiednie opcje.</p>", "IS_AUTO_ADD_WORKED_ON_TO_TODAY": "Dodaj automatycznie tag dzisiejszy do zadania", "IS_AUTO_MARK_PARENT_AS_DONE": "Zaznacz zadanie główne jako uko<PERSON>, je<PERSON><PERSON> wszystkie podrzędne zostały ukończone", "IS_CONFIRM_BEFORE_EXIT": "Confirm before exiting the app", "IS_CONFIRM_BEFORE_EXIT_WITHOUT_FINISH_DAY": "Potwierdź przed opuszczeniem aplikacji bez wcześniejszego zakończenia dnia.", "IS_DARK_MODE": "Dark Mode", "IS_DISABLE_ANIMATIONS": "Wyłącz wszystkie animacje", "IS_HIDE_NAV": "Hide navigation until main header is hovered (desktop only)", "IS_MINIMIZE_TO_TRAY": "Minimize to tray (desktop only)", "IS_SHOW_TIP_LONGER": "Pokaż wskazówkę dotyczącą produktywności przy starcie aplikacji nieco dłużej", "IS_TRAY_SHOW_CURRENT_COUNTDOWN": "Pokaż aktualne odliczanie w zasobniku / menu statusu (tylko na desktopie Mac)", "IS_TRAY_SHOW_CURRENT_TASK": "Show current task in the tray / status menu (desktop mac/windows only)", "IS_TURN_OFF_MARKDOWN": "Turn off markdown parsing for notes", "IS_USE_MINIMAL_SIDE_NAV": "Używaj minimalnego p<PERSON>a nawig<PERSON> (wyświetlaj tylko ikony)", "START_OF_NEXT_DAY": "<PERSON><PERSON><PERSON> rozpoczęcia następnego dnia", "START_OF_NEXT_DAY_HINT": "od kiedy (w godzinach) ma być liczony następny dzień. domyślnie jest to północ, c<PERSON>li 0.", "TASK_NOTES_TPL": "Task description template", "TITLE": "Ustawienia różne"}, "POMODORO": {"BREAK_DURATION": "Duration of short breaks", "CYCLES_BEFORE_LONGER_BREAK": "Start longer break after X work sessions", "DURATION": "Duration of work sessions", "HELP": "<p>The pomodoro timer can be configured via a couple of settings. The duration of every work session, the duration of normal breaks, the number of work sessions to run before a longer break is started and the duration of this longer break.</p> <p>You can also set if you want to display your distractions during your pomodoro breaks.</p> <p>Setting \"Pause time tracking on pomodoro break\" will also track your breaks as work time spent on a task. </p> <p>Enabling \"Pause pomodoro session when no active task\" will also pause the pomodoro session, when you pause a task.</p>", "IS_ENABLED": "Enable pomodoro timer", "IS_MANUAL_CONTINUE": "Manually confirm starting next pomodoro session", "IS_MANUAL_CONTINUE_BREAK": "Manually confirm starting next break", "IS_PLAY_SOUND": "Play sound when session is done", "IS_PLAY_SOUND_AFTER_BREAK": "Play sound when break is done", "IS_PLAY_TICK": "Play tick sound every second", "IS_STOP_TRACKING_ON_BREAK": "Stop time tracking for task on break", "LONGER_BREAK_DURATION": "Duration of longer breaks", "TITLE": "Pomodoro Timer"}, "REMINDER": {"COUNTDOWN_DURATION": "Pokaż baner X przed właściwym przypomnieniem", "IS_COUNTDOWN_BANNER_ENABLED": "Wyświetlanie banera odliczającego przed upływem terminu przypomnienia", "TITLE": "Przypomnienia"}, "SCHEDULE": {"HELP": "The schedule feature should provide you with a quick overview over how your planned tasks play out over time. You can find it in the left hand menu under <a href='#/schedule'>'Schedule'</a>. ", "L_IS_LUNCH_BREAK_ENABLED": "Włącz przerwę na lunch", "L_IS_WORK_START_END_ENABLED": "Limit unscheduled task flow to specific work times", "L_LUNCH_BREAK_END": "Koniec przerwy obiadowej", "L_LUNCH_BREAK_START": "Początek przerwy obiadowej", "L_WORK_END": "Work Day End", "L_WORK_START": "Work Day Start", "LUNCH_BREAK_START_END_DESCRIPTION": "np. 13:00", "TITLE": "Schedule", "WORK_START_END_DESCRIPTION": "e.g. 17:00"}, "SHORT_SYNTAX": {"HELP": "<p><PERSON><PERSON>j mo<PERSON> kontrolować opcje krótkiej składni podczas tworzenia zadania</p>", "IS_ENABLE_DUE": "Włącz krótką składnię (@<Due time>)", "IS_ENABLE_PROJECT": "Włącz krótką składnię projektu (+<Project name>)", "IS_ENABLE_TAG": "Włącz krótką składnię tagu (#<Tag>)", "TITLE": "Krótka składnia"}, "SOUND": {"BREAK_REMINDER_SOUND": "Dźwięk przypomnienia o przerwie", "DONE_SOUND": "Dźwięk zrobionego zadania", "IS_INCREASE_DONE_PITCH": "Zwiększaj tonację dla każdego wykonanego zadania", "TITLE": "Dźwięk", "TRACK_TIME_SOUND": "Dźwięk przypomnienia o czasie utworu", "VOLUME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "TAKE_A_BREAK": {"ADD_NEW_IMG": "<PERSON><PERSON><PERSON> mot<PERSON> obraz", "FULL_SCREEN_BLOCKER_DURATION": "<PERSON>zas wyświetlania okna pełnoekranowego (tylko pulpit)", "HELP": "<div> <p>Allows you to configure a reoccurring reminder when you have worked for a specified amount of time without taking a break.</p> <p>You can modify the message displayed. ${duration} will be replaced with the time spent without a break.</p> </div>", "IS_ENABLED": "Enable take a break reminder", "IS_FOCUS_WINDOW": "Focus app window when reminder is active (desktop only)", "IS_FULL_SCREEN_BLOCKER": "Wyświetlanie wiadomości w oknie pełnoekranowym (tylko pulpit)", "IS_LOCK_SCREEN": "Lock screen when a break is due (desktop only)", "MESSAGE": "Take a break message", "MIN_WORKING_TIME": "<PERSON><PERSON> take a break notification after <PERSON> working without one", "MOTIVATIONAL_IMGS": "Motivational image (web url)", "NOTIFICATION_TITLE": "<PERSON>rób sobie przerwę!", "SNOOZE_TIME": "Snooze time when prompted to take a break", "TITLE": "Break Reminder"}, "TIME_TRACKING": {"HELP": "Przypomnienie o śledzeniu czasu to baner wyświetlany na wypadek, gdybyś zapomniał rozpocząć śledzenie czasu.", "L_DEFAULT_ESTIMATE": "Domyślne oszacowanie czasu dla nowych zadań", "L_DEFAULT_ESTIMATE_SUB_TASKS": "Domyślne oszacowanie czasu dla nowych zadań podrzędnych", "L_IS_AUTO_START_NEXT_TASK": "Rozpoczęcie śledzenia następnego zadania po oznaczeniu bieżącego jako wykonanego", "L_IS_NOTIFY_WHEN_TIME_ESTIMATE_EXCEEDED": "Powiadomienie o przekroczeniu szacowanego czasu", "L_IS_TRACKING_REMINDER_ENABLED": "Przypomnienie o śledzeniu włączone", "L_IS_TRACKING_REMINDER_FOCUS_WINDOW": "Okno aplikacji Focus, gdy przy<PERSON>mnienie jest aktywne (tylko komputery)", "L_IS_TRACKING_REMINDER_NOTIFY": "<PERSON><PERSON><PERSON><PERSON>, gdy pojawi się przypomnienie o śledzeniu c<PERSON>u", "L_IS_TRACKING_REMINDER_SHOW_ON_MOBILE": "Wyświetlanie przypomnienia o śledzeniu w aplikacji mobilnej", "L_TRACKING_INTERVAL": "Interwał śledzenia czasu (EKSPERYMENTALNY)", "L_TRACKING_REMINDER_MIN_TIME": "<PERSON>zas oczekiwania przed wyświetleniem banera przypominającego o śledzeniu", "TITLE": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>"}}, "GLOBAL_RELATIVE_TIME": {"FUTURE": {"A_DAY": "W ciągu dnia", "A_MINUTE": "w ciągu minuty", "A_MONTH": "w ciągu miesiąca", "A_YEAR": "w ciągu roku", "AN_HOUR": "w ciągu godziny", "DAYS": "w ciągu {{count}} dni", "FEW_SECONDS": "za kilka sekund", "HOURS": "za {{count}} godzin", "MINUTES": "za {{count}} minut", "MONTHS": "za {{count}} mi<PERSON><PERSON><PERSON>", "YEARS": "za {{count}} lat"}, "PAST": {"A_DAY": "<PERSON>zie<PERSON> temu", "A_MINUTE": "minutę temu", "A_MONTH": "<PERSON><PERSON><PERSON><PERSON> temu", "A_YEAR": "rok temu", "AN_HOUR": "<PERSON><PERSON><PERSON> temu", "DAYS": "{{count}} dni temu", "FEW_SECONDS": "kilka sekund temu", "HOURS": "{{count}} god<PERSON> temu", "MINUTES": "{{count}} minut temu", "MONTHS": "{{count}} <PERSON><PERSON><PERSON><PERSON> temu", "YEARS": "{{count}} lat temu"}}, "GLOBAL_SNACK": {"COPY_TO_CLIPPBOARD": "Skopiowano do schowka", "ERR_COMPRESSION": "Błąd kompresji interfejsu", "FILE_DOWNLOADED": "{{fileName}} pobrane", "FILE_DOWNLOADED_BTN": "Otwórz folder", "NAVIGATE_TO_TASK_ERR": "Nie można skupić się na zadaniu. <PERSON><PERSON> to zostało usunięte?", "PERSISTENCE_DISALLOWED": "<PERSON> nie będą trzymane na stałe. Bądź świadom, że może to prowadzić do utraty danych!!", "PERSISTENCE_ERROR": "Błąd podczas żądania utrwalenia danych: {{err}}", "RUNNING_X": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{str}}\".", "SHORTCUT_WARN_OPEN_BOOKMARKS_FROM_TAG": "{{key<PERSON><PERSON><PERSON>}} pressed, but open bookmarks shortcut is only available when in project context."}, "GPB": {"ASSETS": "Loading assets...", "DBX_DOWNLOAD": "Dropbox: Download file...", "DBX_GEN_TOKEN": "Dropbox: Generate token...", "DBX_META": "Dropbox: Get file meta...", "DBX_UPLOAD": "Dropbox: Upload file...", "GITHUB_LOAD_ISSUE": "GitHub: Load issue data...", "JIRA_LOAD_ISSUE": "Jira: Load issue data...", "SYNC": "Synchronizacja danych...", "UNKNOWN": "Loading remote data", "WEB_DAV_DOWNLOAD": "WebDAV: Downloading data...", "WEB_DAV_UPLOAD": "WebDAV: Uploading data..."}, "MH": {"ADD_NEW_TASK": "<PERSON><PERSON>j nowe Zadanie", "ALL_PLANNED_LIST": "Powtarzanie/zaplanowane", "BOARDS": "Tablice", "CREATE_PROJECT": "Stwórz <PERSON>je<PERSON>", "CREATE_TAG": "Stwórz Tag", "DELETE_PROJECT": "Us<PERSON>ń projekt", "DELETE_TAG": "Usuń Tag", "ENTER_FOCUS_MODE": "Włącz tryb skupienia", "GO_TO_TASK_LIST": "Idź do listy zadań", "HELP": "Pomoc", "HM": {"CALENDARS": "Jak to zrobić: Połącz kalendarze", "CONTRIBUTE": "<PERSON><PERSON><PERSON><PERSON>", "GET_HELP_ONLINE": "Uzyskaj pomoc online", "KEYBOARD": "Howto: Zaawansowana klawiatura", "REDDIT_COMMUNITY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Reddit", "REPORT_A_PROBLEM": "<PERSON><PERSON>ł<PERSON>ś problem", "START_WELCOME": "Rozpocznij wycieczkę powitalną", "SYNC": "<PERSON><PERSON> to zrobić: Konfiguracja synchronizacji"}, "METRICS": "<PERSON><PERSON><PERSON>", "NO_PROJECT_INFO": "Brak dostępnych projektów. Możesz utworzyć nowy projekt, klikając przycisk \"Utwórz projekt\".", "NO_TAG_INFO": "Obecnie nie ma żadnych tagów. Tagi można <PERSON>, wpisując `#yourTagName` podczas dodawania lub edytowania zadań.", "NOTES": "Notatki", "NOTES_PANEL_INFO": "Notatki mogą być wyświetlane tylko w widoku harmonogramu i zwykłej listy zadań.", "PLANNER": "Planista", "PROCRASTINATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PROJECT_MENU": "<PERSON>u projektu", "PROJECT_SETTINGS": "Ustawienia projektów", "PROJECTS": "Projekty", "QUICK_HISTORY": "Szybka historia", "SCHEDULE": "Harmonogram", "SEARCH": "Szukaj", "SETTINGS": "Ustawienia", "SHOW_SEARCH_BAR": "Pokaż pasek wyszukiwania", "TAGS": "Tagi", "TASK_LIST": "Lista zadań", "TASKS": "Zadania", "TOGGLE_SHOW_BOOKMARKS": "Pokaż/Ukryj <PERSON>ak<PERSON>dki", "TOGGLE_SHOW_ISSUE_PANEL": "Pokaż/Ukryj panel problemów", "TOGGLE_SHOW_NOTES": "Pokaż/Ukryj Notatki Projektu", "TOGGLE_TRACK_TIME": "Start/Stop zliczania czasu", "TRIGGER_SYNC": "Ręczne wyzwalanie synchronizacji", "WORKLOG": "<PERSON><PERSON> pracy"}, "MIGRATE": {"C_DOWNLOAD_BACKUP": "<PERSON><PERSON>z pobrać kopię zapasową starszych danych (może być używana ze starszymi wersjami Super Productivity)?", "DETECTED_LEGACY": "Wykryto starsze dane. Przeniesiemy go za Ciebie!", "E_MIGRATION_FAILED": "Migracja nie powiodła się! z błędem:", "E_RESTART_FAILED": "Automatyczne ponowne uruchomienie nie powiodło się. Uruchom ponownie aplikację ręcznie!", "SUCCESS": "Migracja gotowa! Ponowne uruchamianie aplikacji teraz..."}, "PDS": {"ADD_TASKS_FROM_TODAY": "Dodawanie zadań od dzisiaj", "BACK": "Poczekaj! Zapomniałem czegoś...", "BREAK_LABEL": "Przewy (nr / czas)", "CELEBRATE": "Czas na <i>świętowanie!</i>", "CLEAR_ALL_CONTINUE": "Wyczyść wszystkie zakończone i kontynuuj", "D_CONFIRM_APP_CLOSE": {"CANCEL": "<PERSON><PERSON>, tylko wyczyść zadania", "MSG": "Twoja praca skończona, czas wracać do domu!", "OK": "Tak, tak! Spadamy!"}, "ESTIMATE_TOTAL": "Łączna wartość szacunkowa:", "EVALUATE_DAY": "OCENA", "EXPORT_TASK_LIST": "Eksportuj listę <PERSON>", "NO_TASKS": "Nie ma żadnych zadań na dziś", "PLAN_TOMORROW": "Plan", "REVIEW_TASKS": "Recenzja", "ROUND_5M": "Zaokrągl czas zadań do 5 minut", "ROUND_15M": "Zaokrągl czas zadań do 15 minut", "ROUND_30M": "Zaokrągl czas zadań do 30 minut", "ROUND_TIME_SPENT": "Zaokrągl Spęczony Czas", "ROUND_TIME_SPENT_TITLE": "Zaokrągl czas dla wszystkich zadań. B<PERSON>dź ostroży, bo tego nie zofniesz!", "ROUND_TIME_WARNING": "!!! <PERSON><PERSON><PERSON><PERSON>, bo tego nie da się odwr<PERSON><PERSON> !!!", "ROUND_UP_5M": "Zaokrągl czas zadań W GÓRĘ do 5 minut", "ROUND_UP_15M": "Zaokrągl czas zadań W GÓRĘ do 15 minut", "ROUND_UP_30M": "Zaokrągl czas zadań W GÓRĘ do 30 minut", "SAVE_AND_GO_HOME": "Zapisz i idź do domu", "SAVE_AND_GO_HOME_TOOLTIP": "Przenieś wszystkie wykonane zadania do archiwum (dziennika zadań) i opcjonalnie zsynchronizuj wszystkie dane, a następnie zamknij aplikację.", "START_END": "Start – Koniec", "SUMMARY_FOR": "Dzienne Podsumiwanie {{dayStr}}", "TASKS_COMPLETED": "Zadania ukończone", "TIME_SPENT_AND_ESTIMATE_LABEL": "Czas spędzony / Estymacja", "TIME_SPENT_ESTIMATE_TITLE": "Czas spędzony: Całkowity czas spędzony. Nie liczymy zadań zarchiwowanych. – Estymacja: Czas zaplanowany na wykonane zadania bez czasu pracy wykonanym w innych dniach.", "TIME_SPENT_TODAY_BY_TAG": "<PERSON>zas spędzony dzisiaj według tagów", "WEEK": "Tydzień"}, "PM": {"TITLE": "<PERSON><PERSON><PERSON>"}, "PS": {"GLOBAL_SETTINGS": "Ustawienia Globalne", "ISSUE_INTEGRATION": "Intergracja Błędów", "PRIVACY_POLICY": "Polityka Prywatności", "PRODUCTIVITY_HELPER": "Pomocnik Produktywności", "PROJECT_SETTINGS": "Ustawienia dla Projektu", "PROVIDE_FEEDBACK": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SYNC_EXPORT": "Synchronizacja i Eksport", "TAG_SETTINGS": "<PERSON><PERSON><PERSON>", "TOGGLE_DARK_MODE": "Włącz ciemny motyw"}, "SCHEDULE": {"NO_REPEATABLE_TASKS": "Obecnie nie ma powtarzających się zadań. <PERSON><PERSON><PERSON><PERSON> zadanie, w<PERSON><PERSON><PERSON><PERSON><PERSON> op<PERSON> \"Powtórz zadanie\" w panelu bocznym zadania. Aby go ot<PERSON><PERSON><PERSON><PERSON>, kliknij ikonę po prawej stronie, która pojawia się po najechaniu kursorem na zadanie (lub po prostu dotknij zadania na telefonie komórkowym).", "NO_SCHEDULED": "Żadne zadanie nie zostało zaplanowane. Możesz zaplanować wybierając \"Zaplanuj Zadanie\" z menu kontekstowego zadania. Aby je otworzyć kliknij trzy kropki po prawej na zadaniu.", "NO_SCHEDULED_TITLE": "Zaplanowane zadania na dany dzień", "REPEATED_TASKS": "Powtarzające się zadania", "SCHEDULED_TASKS": "Zaplanowane zadania", "SCHEDULED_TASKS_WITH_TIME": "Zaplanowane zadania z przypomnieniem", "START_TASK": "Ropoczni zadanie i wyłącz przypomnienie"}, "THEMES": {"amber": "amber", "blue": "blue", "blue-grey": "blue-grey", "cyan": "cyan", "deep-orange": "deep-orange", "deep-purple": "deep-purple", "green": "green", "indigo": "indigo", "light-blue": "light-blue", "light-green": "light-green", "lime": "lime", "pink": "pink", "purple": "purple", "SELECT_THEME": "Select Theme", "teal": "teal", "yellow": "yellow"}, "V": {"E_1TO10": "Please enter a value between 1 and 10", "E_DATETIME": "The entered value is not a datetime!", "E_DURATION": "Podaj prawidłowy czas trwania (np. 1 godzina)", "E_MAX": "Should not be bigger than {{val}}", "E_MAX_LENGTH": "Should be max {{val}} characters long", "E_MIN": "Should be smaller than {{val}}", "E_MIN_LENGTH": "Should be at least {{val}} characters long", "E_PATTERN": "Invalid input", "E_REQUIRED": "This field is required"}, "WW": {"ADD_MORE": "<PERSON><PERSON>j wi<PERSON>cej", "ADD_SCHEDULED_FOR_TOMORROW": "Dodaj zaplanowane zadania na jutro ({{nr}})", "ADD_SOME_TASKS": "Dodaj zadania do dzisiejszego planu!", "DONE_TASKS": "Wykonane z<PERSON>", "DONE_TASKS_IN_ARCHIVE": "Obecnie nie ma tutaj żadnych zakończonych zadań, ale są już archiwizowane.", "ESTIMATE_REMAINING": "Pozostało zaplanowanego czasu:", "FINISH_DAY": "Zakończ Dzień", "FINISH_DAY_FOR_PROJECT": "Dzień zakończenia tego projektu", "FINISH_DAY_FOR_TAG": "Dzień zakończenia dla tego tagu", "FINISH_DAY_TOOLTIP": "Oceń swój dzień, przenieś wszystkie wykonane zadania do archiwum (opcjonalnie) i/lub zaplanuj następny dzień.", "HELP_PROCRASTINATION": "Help I'm procrastinating!", "MOVE_DONE_TO_ARCHIVE": "Przenieś gotowe do archiwum", "NO_DONE_TASKS": "Obecnie nie ma żadnych wykonanych zadań", "NO_PLANNED_TASK_ALL_DONE": "wszystkie zadania zakończone", "NO_PLANNED_TASKS": "<PERSON><PERSON> z<PERSON>", "READY_TO_WORK": "Gotowy do Pracy!", "RESET_BREAK_TIMER": "Zresetuj bez czasu przerwy", "TIME_ESTIMATED": "Zaplanowany Czas:", "TODAY_REMAINING": "<PERSON><PERSON>ś pozostało:", "WITHOUT_BREAK": "Bez Przerwy:", "WORKING_TODAY": "Czas pracy:", "WORKING_TODAY_ARCHIVED": "Czas przepracowany dzisiaj nad zarchiwizowanymi zadaniami"}}