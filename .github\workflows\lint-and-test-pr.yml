name: 'Lint & Test PRs'

on: [pull_request]

permissions:
  pull-requests: write

jobs:
  test-on-linux:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      # work around for npm installs from git+https://github.com/johannesjo/J2M.git
      - name: Reconfigure git to use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/

      - name: Get npm cache directory
        id: npm-cache-dir
        run: |
          echo "::set-output name=dir::$(npm config get cache)"
      - uses: actions/cache@v4
        id: npm-cache # use this to check for `cache-hit` ==> if: steps.npm-cache.outputs.cache-hit != 'true'
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install npm Packages
        #  if: steps.npm-cache.outputs.cache-hit != 'true'
        run: npm i

      - name: Setup Chrome
        uses: browser-actions/setup-chrome@v1
        with:
          chrome-version: 'latest'

      - run: npm run lint:ci
      - run: npm run test
      - run: npm run e2e
      - name: Upload performance metrics as an artifact
        uses: actions/upload-artifact@v4
        with:
          name: nightwatch-performance-metrics
          path: perf-metrics*.json

      #      - name: Read performance metrics to env
      #        # if: github.event_name == 'pull_request'
      #        run: |
      #          perfResult=$(node tools/gen-perf-metrics.js)
      #          echo "e2ePerfResult<<EOF" >> $GITHUB_ENV
      #          echo "$perfResult" >> $GITHUB_ENV
      #          echo "EOF" >> $GITHUB_ENV
      #          echo $perfResult
      #          echo $GITHUB_ENV
      #      - name: Attach performance metrics to PR as comment
      #        # if: github.event_name == 'pull_request'
      #        uses: marocchino/sticky-pull-request-comment@v2
      #        continue-on-error: true
      #        with:
      #          #          header: test
      #          message: |
      #            Perf Result
      #            ${{ env.e2ePerfResult }}

      - name: 'Upload E2E results on failure'
        if: ${{ failure() }}
        uses: actions/upload-artifact@v4
        with:
          name: e2eResults
          path: e2e-test-results/**/*.*
          retention-days: 14
