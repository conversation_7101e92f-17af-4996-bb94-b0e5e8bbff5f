{"ANDROID": {"NOTIFICATIONS": {"SYNC_CONFLICT_MSG": "Veuillez vérifier et décider quoi faire.", "SYNC_CONFLICT_TITLE": "Un conflit de synchronisation s'est produit"}, "PERMANENT_NOTIFICATION_MSGS": {"INITIAL": "Application exécutée en arrière-plan pour permettre la synchronisation si elle est activée", "NO_ACTIVE_TASKS": "Aucune tâche active", "SYNCING": "Synchronisation"}}, "APP": {"B_INSTALL": {"IGNORE": "<PERSON><PERSON><PERSON>", "INSTALL": "Installer", "MSG": "Voulez-vous installer Super Productivity en tant que PWA ?"}, "B_OFFLINE": "Vous êtes déconnecté d'Internet. La synchronisation et la demande des données du fournisseur de problèmes ne fonctionneront pas.", "UPDATE_MAIN_MODEL": "Super Productivity a obtenu une mise à jour majeure! Certaines migrations pour vos données sont nécessaires. Veuillez noter que cela rend vos données incompatibles avec les anciennes versions de l'application.", "UPDATE_MAIN_MODEL_NO_UPDATE": "Aucune mise à jour de modèle choisie. Veuillez noter que vous devez soit rétrograder vers la dernière version, si vous ne souhaitez pas effectuer la mise à niveau du modèle.", "UPDATE_WEB_APP": "Une nouvelle version est disponible. Charger la nouvelle version ?"}, "BL": {"NO_TASKS": "Il n'y a actuellement aucune tâche dans votre backlog"}, "CONFIRM": {"AUTO_FIX": "Vos données semblent endommagées. Voulez-vous essayer de le réparer automatiquement? Cela peut entraîner une perte de données partielle.", "RELOAD_AFTER_IDB_ERROR": "Impossible d'accéder à la base de données :( Les causes possibles sont une mise à jour de l'application en arrière-plan ou un espace disque insuffisant. Appuyez sur OK pour recharger l'application (peut nécessiter un redémarrage manuel de l'application sur certaines plates-formes).", "RESTORE_FILE_BACKUP": "Il ne semble pas y avoir de DONNÉES, mais des sauvegardes sont disponibles sur \"{{dir}}\". Voulez-vous restaurer la dernière sauvegarde de {{from}}?", "RESTORE_FILE_BACKUP_ANDROID": "Il semble qu'il n'y ait PAS DE DONNÉES, mais une sauvegarde est disponible. Voulez-vous le charger ?", "RESTORE_STRAY_BACKUP": "Lors de la dernière synchronisation, une erreur s'est peut-être produite. Voulez-vous restaurer la dernière sauvegarde?"}, "DATETIME_SCHEDULE": {"LATER_TODAY": "Plus tard aujourd'hui", "NEXT_WEEK": "Prochaine semaine", "PLACEHOLDER": "V<PERSON><PERSON>z choisir une date", "PRESS_ENTER_AGAIN": "Appuyez à nouveau sur Entrée pour enregistrer", "TOMORROW": "demain"}, "F": {"ATTACHMENT": {"DIALOG_EDIT": {"ADD_ATTACHMENT": "Ajouter une pièce jointe", "EDIT_ATTACHMENT": "Modifier la pièce jointe", "LABELS": {"FILE": "<PERSON><PERSON><PERSON> du <PERSON>", "IMG": "Image", "LINK": "URL"}, "SELECT_TYPE": "Sélectionner un type", "TYPES": {"FILE": "Fichier (ouvert par l'application système par défaut)", "IMG": "Image (affichée en miniature)", "LINK": "<PERSON><PERSON> (ouvert dans le navigateur)"}}}, "BOARDS": {"DEFAULT": {"DONE": "<PERSON><PERSON><PERSON><PERSON>", "EISENHAUER_MATRIX": "<PERSON><PERSON>", "IMPORTANT": "Important", "IN_PROGRESS": "En cours", "KANBAN": "Ka<PERSON><PERSON>", "NOT_URGENT_IMPORTANT": "Pas urgent et important", "NOT_URGENT_NOT_IMPORTANT": "Pas urgent et pas important", "TO_DO": "À faire", "URGENT": "<PERSON><PERSON>", "URGENT_IMPORTANT": "Urgent et important", "URGENT_NOT_IMPORTANT": "Urgent et pas important"}, "FORM": {"ADD_NEW_PANEL": "Ajouter un nouveau panneau", "BACKLOG_TASK_FILTER_ALL": "<PERSON>ut", "BACKLOG_TASK_FILTER_NO_BACKLOG": "Exclure", "BACKLOG_TASK_FILTER_ONLY_BACKLOG": "Seulement le backlog", "BACKLOG_TASK_FILTER_TYPE": "Tâches du backlog", "COLUMNS": "Colonnes", "TAGS_EXCLUDED": "Tags exclus", "TAGS_REQUIRED": "Tags requis", "TASK_DONE_STATE": "État de la tâche terminée", "TASK_DONE_STATE_ALL": "<PERSON>ut", "TASK_DONE_STATE_DONE": "<PERSON><PERSON><PERSON><PERSON>", "TASK_DONE_STATE_UNDONE": "Non terminé"}, "V": {"ADD_NEW_BOARD": "Ajouter un nouveau tableau", "CONFIRM_DELETE": "Voulez-vous vraiment supprimer ce tableau ?", "CREATE_NEW_TAG_BTN": "<PERSON><PERSON>er un tag", "CREATE_NEW_TAG_MSG": "1 nouveau tag doit être créé pour que ce tableau fonctionne", "CREATE_NEW_TAGS_BTN": "<PERSON><PERSON><PERSON>", "CREATE_NEW_TAGS_MSG": "{{nr}} nouveaux tags doivent être créés pour que ce tableau fonctionne", "EDIT_BOARD": "Modifier le tableau", "NO_PANELS_BTN": "Configurer le tableau", "NO_PANELS_MSG": "Ce tableau n'a pas de panneaux configurés. Ajoutez-y des panneaux."}}, "CALDAV": {"DIALOG_INITIAL": {"TITLE": "Configurer CalDav pour le projet"}, "FORM": {"CALDAV_CATEGORY_FILTER": "Catégorie pour laquelle filtrer les problèmes (laissez vide pour aucun)", "CALDAV_PASSWORD": "Votre mot de passe CalDav", "CALDAV_RESOURCE": "Le nom de la ressource CalDav (le calendrier)", "CALDAV_URL": "URL CalDav (l'URL de base)", "CALDAV_USER": "Votre nom d'utilisateur CalDav", "IS_TRANSITION_ISSUES_ENABLED": "Compléter automatiquement les tâches CalDav à la fin de la tâche"}, "FORM_SECTION": {"HELP": "<p><PERSON><PERSON>, vous pouvez configurer SuperProductivity pour répertorier les tâches CalDav inachevées pour un projet spécifique dans le panneau de création de tâches de la vue de planification quotidienne. Ils seront listés comme des suggestions et fourniront un lien vers le todo ainsi que plus d'informations à ce sujet.</p> <p>En outre, vous pouvez automatiquement ajouter et synchroniser toutes les tâches inachevées dans votre backlog de tâches.</p>", "TITLE": "CalDav"}, "ISSUE_CONTENT": {"ASSIGNEE": "Bénéficiaire", "AT": "À", "ATTACHMENTS": "Pièces jointes", "CHANGED": "<PERSON><PERSON><PERSON><PERSON>", "COMMENTS": "Commentaires", "COMPONENTS": "Composants", "DESCRIPTION": "La description", "LABELS": "catégories", "LIST_OF_CHANGES": "Liste des changements", "MARK_AS_CHECKED": "Mar<PERSON> les mises à jour comme cochées", "ON": "sur", "RELATED": "<PERSON><PERSON>", "STATUS": "Statut", "STORY_POINTS": "Points d'histoire", "SUB_TASKS": "Sous-tâches", "SUMMARY": "Résumé", "WORKLOG": "Journal de travail", "WRITE_A_COMMENT": "Écrire un commentaire"}, "S": {"CALENDAR_NOT_FOUND": "CalDav: calendrier \"{{calendarName}}\" introuvable", "CALENDAR_READ_ONLY": "CalDav: l'agenda \"{{calendarName}}\" est en lecture seule", "ISSUE_NOT_FOUND": "CalDav: Todo \"{{issueId}}\" semble être supprimé sur le serveur."}}, "CALENDARS": {"BANNER": {"ADD_AS_TASK": "A<PERSON>ter une Tâche", "FOCUS_TASK": "Tâche de concentration", "TXT": "<strong>{{title}}</strong> commence à <strong>{{start}}</strong> !", "TXT_MULTIPLE": "<strong>{{title}}</strong> commence à <strong>{{start}}</strong> !<br> (et {{nrOfOtherBanners}} autres événements sont prévus)", "TXT_PAST": "<strong>{{title}}</strong> a commencé à <strong>{{start}}</strong> !", "TXT_PAST_MULTIPLE": "<strong>{{title}}</strong> a commencé à <strong>{{start}}</strong> !<br> (et {{nrOfOtherBanners}} autres événements sont prévus)"}, "S": {"CAL_PROVIDER_ERROR": "<PERSON><PERSON><PERSON> du fournisseur de calendrier : {{errTxt}}"}}, "CONFIG": {"S": {"UPDATE_SECTION": "Paramètres mis à jour pour <strong>{{sectionKey}}</strong>"}}, "D_RATE": {"A_HOW": "Comment et où <PERSON>", "BTN_DONT_BOTHER": "Ne me dérange plus", "TITLE": "\u001f926\u001f3fd <PERSON><PERSON><PERSON><PERSON> no<PERSON> pardon<PERSON>, mais...", "TXT": "Vous aideriez énormément le projet en <strong>l'évaluant positivement, si vous l'aimez !</strong>"}, "DOMINA_MODE": {"FORM": {"HELP": "Ré<PERSON>ète la phrase configurée tous les X lors du suivi du temps d'une tâche.", "L_INTERVAL": "Intervalle de répétition de la phrase", "L_TEXT": "Texte", "L_TEXT_DESCRIPTION": "Ex : « Travailler sur $ {currentTaskTitle} ! »", "L_VOICE": "Sélectionner une voix", "L_VOICE_DESCRIPTION": "Choisissez une voix", "TITLE": "Mode Domina"}}, "DROPBOX": {"S": {"ACCESS_TOKEN_ERROR": "Dropbox: impossible de générer un jeton d'accès à partir du code d'authentification", "ACCESS_TOKEN_GENERATED": "Dropbox: jeton d'accès généré à partir du code d'authentification", "AUTH_ERROR": "Dropbox: jeton d'accès non valide fourni", "AUTH_ERROR_ACTION": "Changer de <PERSON>on", "OFFLINE": "Dropbox: impossible de synchroniser, car hors ligne", "SYNC_ERROR": "Dropbox: erreur lors de la synchronisation", "UNABLE_TO_GENERATE_PKCE_CHALLENGE": "Dropbox : Impossible de générer le défi PKCE."}}, "FINISH_DAY_BEFORE_EXIT": {"C": {"FINISH_DAY_BEFORE_EXIT": "Il y a {{nr}} tâches terminées dans votre liste d'aujourd'hui qui ne sont pas encore déplacées vers l'archive. Voulez-vous vraiment arrêter sans finir votre journée ?"}}, "FOCUS_MODE": {"B": {"SESSION_RUNNING": "La session de concentration est en cours", "TO_FOCUS_OVERLAY": "Pour superposition de concentration"}, "BACK_TO_PLANNING": "Retour à la planification", "CONGRATS": "Félicitations pour avoir terminé cette session !", "CONTINUE_FOCUS_SESSION": "Continuer la session de concentration", "COUNTDOWN": "Compte à rebours", "FINISH_TASK_AND_SELECT_NEXT": "Terminer la tâche et sélectionner la suivante", "FLOWTIME": "Temps de flux", "FOR_TASK": "pour la tâche", "GET_READY": "Préparez-vous pour votre session de concentration !!!", "GO_TO_PROCRASTINATION": "<PERSON><PERSON><PERSON><PERSON> de l'aide en cas de procrastination", "GOGOGO": "Allez! Allez! Allez!!!", "NEXT": "Suivant", "ON": "activé", "OPEN_ISSUE_IN_BROWSER": "<PERSON><PERSON><PERSON><PERSON><PERSON> le problème dans un navigateur", "POMODORO_BACK": "Retour", "POMODORO_DISABLE": "Désactiver Pomodoro", "POMODORO_INFO": "Les sessions de concentration ne peuvent pas être utilisées avec le minuteur Pomodoro activé", "PREP_GET_MENTALLY_READY": "Préparez-vous mentalement à être concentré et productif", "PREP_SIT_UPRIGHT": "Asseyez-vous (ou tenez-vous debout) droit", "PREP_STRETCH": "Faire quelques étirements légers", "SELECT_ANOTHER_TASK": "Sélectionner une autre tâche", "SELECT_TASK": "Sé<PERSON><PERSON>ner une tâche sur laquelle se concentrer", "SESSION_COMPLETED": "Session de concentration terminée !", "SET_FOCUS_SESSION_DURATION": "Définir la durée de la session de concentration", "SHOW_HIDE_NOTES_AND_ATTACHMENTS": "Afficher/masquer les notes et pièces jointes de la tâche", "START_FOCUS_SESSION": "Débuter la session de concentration", "START_NEXT_FOCUS_SESSION": "Débuter la prochaine session de concentration", "WORKED_FOR": "Vous avez travaillé pendant"}, "GITEA": {"DIALOG_INITIAL": {"TITLE": "Configurer Gitea pour le projet"}, "FORM": {"FILTER_USER": "Nom d'utilisateur (e.g. pour filtrer vos propres modifications)", "HOST": "H<PERSON><PERSON> (e.g.: https://try.gitea.io)", "REPO_FULL_NAME": "Nom d'utilisateur ou nom de l'organisation/projet", "REPO_FULL_NAME_DESCRIPTION": "Peut être trouvé comme partie de l'URL, lors de la visualisation du projet dans le navigateur.", "SCOPE": "Portée", "SCOPE_ALL": "<PERSON>ut", "SCOPE_ASSIGNED": "Attribués à moi", "SCOPE_CREATED": "<PERSON><PERSON><PERSON> par moi", "TOKEN": "<PERSON>on d'a<PERSON>ès"}, "FORM_SECTION": {"HELP": "<p><PERSON><PERSON>, vous pouvez configurer SuperProductivity pour lister les problèmes ouverts de Gitea pour un dépôt spécifique dans le panneau de création de tâches dans la vue de planification quotidienne. Ils seront listés comme suggestions et fourniront un lien vers le problème ainsi que plus d'informations à son sujet.</p> <p>En plus, vous pouvez automatiquement ajouter et importer tous les problèmes ouverts.</p><p>Pour contourner les limites d'utilisation et pour accéder, vous pouvez fournir un jeton d'accès.</p>", "TITLE": "<PERSON><PERSON><PERSON>"}, "ISSUE_CONTENT": {"ASSIGNEE": "Responsable", "AT": "à", "DESCRIPTION": "Description", "LABELS": "Étiquettes", "MARK_AS_CHECKED": "Mar<PERSON> les mises à jour comme cochées", "PROJECT": "Projet", "STATUS": "Statut", "SUMMARY": "Résumé", "WRITE_A_COMMENT": "Écrire un commentaire"}, "S": {"ERR_UNKNOWN": "Gitea : Erreur inconnue {{statusCode}} {{errorMsg}}. Limite de taux de l'API dépassée ?"}}, "GITHUB": {"DIALOG_INITIAL": {"TITLE": "Configurer GitHub pour le projet"}, "FORM": {"FILTER_USER": "Nom d'utilisateur (e.g. pour filtrer vos propres modifications)", "INVALID_TOKEN_MESSAGE": "Le jeton GitHub n'est pas valide. Il doit commencer par \"ghp_\".", "IS_ASSIGNEE_FILTER": "Filtrer les problèmes qui m'ont été attribués", "REPO": "\"nom d'utilisateur / nom du repository\" pour le repository git que vous souhaitez suivre", "TOKEN": "<PERSON>on d'a<PERSON>ès"}, "FORM_SECTION": {"HELP": "<p>Ici, vous pouvez configurer SuperProductivity pour lister les problèmes ouverts de GitHub pour un dépôt spécifique dans le panneau de création de tâches dans la vue de planification quotidienne. Ils seront listés comme suggestions et fourniront un lien vers le problème ainsi que plus d'informations à son sujet.</p> <p>De plus, vous pouvez importer automatiquement tous les problèmes ouverts.</p><p><PERSON>ur contourner les limites d'utilisation et pour accéder, vous pouvez fournir un jeton d'accès. <a href='https://docs.github.com/en/free-pro-team@latest/developers/apps/scopes-for-oauth-apps'>Plus d'informations sur ses portées sont disponibles ici</a>.</p>", "TITLE": "GitHub"}, "ISSUE_CONTENT": {"ASSIGNEE": "Attribué à", "AT": "à", "DESCRIPTION": "Description", "LABELS": "Étiquettes", "LAST_COMMENT": "<PERSON><PERSON> commentaire", "LOAD_ALL_COMMENTS": "Charger les {nr} commentaires", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "Charger la description et tous les commentaires", "MARK_AS_CHECKED": "Mar<PERSON> les mises à jour comme cochées", "STATUS": "Statut", "SUMMARY": "Résumé", "WRITE_A_COMMENT": "Écrire un commentaire"}, "S": {"CONFIG_ERROR": "GitHub : erreur lors du mapping des données. Le nom de votre dépôt est-il correct ?", "ERR_UNKNOWN": "GitHub: erreur inconnue {{statusCode}} {{errorMsg}}. Limite de taux de l'API dépassée ?"}}, "GITLAB": {"DIALOG_INITIAL": {"TITLE": "Configurer GitLab pour le projet"}, "*********************": {"PAST_DAY_INFO": "La durée préremplie contient des données non suivies des jours passés.", "T_ALREADY_TRACKED": "D<PERSON>jà suivi", "T_TITLE": "Titre", "T_TO_BE_SUBMITTED": "A soumettre", "TITLE": "Soumettre sur GitLab le temps passé sur les tickets", "TOTAL_MSG": "Vous allez soumettre <em>{{totalTimeToSubmit}}</em> comme temps de travail total d'aujourd'hui, passé sur <em>{{nrOfTasksToSubmit}}</em> tickets différents."}, "FORM": {"FILTER": "<PERSON><PERSON><PERSON> person<PERSON>", "FILTER_DESCRIPTION": "Voir https://docs.gitlab.com/ee/api/issues.html#list-issues. Plusieurs peuvent être combinés avec le caractère &", "FILTER_USER": "Nom d'utilisateur (e.g. pour filtrer vos propres modifications)", "GITLAB_BASE_URL": "URL de base GitLab personnalisée", "PROJECT": "Chemin complet ou nom d'utilisateur / projet", "PROJECT_HINT": "par exemple, johannesjo/super-productivité", "SCOPE": "Portée", "SCOPE_ALL": "<PERSON>ut", "SCOPE_ASSIGNED": "Attribués à moi", "SCOPE_CREATED": "<PERSON><PERSON><PERSON> par moi", "SOURCE": "La source", "SOURCE_GLOBAL": "<PERSON>ut", "SOURCE_GROUP": "Groupe", "SOURCE_PROJECT": "Projet", "SUBMIT_TIMELOGS": "Soumettre le suivi de temps sur Gitlab", "SUBMIT_TIMELOGS_DESCRIPTION": "Afficher la boîte de dialogue de suivi du temps après avoir cliqué sur \"terminer la journée\"", "TOKEN": "<PERSON>on d'a<PERSON>ès"}, "FORM_SECTION": {"HELP": "<p> <PERSON><PERSON>, vous pouvez configurer SuperProductivity pour répertorier les problèmes ouverts de GitLab (que ce soit la version en ligne ou une instance auto-hébergée) pour un projet spécifique dans le panneau de création de tâches dans la vue de planification quotidienne. Ils seront répertoriés sous forme de suggestions et fourniront un lien vers le problème ainsi que plus d'informations à ce sujet. </p> <p> En outre, vous pouvez automatiquement ajouter et synchroniser tous les problèmes ouverts à votre carnet de tâches. </p>", "TITLE": "GitLab"}, "ISSUE_CONTENT": {"ASSIGNEE": "Responsable", "AT": "à", "DESCRIPTION": "Description", "LABELS": "Étiquettes", "MARK_AS_CHECKED": "Mar<PERSON> les mises à jour comme cochées", "PROJECT": "Projet", "STATUS": "Statut", "SUMMARY": "Résumé", "WRITE_A_COMMENT": "Écrire un commentaire"}, "S": {"ERR_UNKNOWN": "GitLab: erreur inconnue {{statusCode}} {{errorMsg}}"}}, "ISSUE": {"CROSS_ORIGIN_BROWSER_WARNING": "Cette intégration ne fonctionnera probablement pas dans votre navigateur. Veuillez télécharger la version Desktop ou Android de Super Productivity !", "DEFAULT": {"ISSUE_STR": "Problème", "ISSUES_STR": "Problèmes"}, "DEFAULT_PROJECT_DESCRIPTION": "Projet attribué aux tâches créées à partir de problèmes.", "DEFAULT_PROJECT_LABEL": "Projet Super Productivity par défaut", "HOW_TO_GET_A_TOKEN": "Comment obtenir un jeton ?", "ISSUE_CONTENT": {"ASSIGNEE": "Bénéficiaire", "AT": "À", "ATTACHMENTS": "Pièces jointes", "AUTHOR": "<PERSON><PERSON><PERSON>", "CATEGORY": "<PERSON><PERSON><PERSON><PERSON>", "CHANGED": "<PERSON><PERSON><PERSON><PERSON>", "COMMENTS": "Commentaires", "COMPONENTS": "Composants", "DESCRIPTION": "Description", "DONE_RATIO": "<PERSON><PERSON> term<PERSON>", "DUE_DATE": "Date d'échéance", "LABELS": "Étiquettes", "LAST_COMMENT": "<PERSON><PERSON> commentaire", "LIST_OF_CHANGES": "Liste des changements", "LOAD_ALL_COMMENTS": "Charger tous les {{nr}} commentaires", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "Charger la description et tous les commentaires", "LOCATION": "Emplacement", "MARK_AS_CHECKED": "Marquer les mises à jour comme vérifiées", "ON": "sur", "PRIORITY": "Priorité", "RELATED": "<PERSON><PERSON>", "START": "D<PERSON>but", "STATUS": "Statut", "STORY_POINTS": "Points d'histoire", "SUB_TASKS": "Sous-tâches", "SUMMARY": "Résumé", "TIME_SPENT": "Te<PERSON> passé", "TYPE": "Type", "VERSION": "Version", "WORKLOG": "Journal de travail", "WRITE_A_COMMENT": "Écrire un commentaire"}, "S": {"ERR_GENERIC": "{{issue<PERSON><PERSON><PERSON><PERSON>ame}} Erreur : {{errTxt}}", "ERR_NETWORK": "{{issue<PERSON><PERSON><PERSON><PERSON><PERSON>}}: la demande a échoué en raison d'une erreur réseau côté client", "ERR_NOT_CONFIGURED": "{{issue<PERSON><PERSON>ider<PERSON>ame}}: pas correctement configuré", "IMPORTED_MULTIPLE_ISSUES": "{{issueProviderName}}: {{nr}} nouveau {{issuesStr}} importé dans le backlog", "IMPORTED_SINGLE_ISSUE": "{{issueProviderName}}: {{issueStr}} \"{{issueTitle}}\" importé dans le backlog", "ISSUE_DELETED_OR_CLOSED": "{{issueProviderName}}: {{issueStrC}} \"{{issueTitle}}\" semble être supprimé ou fermé", "ISSUE_NO_UPDATE_REQUIRED": "{{issue<PERSON><PERSON><PERSON><PERSON>ame}}: aucune mise à jour requise", "ISSUE_UPDATE_MULTIPLE": "{{issueProviderName}}: données mises à jour pour {{nr}} {{issuesStr}}", "ISSUE_UPDATE_SINGLE": "{{issueProviderName}}: données mises à jour pour \"{{issueTitle}}\"", "MANUAL_UPDATE_ISSUE_SUCCESS": "{{issueProviderName}}: données mises à jour pour \"{{issueTitle}}\"", "MISSING_ISSUE_DATA": "{{issueProviderName}}: tâches avec des données {{issueStr}} manquantes trouvées. Rechargement.", "NEW_COMMENT": "{{issueProviderName}}: Nouveau commentaire pour \"{{issueTitle}}\"", "POLLING_BACKLOG": "{{issueProviderName}}: interrogation pour le nouveau {{issuesStr}}", "POLLING_CHANGES": "{{issueProviderName}}: interrogation des modifications pour {{issuesStr}}"}}, "JIRA": {"BANNER": {"BLOCK_ACCESS_MSG": "Jira : Pour éviter le blocage de l'API, l'accès a été bloqué par Super Productivity. Vous devriez probablement vérifier vos paramètres Jira !", "BLOCK_ACCESS_UNBLOCK": "Débloquer"}, "CFG_CMP": {"ALWAYS_ASK": "Toujours ouvrir le dialogue", "DO_NOT": "Ne pas faire de transitions", "DONE": "Statut pour terminer la tâche", "ENABLE": "Activer l'intégration de Jira", "ENABLE_TRANSITIONS": "Activer la gestion des transitions", "IN_PROGRESS": "Statut de la tâche démarrée", "LOAD_SUGGESTIONS": "Charger les suggestions", "MAP_CUSTOM_FIELDS": "Mapper les champs personnalisés", "MAP_CUSTOM_FIELDS_INFO": "Malheureusement, certaines données de Jira sont enregistrées dans des champs personnalisés, qui sont différents pour chaque installation. Si vous souhaitez inclure ces données, vous devez sélectionner le champ personnalisé approprié.", "OPEN": "Statut pour les tâche en pause", "SELECT_ISSUE_FOR_TRANSITIONS": "Sélectionnez le ticket pour charger les transistions disponibles", "STORY_POINTS": "Story points", "TRANSITION": "Gestion des transitions"}, "DIALOG_CONFIRM_ASSIGNMENT": {"MSG": "<strong>{{summary}}</strong> est actuellement affecté à <strong>{{assignee}}</strong>. Voulez-vous vous l'attribuer ?", "OK": "Oui!"}, "DIALOG_INITIAL": {"TITLE": "Configurer Jira pour le projet"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "Choisissez le statut à attribuer", "CURRENT_ASSIGNEE": "Responsable actuel:", "CURRENT_STATUS": "Statut actuel:", "TASK_NAME": "Nom de la tâche:", "TITLE": "Jira: <PERSON><PERSON> à jour le statut", "UPDATE_STATUS": "Mettre à jour le statut"}, "DIALOG_WORKLOG": {"CHECKBOXES": {"ALL_TIME": "Utilisez toujours tout le temps passé sur la tâche par défaut", "ALL_TIME_MINUS_LOGGED": "Utilisez toujours uniquement le temps passé moins le temps enregistré par défaut", "TIME_SPENT_TODAY": "N'utilisez toujours que le temps passé aujourd'hui par défaut", "TIME_SPENT_YESTERDAY": "Toujours utiliser uniquement le temps passé hier par défaut"}, "CURRENTLY_LOGGED": "Temps de travail actuel:", "INVALID_DATE": "La valeur entrée n'est pas une date!", "SAVE_WORKLOG": "Enregistrer le temps de travail", "STARTED": "<PERSON><PERSON><PERSON><PERSON>", "SUBMIT_WORKLOG_FOR": "Soumettre à Jira le temps de travail pour", "TIME_SPENT": "Te<PERSON> passé", "TIME_SPENT_TOOLTIP": "Ajouter des heures différentes", "TITLE": "Jira: <PERSON><PERSON><PERSON><PERSON> le temps de travail"}, "FORM_ADV": {"AUTO_ADD_BACKLOG_JQL_QUERY": "JQL utilisé pour ajouter des tâches automatiquement au backlog", "IS_ADD_WORKLOG_ON_SUB_TASK_DONE": "<PERSON><PERSON><PERSON><PERSON><PERSON> la boîte de dialogue pour soumettre à Jira le temps de travail lorsque la sous-tâche est terminée", "IS_CHECK_TO_RE_ASSIGN_TICKET_ON_TASK_START": "Vérifier si le ticket actuellement traité est affecté à l'utilisateur actuel", "IS_WORKLOG_ENABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON> la boîte de dialogue pour soumettre à jira le temps de travail lorsque la tâche est terminée", "SEARCH_JQL_QUERY": "Requête JQL pour limiter la recherche des tâches", "WORKLOG_DEFAULT_ALL_TIME": "Remp<PERSON><PERSON><PERSON> tout le temps passé sur la tâche", "WORKLOG_DEFAULT_ALL_TIME_MINUS_LOGGED": "Remplis<PERSON><PERSON> tout le temps passé moins le temps enregistré", "WORKLOG_DEFAULT_TIME_MODE": "Valeur de temps par défaut pour le dialogue", "WORKLOG_DEFAULT_TODAY": "Ne remplissez que le temps passé aujourd'hui", "WORKLOG_DEFAULT_YESTERDAY": "Remp<PERSON>r uniquement le temps passé hier"}, "FORM_CRED": {"ALLOW_SELF_SIGNED": "Autoriser le certificat auto-signé", "HOST": "Hôte (par exemple: http://my-host.de:1234)", "PASSWORD": "Jeton / mot de passe", "USE_PAT": "Utiliser le jeton d'accès personnel au lieu du mot de passe", "USER_NAME": "Email / Nom d'utilisateur", "WONKY_COOKIE_MODE": "Authentification de secours Wonky Cookie (application de bureau uniquement)"}, "FORM_SECTION": {"ADV_CFG": "Configuration avancée", "HELP_ARR": {"H1": "configuration de base", "H2": "Paramètres du backlog", "H3": "Transitions par défaut", "P1_1": "Veuillez fournir un login (qui se trouve sur votre page de profil) et un <a target=\"_blank\" href=\"https://confluence.atlassian.com/cloud/api-tokens-938839638.html\" target=\"_blank\">jeton d'API</a> ou un mot de passe si vous ne pouvez pas en générer un pour une raison quelconque. Veuillez noter que les versions les plus récentes de jira ne fonctionnent parfois qu'avec le jeton.", "P1_2": "Vous devez également spécifier une requête JQL utilisée pour les suggestions permettant d'ajouter des tâches à partir de Jira. Si vous avez besoin d’aide, consultez ce lien <a target=\"_blank\" href=\"https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html\" target=\"_blank\">https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html</a>.", "P1_3": "Vous pouvez également configurer si vous souhaitez ajouter automatiquement (par exemple, chaque fois que vous consultez la vue Planification), toutes les nouvelles tâches spécifiées par une requête JQL personnalisée au backlog.", "P1_4": "Une autre option est \"Vérifier si le ticket actuel est attribué à l'utilisateur actuel\". Si activé et que vous démarrez un ticket, il sera vérifié que vous êtes actuellement affecté à ce ticket sur Jira. Sinon, un dialogue apparaît dans lequel vous pouvez choisir de vous attribuer le ticket.", "P2_1": "Plusieurs options permettent de déterminer quand et comment vous souhaitez soumettre le temps de travail. L'activation de <em>'Ouvrir la boîte de dialogue pour soumettre à jira le temps de travail lorsque la tâche est terminée'</em> ouvre une boîte de dialogue permettant d'ajouter le temps de travail chaque fois que vous marquez une tâche Jira comme terminée. Gardez donc à l'esprit que le temps de travail s'ajoutera à tout ce qui a été suivi jusqu'à présent. Ainsi, si vous marquez une tâche comme terminée pour une seconde fois, vous ne voudrez peut-être pas soumettre à nouveau le temps complet travaillé pour la tâche.", "P2_2": "<em>'Ouv<PERSON>r la boîte de dialogue du temps de travail lorsque la sous-tâche est terminée et non pas pour les tâches avec sous-tâches'</em> ouvre une boîte pour le temps de travail chaque fois que vous marquez une sous-tâche d'un ticket Jira comme terminée. Comme vous suivez déjà votre temps via les sous-tâches, aucune boîte de dialogue ne s'ouvre lorsque vous indiquez que la tâche Jira est terminée.", "P2_3": "<em>\"Envoyer les mises à jour du temps de travail automatiquement sans boîte de dialogue\"</em> fait ce qu'il dit. Comme marquer une tâche plusieurs fois entraîne le suivi complet du temps de travail complet à deux reprises, ceci n'est pas recommandé.", "P3_1": "<PERSON><PERSON>, vous pouvez reconfigurer vos transitions par défaut. Jira permet une configuration étendue de transitions entrant généralement en action sous forme de colonnes différentes sur votre tableau agile Jira. Nous ne pouvons donc pas supposer où et quand transiter vos tâches et vous devez le définir manuellement."}}, "ISSUE_CONTENT": {"ASSIGNEE": "Responsable", "AT": "à", "ATTACHMENTS": "Pièces jointes", "CHANGED": "<PERSON><PERSON><PERSON><PERSON>", "COMMENTS": "commentaires", "COMPONENTS": "composants", "DESCRIPTION": "Description", "LIST_OF_CHANGES": "Liste des modifications", "MARK_AS_CHECKED": "Mar<PERSON> les mises à jour comme cochées", "ON": "sur", "RELATED": "En rapport", "STATUS": "Statut", "STORY_POINTS": "Story points", "SUB_TASKS": "Sous-tâches", "SUMMARY": "Résumé", "WORKLOG": "Temps de travail", "WRITE_A_COMMENT": "Écrire un commentaire"}, "S": {"ADDED_WORKLOG_FOR": "Jira: temps de travail ajouté pour {{issueKey}}", "EXTENSION_NOT_LOADED": "Extension Super Productivity non chargée. Recharger la page peut aider", "INSUFFICIENT_SETTINGS": "Réglages insuffisants pour Jira", "INVALID_RESPONSE": "Jira : la réponse contenait des données non valides", "ISSUE_NO_UPDATE_REQUIRED": "<PERSON><PERSON>: \"{{issueText}}\" déj<PERSON> à jour", "MANUAL_UPDATE_ISSUE_SUCCESS": "Jira: données mises à jour pour \"{{issueText}}\"", "MISSING_ISSUE_DATA": "Jira: Tâches avec données du ticket manquantes trouvées. Rechargement.", "NO_AUTO_IMPORT_JQL": "Jira: aucune requête de recherche définie pour l'importation automatique", "NO_VALID_TRANSITION": "Jira: Aucune transition valide n'est configurée", "TIMED_OUT": "Jira: la demande a expiré", "TRANSITION": "<PERSON>ra: passer le ticket \"{{issue<PERSON>ey}}\" à \"{{name}}\"", "TRANSITION_SUCCESS": "Jira: Passer le ticket {{issue<PERSON><PERSON>}} à <strong>{{chosenTransition}}</strong>.", "TRANSITIONS_LOADED": "Jira: Transitions chargées. Utilisez les listes ci-dessous pour les assigner", "UNABLE_TO_REASSIGN": "Jira: Impossible de réaffecter le ticket à vous-même, car vous n'avez pas spécifié de nom d'utilisateur. veuillez visitez les paramètres."}, "STEPPER": {"CREDENTIALS": "Identifiants", "DONE": "Vous êtes paré!", "LOGIN_SUCCESS": "Connexion réussie!", "TEST_CREDENTIALS": "Tester les identifiants", "WELCOME_USER": "Bienvenue {{user}}!"}}, "MARKDOWN_PASTE": {"CONFIRM_ADD_TO_SUB_TASK_NOTES": "Ajouter la liste markdown collée aux notes de la sous-tâche \"{{parentTaskTitle}}\" ?", "CONFIRM_PARENT_TASKS": "<PERSON><PERSON><PERSON> <strong>{{tasksCount}} nouvelles tâches</strong> à partir de la liste markdown collée ?", "CONFIRM_PARENT_TASKS_WITH_SUBS": "<PERSON><PERSON><PERSON> <strong>{{tasksCount}} nouvelles tâches et {{subTasksCount}} sous-tâches</strong> à partir de la liste markdown collée ?", "CONFIRM_SUB_TASKS": "<PERSON><PERSON><PERSON> {{tasksCount}} nouvelles sous-tâches à partir de la liste markdown collée ?", "CONFIRM_SUB_TASKS_WITH_PARENT": "<PERSON><PERSON><PERSON> <strong>{{tasksCount}} nouvelles sous-tâches sous \"{{parentTaskTitle}}\"</strong> à partir de la liste markdown collée ?", "DIALOG_TITLE": "Liste Markdown collée détectée !"}, "METRIC": {"BANNER": {"CHECK": "Je l'ai fait !"}, "CMP": {"AVG_BREAKS_PER_DAY": "<PERSON><PERSON>. <PERSON> par jour", "AVG_TASKS_PER_DAY_WORKED": "Moy. tâches par jour travaillées", "AVG_TIME_SPENT_ON_BREAKS": "Moy. temps consacré aux pauses", "AVG_TIME_SPENT_PER_DAY": "Moy. temps passé par jour", "AVG_TIME_SPENT_PER_TASK": "Moy. temps passé par tâche", "COUNTING_SUBTASKS": "(compter les sous-tâches)", "DAYS_WORKED": "Jours travaill<PERSON>", "GLOBAL_METRICS": "Mesures globales", "IMPROVEMENT_SELECTION_COUNT": "Nombre de fois qu'un facteur d'amélioration a été sélectionné", "MOOD_PRODUCTIVITY_OVER_TIME": "Humeur et productivité dans le temps", "NO_ADDITIONAL_DATA_YET": "Aucune donnée supplémentaire collectée pour l'instant. Utilisez le formulaire du panneau \"Evaluation\" du résumé quotidien pour le faire.", "OBSTRUCTION_SELECTION_COUNT": "Nombre de fois qu'un facteur d'obstruction a été sélectionné", "SIMPLE_CLICK_COUNTERS_OVER_TIME": "Compteurs de clics au fil du temps", "SIMPLE_COUNTERS": "Compteurs simples", "SIMPLE_STOPWATCH_COUNTERS_OVER_TIME": "Compteurs de chronomètre au fil du temps", "TASKS_DONE_CREATED": "Tâches (effectuées / créées)", "TIME_ESTIMATED": "Temps estimé", "TIME_SPENT": "Te<PERSON> passé"}, "EVAL_FORM": {"ADD_NOTE_FOR_TOMORROW": "Ajouter une note pour demain", "DISABLE_REPEAT_EVERY_DAY": "Désactiver la répétition tous les jours", "ENABLE_REPEAT_EVERY_DAY": "<PERSON><PERSON><PERSON><PERSON><PERSON> tous les jours", "HELP_H1": "Pourquoi devrais-je m'en soucier ?", "HELP_LINK_TXT": "Aller à la section des métriques", "HELP_P1": "Il est temps de faire une petite auto-évaluation ! Vos réponses ici sont sauvegardées et vous fournissent un peu de statistiques sur la façon dont vous travaillez dans la section des métriques. De plus, les suggestions pour demain apparaîtront au-dessus de votre liste de tâches le lendemain.", "HELP_P2": "Ceci est n'est pas destiné à calculer des métriques exactes ou à devenir efficace comme une machine dans tous vos travaux mais plutôt à améliorer votre perception de votre travail. Il peut être utile d’évaluer les points négatifs dans votre routine quotidienne et de rechercher des facteurs qui vous aident. En étant un peu systématiques à ce sujet, nous espérons pouvoir mieux les maîtriser et améliorer ce que vous pouvez.", "IMPROVEMENTS": "Qu'est-ce qui a amélioré votre productivité ?", "IMPROVEMENTS_TOMORROW": "Que pourriez-vous faire pour améliorer demain ?", "MOOD": "Comment vous sentez-vous?", "MOOD_HINT": "1: <PERSON><PERSON> - 10: <PERSON><PERSON><PERSON><PERSON>", "NOTES": "Notes pour demain", "OBSTRUCTIONS": "Qu'est-ce qui a nui à votre productivité ?", "PRODUCTIVITY": "Avez-vous travaillé efficacement ?", "PRODUCTIVITY_HINT": "1: N'a même pas commencé - 10: Extrêmement efficace"}, "S": {"SAVE_METRIC": "Métrique enregistrée avec succès"}}, "NOTE": {"D_ADD": {"NOTE_LABEL": "Entrez le texte à enregistrer en tant que note ..."}, "D_FULLSCREEN": {"VIEW_PARSED": "Afficher comme Markdown parsé (non modifiable)", "VIEW_SPLIT": "Afficher les fichiers Markdown parsés et non parsés dans la vue fractionnée", "VIEW_TEXT_ONLY": "Afficher comme texte non parsé"}, "NOTE_CMP": {"DISABLE_PARSE": "Désactiver le markdown", "ENABLE_PARSE": "Activer le markdown"}, "NOTES_CMP": {"ADD_BTN": "Ajouter une nouvelle note", "DROP_TO_ADD": "Dé<PERSON>r ici pour ajouter une nouvelle note", "NO_NOTES": "Il n'y a actuellement aucune note"}, "S": {"NOTE_ADDED": "Note sauvegardée"}}, "OPEN_PROJECT": {"CFG_CMP": {"ALWAYS_ASK": "Toujours ouvrir la boîte de dialogue", "DO_NOT": "<PERSON>e pas transitionner", "DONE": "Statut d'achèvement de la tâche", "ENABLE": "Activer l'intégration Openproject", "ENABLE_TRANSITIONS": "Activer la gestion de transition", "IN_PROGRESS": "Statut pour le démarrage de la tâche", "OPEN": "Statut pour la mise en pause de la tâche", "PROGRESS_ON_SAVE": "Progression par défaut lors de la sauvegarde", "SELECT_ISSUE_FOR_TRANSITIONS": "Sélectionnez un ticket pour charger les transitions disponibles", "TRANSITION": "Gestion de transition"}, "DIALOG_INITIAL": {"TITLE": "Configurer OpenProject pour le projet"}, "DIALOG_TRACK_TIME": {"ACTIVITY": "activité", "CURRENTLY_LOGGED": "Heure actuellement connectée:", "INVALID_DATE": "La valeur entrée n'est pas une date!", "POST_TIME": "Heure de publication", "STARTED": "commencé", "SUBMIT_TIME_FOR": "Soumettre du temps à OpenProject pour", "TIME_SPENT": "Te<PERSON> passé", "TITLE": "OpenProject : Soumettre le journal de travail"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "Choisissez le statut à attribuer", "CURRENT_ASSIGNEE": "Responsable actuel :", "CURRENT_STATUS": "Statut actuel :", "PERCENTAGE_DONE": "Avancement :", "TASK_NAME": "Nom de la tâche", "TITLE": "OpenProject : <PERSON><PERSON> à jour du statut", "UPDATE_STATUS": "Mettre à jour le statut"}, "FORM": {"FILTER_USER": "Nom d'utilisateur (par exemple, pour filtrer vous-même les modifications)", "HOST": "<PERSON><PERSON><PERSON><PERSON> (ex : https://www.openproject.org/)", "IS_SHOW_TIME_TRACKING_DIALOG": "Afficher la boîte de dialogue de suivi du temps à signaler à OpenProject", "IS_SHOW_TIME_TRACKING_DIALOG_DESCRIPTION": "Nécessite l'activation du module de suivi du temps pour le projet OpenProject", "IS_SHOW_TIME_TRACKING_DIALOG_FOR_EACH_SUB_TASK": "Afficher la boîte de dialogue de suivi du temps lorsque les sous-tâches sont terminées", "PROJECT_ID": "identifiant du projet", "PROJECT_ID_DESCRIPTION": "Peut être trouvé dans l'url, lors de la visualisation du projet dans le navigateur.", "SCOPE": "Portée", "SCOPE_ALL": "<PERSON>ut", "SCOPE_ASSIGNED": "<PERSON><PERSON><PERSON> à moi", "SCOPE_CREATED": "<PERSON><PERSON><PERSON> par moi", "TOKEN": "<PERSON>on d'a<PERSON>ès"}, "FORM_SECTION": {"HELP": "<p><PERSON><PERSON>, vous pouvez configurer SuperProductivity pour répertorier les packages de travail OpenProject ouverts. Veuillez noter que pour que cela fonctionne dans le navigateur, vous devez probablement configurer CORS pour votre serveur OpenProject, pour autoriser l'accès depuis app.super-productivity.com</p>", "TITLE": "Projet ouvert"}, "ISSUE_CONTENT": {"ASSIGNEE": "Cessionnaire", "ATTACHMENTS": "Pièces jointes", "DESCRIPTION": "La description", "MARK_AS_CHECKED": "Mar<PERSON> les mises à jour comme cochées", "STATUS": "Statut", "SUMMARY": "Résumé", "TYPE": "Type", "UPLOAD_ATTACHMENT": "Téléverser dans la tâche"}, "ISSUE_STRINGS": {"ISSUE_STR": "Lot de travaux", "ISSUES_STR": "lots de travaux"}, "S": {"ERR_NO_FILE": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "ERR_UNKNOWN": "OpenProject : erreur inconnue {{statusCode}} {{errorMsg}}. CORS est-il correctement configuré pour le serveur ?", "POST_TIME_SUCCESS": "OpenProject : entrée de temps créée avec succès pour {{issueTitle}}", "TRANSITION": "OpenProject : d<PERSON><PERSON><PERSON> le ticket \"{{issueKey}}\" sur \"{{name}}\"", "TRANSITION_SUCCESS": "OpenProject : d<PERSON><PERSON><PERSON> le ticket {{issueKey}} sur <strong>{{chosenTransition}}</strong>", "TRANSITIONS_LOADED": "Transitions chargées. Utilisez les sélections ci-dessous pour les attribuer"}}, "PLANNER": {"D": {"ADD_PLANNED": {"ADD_TO_TODAY": "A<PERSON><PERSON> à Aujourd'hui", "RE_PLAN_ALL": "Tout reprogrammer", "TITLE": "Ajouter les tâches planifiées à Aujourd'hui"}}, "EDIT_REPEATED_TASK": "Modifier la tâche répétée '{{taskName}}'", "NO_TASKS": "Pas de t<PERSON>che", "PLAN_VIEW": {"NO_SCHEDULED_ITEMS": "Aucun élément programmé"}, "S": {"REMOVED_PLAN_DATE": "Date de planification supprimée pour la tâche \"{{taskTitle}}\"", "TASK_ALREADY_PLANNED": "La tâche est déjà plannifiée le {{date}", "TASK_PLANNED_FOR": "Tâche planifiée pour {date}"}, "TASK_DRAWER": "Tiroir des tâches"}, "POMODORO": {"BACK_TO_WORK": "Au travail !", "BREAK_IS_DONE": "Votre pause est terminée !", "ENJOY_YOURSELF": "<PERSON><PERSON><PERSON>-vous, bougez, revenez dans :", "FINISH_SESSION_X": "Vous avez terminé avec succès la session <strong>{{nr}}</strong> !", "NOTIFICATION": {"BREAK_TIME": "Pomodoro: Temps pour la pause {{nr}}!", "BREAK_X_START": "Pomodoro : <PERSON> pause {{nr}} a commencée !", "NO_TASKS": "Vous devez ajouter des tâches avant que la minuterie Pomodoro puisse démarrer.", "SESSION_X_START": "Pomodoro : La session {{nr}} a commencée !"}, "S": {"RESET": "<PERSON><PERSON><PERSON><PERSON> la <PERSON> Pomodoro", "SESSION_SKIP": "Passer la <PERSON> <PERSON><PERSON><PERSON><PERSON>", "SESSION_X_START": "Pomodoro : La session {{nr}} a commencée !"}, "SKIP_BREAK": "Passer la pause", "START_BREAK": "<PERSON><PERSON><PERSON><PERSON> la <PERSON>"}, "PROCRASTINATION": {"BACK_TO_WORK": "Au travail !", "COMP": {"INTRO": "Faire preuve d'indulgence est toujours une bonne idée. Ça améliore l'estime de soi, favorise les émotions positives et peut vous aider à surmonter la procrastination, bien sûr. Essayez un petit exercice:", "L1": "Asseyez-vous quelques instants et étirez-vous, en d'autres termes, détendez-vous", "L2": "Essayez d'écouter les pensées et les sentiments qui émergent", "L3": "Agis<PERSON>z-vous avec vous-même de la même manière que vous agiriez avec un ami ?", "L4": "Si la réponse est non, imaginez votre ami dans votre situation. Que lui diriez-vous? Que feriez-vous pour eux ?", "OUTRO": "Vous trouverez plus d'exercices <a target=\"_blank\" href=\"https://drsoph.com/blog/2018/9/17/3-exercises-in-self-compassion\" target=\"_blank\">ici</a> ou sur <a target=\"_blank\" href=\"https://www.google.com/search?q=self+compassion+exercises&oq=self+compassion+excers&aqs=chrome.1.69i57j0l5.4303j0j7&sourceid=chrome&ie=UTF-8\" target=\"_blank\">google</a>.", "TITLE": "Indulgence avec soi-même"}, "CUR": {"INTRO": "La procrastination est intéressante, n'est-ce pas? Cela n'a pas de sens de le faire. Pas du tout dans votre intérêt à long terme. Mais tout le monde le fait quand même. Profitez et explorez!", "L1": "Quels sentiments suscitent votre tentation de procrastiner?", "L2": "Où les sens-tu dans ton corps?", "L3": "Qu'est-ce qu'ils vous rappellent?", "L4": "Qu'advient-il de l'envie de procrastiner quand vous l'observez? Ça l'intensifie? La dissipe? Ça cause d'autres émotions?", "L5": "Comment les sensations dans votre corps changent-elles pendant que vous continuez à concentrer votre conscience sur elles?", "PROCRASTINATION_TRIGGERS_TEXT": "Une autre méthode très efficace consiste à noter ce qui a déclenché votre envie de procrastiner. Par exemple, j'ai souvent envie d'aller rapidement sur Reddit ou sur mon site d'actualités préféré dès que la fenêtre de mon navigateur apparaît. Depuis que j'ai commencé à noter mes déclencheurs dans un simple document texte vide, j'ai pris conscience de l'enracinement de ce pattern et cela m'a aidé à expérimenter différentes contre-mesures.", "PROCRASTINATION_TRIGGERS_TITLE": "Notez vos déclencheurs de procrastination", "TITLE": "Curiosité"}, "H1": "Prenez du temps!", "INTRO": {"AVOIDING": "<PERSON><PERSON><PERSON> t<PERSON>che", "FEAR": "Peur de l'échec", "STRESSED": "Stress<PERSON> de ne pas terminer le travail", "TITLE": "Introduction"}, "P1": "Tout d'abord détendez-vous! Tout le monde le fait de temps en temps. Et si vous ne faites pas ce que vous devriez, vous devriez au moins en profiter! Ensuite, consultez les sections ci-dessous pour quelque chose d'utile.", "P2": "Rappelez-vous: la procrastination est un problème de régulation des émotions, pas un problème de gestion du temps.", "REFRAME": {"INTRO": "Pensez à ce qui pourrait être positif à propos de la tâche malgré elle.", "TITLE": "Recadrage", "TL1": "Qu'est-ce qui pourrait être intéressant?", "TL2": "Quel est l'avantage de la compléter?", "TL3": "Comment vous sentirez-vous si vous la complétez?"}, "SPLIT_UP": {"INTRO": "Divisez la tâche en autant de petits morceaux que vous pouvez.", "OUTRO": "Terminé? Alors ré<PERSON>-y. Quelle serait - strictement théorique - la première chose que vous feriez <i>si</i> vous commenciez à travailler sur la tâche? Pensez-y bien ...", "TITLE": "Découpez-la!"}}, "PROJECT": {"D_CREATE": {"CREATE": "Créer un projet", "EDIT": "Modifier le projet", "SETUP_CALDAV": "Configurer l'intégration Caldav", "SETUP_GIT": "Configurer l'intégration de GitHub", "SETUP_GITEA_PROJECT": "Mettre en place l'intégration Gitea", "SETUP_GITLAB": "Configurer l'intégration de GitLab", "SETUP_JIRA": "Configurer l'intégration de Jira", "SETUP_OPEN_PROJECT": "Configurer l'intégration OpenProject", "SETUP_REDMINE_PROJECT": "Mettre en place l'intégration Redmine"}, "D_DELETE": {"MSG": "Êtes-vous sûr de vouloir supprimer le projet \"{title}\" ?"}, "FORM_BASIC": {"L_ENABLE_BACKLOG": "Activer la Backlog de Projet", "L_IS_HIDDEN_FROM_MENU": "Masquer le projet dans le menu", "L_TITLE": "Nom du projet", "TITLE": "Paramètres de base"}, "FORM_THEME": {"D_IS_DARK_THEME": "Ne sera pas utilisé si le système prend en charge le mode sombre global.", "HELP": "Paramètres du thème pour votre projet.", "L_BACKGROUND_IMAGE_DARK": "URL de l'image d'arrière-plan (thème sombre)", "L_BACKGROUND_IMAGE_LIGHT": "URL de l'image d'arrière-plan (thème clair)", "L_COLOR_ACCENT": "Couleur d'accentuation", "L_COLOR_PRIMARY": "Couleur principale", "L_COLOR_WARN": "Couleur d'avertissement / erreur", "L_HUE_ACCENT": "Seuil pour le texte sombre sur l'arrière-plan de la couleur d'accentuation", "L_HUE_PRIMARY": "Seuil pour le texte sombre sur fond de la couleur principale", "L_HUE_WARN": "Seuil pour le texte sombre sur l'arrière-plan de la couleur d'avertissement", "L_IS_AUTO_CONTRAST": "Réglage automatique des couleurs du texte pour une meilleure lisibilité", "L_IS_DISABLE_BACKGROUND_GRADIENT": "Désactiver le dégradé d'arrière-plan coloré", "L_IS_REDUCED_THEME": "Utiliser une interface utilisateur réduite (aucune bordure autour des tâches)", "L_THEME_COLOR": "<PERSON><PERSON>ur du thème", "L_TITLE": "Titre", "TITLE": "Thème"}, "S": {"ARCHIVED": "Projet archivé", "CREATED": "Projet <strong>{{title}}</strong>  c<PERSON><PERSON>. Vous pouvez le sélectionner dans le menu en haut à gauche.", "DELETED": "Projet supprimé", "E_EXISTS": "Le projet \"{{title}}\" existe déjà", "E_INVALID_FILE": "Données invalides pour le fichier de projet", "ISSUE_PROVIDER_UPDATED": "Paramètres de projet mis à jour pour <strong>{{issueProviderKey}}</strong>", "UNARCHIVED": "Projet non archivé", "UPDATED": "Paramètres du projet mis à jour"}}, "QUICK_HISTORY": {"NO_DATA": "Pas de données pour l'année en cours", "PAGE_TITLE": "Historique rapide", "WEEK_TITLE": "Semaine {{nr}} ({{timeSpent}})"}, "REDMINE": {"DIALOG_INITIAL": {"TITLE": "Configurer Redmine pour le projet"}, "FORM": {"API_KEY": "clé d'accès à l'API", "HOST": "<PERSON><PERSON><PERSON> (ex : https://redmine.org)", "PROJECT_ID": "Identifiant du projet", "PROJECT_ID_DESCRIPTION": "Peut être trouvé dans une partie de l'URL, lors de la visualisation du projet dans le navigateur.", "SCOPE": "Portée", "SCOPE_ALL": "<PERSON>ut", "SCOPE_ASSIGNED": "<PERSON><PERSON><PERSON> à moi-même", "SCOPE_CREATED": "<PERSON><PERSON><PERSON> par moi"}, "FORM_SECTION": {"HELP": "<p><PERSON><PERSON>, vous pouvez configurer SuperProductivity pour répertorier les tickets Redmine ouverts (soit la version en ligne, soit une instance auto-hébergée) pour un projet spécifique dans le panneau de création de tâches de la vue de planification quotidienne. Ils seront répertoriés sous forme de suggestions et fourniront un lien vers le problème ainsi que plus d'informations à ce sujet.</p><p>Vous pouvez aussi importer automatiquement tous les problèmes ouverts.</p>", "TITLE": "Redmine"}, "ISSUE_CONTENT": {"AUTHOR": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "Description", "MARK_AS_CHECKED": "Mar<PERSON> les mises à jour comme cochées", "PRIORITY": "Priorité", "STATUS": "Statut"}, "S": {"ERR_UNKNOWN": "Redmine : erreur inconnue {{statusCode}} {{errorMsg}}."}}, "REMINDER": {"COUNTDOWN_BANNER": {"HIDE": "<PERSON><PERSON><PERSON><PERSON>", "START_NOW": "Commencer maintenant", "TXT": "<strong>{{title}}</strong> commence à <strong>{{start}}</strong> !", "TXT_MULTIPLE": "<strong>{{title}}</strong> commence à <strong>{{start}}</strong> !<br> (et {{nrOfOtherBanners}} autres tâches sont dues)"}, "S_ACTIVE_TASK_DUE": "La tâche sur laquelle vous travaillez actuellement est arrivée à échéance !<br/> ({{title}})", "S_REMINDER_ERR": "Erreur pour l'interface de rappel"}, "SCHEDULE": {"CONTINUED": "A continué", "D_INITIAL": {"TEXT": "<p>L'idée de la chronologie est de fournir une meilleure image de la façon dont les tâches planifiées se déroulent au fil du temps. Il est automatiquement généré à partir de vos tâches et distingue deux choses différentes: <strong>les tâches planifiées</strong>, qui s'affichent à l'heure prévue et les <strong>tâches régulières</strong> qui doivent s'articuler autour de ces événements fixes. Toutes les tâches tiennent compte des estimations de temps que vous leur avez assignées.</p><p>En plus de cela, vous pouvez également fournir une heure de début et de fin du travail. Si elles sont configurées, les tâches régulières n'apparaîtront jamais en dehors de celles-ci. Veuillez noter que la chronologie ne comprend que les 30 jours à venir.</p>", "TITLE": "Chronologie"}, "END": "Fin de travail", "LUNCH_BREAK": "<PERSON>use dé<PERSON>r", "NO_TASKS": "Il n'y a actuellement aucune tâche. Veuillez ajouter quelques tâches via le bouton + dans la barre supérieure.", "NOW": "À présent", "PLAN_END_DAY": "Planifier à la fin de la journée", "PLAN_START_DAY": "Planifier au début de la journée", "START": "Début des travaux", "TASK_PROJECTION_INFO": "Projection future d'une tâche répétable planifiée"}, "SEARCH_BAR": {"INFO": "Cliquez sur l'icône de la liste pour rechercher les tâches archivées", "INFO_ARCHIVED": "Cliquez sur l'icône d'archive pour rechercher les tâches normales", "NO_RESULTS": "Aucune tâche trouvée correspondant à votre recherche", "PLACEHOLDER": "Rechercher une tâche ou une description de tâche", "PLACEHOLDER_ARCHIVED": "Rechercher des tâches archivées", "TOO_MANY_RESULTS": "Trop de résultats, veuillez affiner votre recherche"}, "SIMPLE_COUNTER": {"D_CONFIRM_REMOVE": {"MSG": "La suppression d'un simple compteur supprimera également toutes les données antérieures qui y sont suivies. Êtes-vous sur de vouloir continuer?", "OK": "Fais le!"}, "D_EDIT": {"CURRENT_STREAK": "Série actuelle", "DAILY_GOAL": "Objectif quotidien", "DAYS": "Jours", "L_COUNTER": "<PERSON><PERSON>er"}, "FORM": {"ADD_NEW": "Ajouter un compteur simple", "HELP": "<PERSON><PERSON>, vous pouvez configurer des boutons simples qui apparaîtront en haut à droite. Il peut s'agir de minuteries ou simplement d'un simple compteur, qui est compté, en cliquant dessus.", "L_COUNTDOWN_DURATION": "Durée du compte à rebours", "L_DAILY_GOAL": "Objectif quotidien pour une série de succès", "L_ICON": "Icône", "L_ICON_ON": "Icône lorsque basculé", "L_IS_ENABLED": "Activée", "L_TITLE": "Titre", "L_TRACK_STREAKS": "Trajectoires", "L_TYPE": "Type", "L_WEEKDAYS": "Jours de la semaine pour vérifier la présence d'un streak", "TITLE": "Compteurs simples", "TYPE_CLICK_COUNTER": "<PERSON><PERSON><PERSON> sur Counter", "TYPE_REPEATED_COUNTDOWN": "<PERSON><PERSON><PERSON><PERSON><PERSON> le compte à rebours", "TYPE_STOPWATCH": "Chronomètre"}, "S": {"GOAL_REACHED_1": "Vous avez atteint votre objectif pour aujourd'hui !", "GOAL_REACHED_2": "Du<PERSON>e de la séquence actuelle :"}}, "SYNC": {"A": {"ARCHIVE_ONLY_UPLOADED": "Vos données ont été partiellement importées. <PERSON><PERSON><PERSON> de réessayer plus tard ! Autrement, vous ne serez pas en capacité de synchroniser vos données avec d'autres appareils.", "POSSIBLE_LEGACY_DATA": "Super Productivity a amélioré la synchronisation en utilisant désormais deux fichiers distincts plutôt qu'un seul, ce qui permet un transfert de données beaucoup moins important. Il est recommandé de mettre à jour toutes les instances de Super Productivity et de synchroniser d'abord les données de l'instance d'application où les données sont les plus récentes. S'il s'agit des données de votre appareil local, ignorez cet avertissement et procédez simplement au téléchargement en confirmant la boîte de dialogue suivante.", "REMOTE_MODEL_VERSION_NEWER": "La version du modèle distant est plus récente que celle locale. Veuillez mettre à jour votre application locale vers la dernière version !"}, "C": {"EMPTY_SYNC": "Vous essayez de synchroniser un objet de données vide. Est-ce votre toute première synchronisation d'une application (presque) vierge?", "FORCE_UPLOAD": "Télécharger quand même des données locales?", "FORCE_UPLOAD_AFTER_ERROR": "Une erreur s'est produite lors du téléchargement de vos données locales. Essayez de forcer la mise à jour?", "MIGRATE_LEGACY": "Données héritées détectées lors de l'importation, voulez-vous essayer de les migrer ?", "NO_REMOTE_DATA": "Aucune donnée sur le serveur distant trouvée. Télécharger local vers le serveur distant?", "TRY_LOAD_REMOTE_AGAIN": "Essayez à nouveau de recharger les données depuis le serveur distant?", "UNABLE_TO_LOAD_REMOTE_DATA": "Impossible de charger les données de la base distante. Voulez-vous essayer d'écraser les données distantes avec vos données locales ? Toutes les données distantes seront perdues dans le processus."}, "D_AUTH_CODE": {"FOLLOW_LINK": "Veuillez ouvrir le lien suivant et copier le code d'authentification qui y est fourni dans le champ de saisie ci-dessous.", "GET_AUTH_CODE": "Obtenir le code d'autorisation", "L_AUTH_CODE": "Entrer le code d'authentification", "TITLE": "Connexion: {{provider}}"}, "D_CONFLICT": {"LAMPORT_CLOCK": "Révision", "LAST_CHANGE": "dernier changement:", "LAST_SYNC": "Dernière synchronisation:", "LOCAL": "Local", "LOCAL_REMOTE": "local -> distant", "REMOTE": "Distant", "TEXT": "<p>Mise à jour depuis Dropbox. Les données locales et distantes semblent avoir été modifiées.</p>", "TIMESTAMP": "Horodatage", "TITLE": "Dropbox: donn<PERSON> en conflit", "USE_LOCAL": "Conserver la version local", "USE_REMOTE": "Utiliser le serveur distant"}, "D_DECRYPT_ERROR": {"BTN_OVER_WRITE_REMOTE": "Changer et écraser le distant", "CHANGE_PW_AND_DECRYPT": "Modifier et tenter de déchiffrer", "P1": "Vos données sont cryptées et le décryptage a échoué. Veuillez entrer le mot de passe correct !", "P2": "Vous pouvez également changer votre mot de passe, ce qui écrasera toutes les données distantes.", "PASSWORD": "Mot de passe"}, "D_INCOMPLETE_SYNC": {"BTN_CLOSE_APP": "Femer l'application", "BTN_DOWNLOAD_BACKUP": "Télécharger une sauvegarde locale", "BTN_FORCE_UPLOAD": "Forcer un envoi des données locales", "P1": "Les données de synchronisation distante sont incohérentes !", "P2": "<PERSON><PERSON><PERSON><PERSON> affect<PERSON> :", "P3": "Vous avez 2 options :", "P4": "1. Allez sur votre autre appareil et essayez d'effectuer une synchronisation complète là-bas.", "P5": "2. <PERSON><PERSON><PERSON><PERSON> les données distantes avec vos données locales. Tous les changements distants seront perdus !", "P6": "Il est recommandé de créer une sauvegarde des données que vous écrasez !!!", "T1": "La dernière synchronisation est incomplète !", "T2": "Vos données d'archive n'ont pas été correctement importées au cours de la dernière synchronisation :", "T3": "Vous avez 2 Options :", "T4": "1. Vous rendre sur votre autre appareil et y effectuer la synchronisation.", "T5": "2. <PERSON><PERSON>, vous pouvez écraser les données distantes avec vos données locales. Tous les changements distants seront perdus !", "T6": "La création d'une sauvegarde des données que vous écrasez est recommandée !!!"}, "D_INITIAL_CFG": {"SAVE_AND_ENABLE": "Enregistrer et activer la synchronisation", "TITLE": "Configurer la synchronisation"}, "D_PERMISSION": {"DISABLE_SYNC": "Désactiver la synchronisation", "PERM_FILE": "Donner l'autorisation", "TEXT": "<p>Votre autorisation de fichier pour la synchronisation locale a été révoquée.</p>", "TITLE": "Synchronisation : autorisation de fichier locale refusée"}, "FORM": {"DROPBOX": {"L_ACCESS_TOKEN": "Jeton d'accès (généré à partir du code d'authentification)"}, "GOOGLE": {"L_SYNC_FILE_NAME": "Nom du fichier de synchronisation"}, "L_ENABLE_COMPRESSION": "Activer la compression (transfert de données plus rapide)", "L_ENABLE_ENCRYPTION": "Activer le chiffrement de bout en bout (expérimental) - Rend vos données inacessibles à votre fournisseur de synchronisation", "L_ENABLE_SYNCING": "Activer la synchronisation", "L_ENCRYPTION_NOTES": "REMARQUES IMPORTANTES : Vous devrez définir le même mot de passe sur vos autres appareils <strong>AVANT</strong> la prochaine synchronisation pour permettre un bon fonctionnement. Veuillez sélectionner un mot de passe sécurisé. Veuillez également noter que <strong>vous ne pourrez PAS accéder à vos données si vous oubliez ce mot de passe. Il n'y a AUCUNE récupération possible</strong>, car vous seul possédez la clé. Le chiffrement sera probablement suffisamment bon pour contrecarrer la plupart des attaquants, mais <strong>il n'y a aucune garantie.</strong>", "L_ENCRYPTION_PASSWORD": "Mot de passe de chiffrement (NE PAS OUBLIER)", "L_SYNC_INTERVAL": "Intervalle de synchronisation", "L_SYNC_PROVIDER": "Fournisseur de synchronisation", "LOCAL_FILE": {"L_SYNC_FILE_PATH_PERMISSION_VALIDATION": "Nécessite une autorisation d'accès au fichier", "L_SYNC_FOLDER_PATH": "Synchroniser le chemin du dossier"}, "TITLE": "Sync", "WEB_DAV": {"CORS_INFO": "<strong>Expérimental !!</strong> <PERSON>ur que cela fonctionne, vous devez désactiver ou limiter CORS pour votre instance Nextcloud, ce qui peut avoir des implications négatives sur la sécurité! Veuillez <a href='https://github.com/nextcloud/server/issues/3131'>consulter ce fil de discussion</a> pour plus d'informations. À utiliser à vos risques et périls!", "L_BASE_URL": "URL de base", "L_PASSWORD": "Mot de passe", "L_SYNC_FOLDER_PATH": "Synchroniser le chemin du dossier", "L_USER_NAME": "Nom d'utilisateur"}}, "S": {"ALREADY_IN_SYNC": "<PERSON><PERSON><PERSON><PERSON> synchronis<PERSON>", "ALREADY_IN_SYNC_NO_LOCAL_CHANGES": "Pas de changement local - Déjà synchronisé", "BTN_CONFIGURE": "Configurer", "BTN_FORCE_OVERWRITE": "Forcer l'écrasement", "ERROR_DATA_IS_CURRENTLY_WRITTEN": "Les données distantes sont en cours d'écriture", "ERROR_FALLBACK_TO_BACKUP": "<PERSON><PERSON><PERSON> chose s'est mal passé lors de l'importation des données. Retour à la sauvegarde locale.", "ERROR_INVALID_DATA": "Erreur lors de la synchronisation. Données invalides", "ERROR_NO_REV": "Pas de révision valide pour le fichier distant", "ERROR_UNABLE_TO_READ_REMOTE_DATA": "Erreur lors de la synchronisation. Impossible de lire les données distantes. Peut-être avez-vous activé le chiffrement et votre mot de passe local ne correspond pas à celui utilisé pour chiffrer les données distantes ?", "IMPORTING": "Importer des données", "INCOMPLETE_CFG": "L'authentification pour la synchronisation a échoué. Veuillez vérifier votre configuration!", "INITIAL_SYNC_ERROR": "Échec de la synchronisation initiale", "SUCCESS_DOWNLOAD": "Données synchronisées depuis le distant", "SUCCESS_IMPORT": "Données importées", "SUCCESS_VIA_BUTTON": "Données synchronisées avec succès", "UNKNOWN_ERROR": "Erreur inconnue lors de la synchronisation. Veuillez vérifier la console.", "UPLOAD_ERROR": "Erreur de téléversement inconnue (paramètres corrects?): {{err}}"}}, "TAG": {"D_CREATE": {"CREATE": "<PERSON><PERSON><PERSON> une balise", "EDIT": "Modifier la balise"}, "D_DELETE": {"CONFIRM_MSG": "Voulez-vous vraiment supprimer la balise \"{{tagName}}\"? Il sera supprimé de toutes les tâches. Ça ne peut pas être annulé."}, "D_EDIT": {"ADD": "Ajouter des balises pour \"{{title}}\"", "EDIT": "Modifier les balises pour \"{{title}}\"", "LABEL": "<PERSON>ts clés"}, "FORM_BASIC": {"L_COLOR": "Couleur (si une couleur de thème principale non définie est utilisée)", "L_ICON": "Icône", "L_TITLE": "Nom de la balise", "TITLE": "Paramètres de base"}, "S": {"UPDATED": "Les paramètres des balises ont été mis à jour"}, "TTL": {"ADD_NEW_TAG": "Ajouter une nouvelle balise"}}, "TASK": {"ADD_TASK_BAR": {"ADD_EXISTING_TASK": "Ajouter la tâche existante \"{{taskTitle}}\"", "ADD_ISSUE_TASK": "Ajouter le problème n °{{issueNr}} de {{issueType}}", "ADD_TASK_TO_BOTTOM_OF_BACKLOG": "Ajouter une tâche au bas du backlog", "ADD_TASK_TO_BOTTOM_OF_TODAY": "Ajouter une tâche au bas de la liste", "ADD_TASK_TO_TOP_OF_BACKLOG": "Ajouter une tâche en haut du backlog", "ADD_TASK_TO_TOP_OF_TODAY": "Ajouter une tâche en haut de la liste", "CREATE_TASK": "<PERSON><PERSON><PERSON> une nouvelle tâche", "EXAMPLE": "Exemple: \"Un titre de tâche + projectName # une balise # une autre balise 10m / 3h\"", "START": "Appuyez sur Entrée une fois de plus pour démarrer", "TOGGLE_ADD_TO_BACKLOG_TODAY": "Activer / Désactiver l'ajout de tâche à la backlog / liste d'aujourd'hui", "TOGGLE_ADD_TOP_OR_BOTTOM": "Activer / Désactiver l'ajout de tâche tout en haut & bas de la liste"}, "ADDITIONAL_INFO": {"ADD_ATTACHMENT": "Ajouter une pièce jointe", "ADD_SUB_TASK": "Ajouter une sous-tâche", "ATTACHMENTS": "Pièces jointes {{nr}}", "DUE": "Planifié à", "FROM_PARENT": "(du parent)", "LOCAL_ATTACHMENTS": "Pièces jointes locales", "NOTES": "Notes", "PARENT": "Parent", "REMINDER": "<PERSON><PERSON>", "REPEAT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SCHEDULE_TASK": "Planifier une tâche", "SUB_TASKS": "Sous-tâches ({{nr}})", "TIME": "Temps", "TITLE_PLACEHOLDER": "Entrez un titre"}, "B": {"ADD_HALF_HOUR": "Ajouter 1/2 heure", "ESTIMATE_EXCEEDED": "Estimation de temps dépassée pour \"{{title}}\""}, "CMP": {"ADD_SUB_TASK": "Ajouter une sous-tâche", "ADD_TO_MY_DAY": "A<PERSON>ter à ma journée", "ADD_TO_PROJECT": "Ajouter à un projet", "CONVERT_TO_PARENT_TASK": "Convertir en tâche parent", "DELETE": "Supp<PERSON>er la tâche", "DELETE_REPEAT_INSTANCE": "Supprimer l'instance de Tâche Répétée", "DROP_ATTACHMENT": "Déposer ici pour attacher à \"{{title}}\"", "EDIT_SCHEDULED": "Modifier le rappel", "EDIT_TAGS": "Modifier les balises", "EDIT_TASK_TITLE": "<PERSON><PERSON> le titre", "FOCUS_SESSION": "<PERSON><PERSON><PERSON><PERSON> la session Focus", "MARK_DONE": "Marquer comme terminée", "MARK_UNDONE": "Marquer comme annulée", "MOVE_TO_BACKLOG": "<PERSON>é<PERSON><PERSON> dans le backlog", "MOVE_TO_OTHER_PROJECT": "<PERSON><PERSON><PERSON>r vers un autre projet", "MOVE_TO_REGULAR": "<PERSON><PERSON><PERSON>r vers la liste quotidienne", "MOVE_TO_TOP": "Déplacer en haut de la liste", "OPEN_ATTACH": "<PERSON><PERSON><PERSON> un fichier ou un lien", "OPEN_ISSUE": "<PERSON><PERSON><PERSON><PERSON><PERSON> le ticket dans un nouvel onglet du navigateur", "OPEN_TIME": "<PERSON><PERSON><PERSON><PERSON> le temps / <PERSON><PERSON>ter le temps passé", "REMOVE_FROM_MY_DAY": "Supp<PERSON><PERSON> de ma journée", "REPEAT_EDIT": "Modifier la répétition de la tâche", "SCHEDULE": "Planifier une tâche", "SHOW_UPDATES": "<PERSON><PERSON><PERSON><PERSON> les mises à jour", "TOGGLE_ATTACHMENTS": "Afficher / masquer les pièces jointes", "TOGGLE_DETAIL_PANEL": "Afficher / masquer les informations supplémentaires", "TOGGLE_DONE": "Marquer comme fait / annulé", "TOGGLE_SUB_TASK_VISIBILITY": "Basculer la visibilité de la sous-tâche", "TOGGLE_TAGS": "Basculer les balises", "TRACK_TIME": "Commencer le suivi du temps", "TRACK_TIME_STOP": "Mettre en pause le suivi du temps", "UNSCHEDULE_TASK": "Déprogrammer la tâche", "UPDATE_ISSUE_DATA": "Mettre à jour les données du ticket"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAG": {"MSG": "Voulez-vous créer la nouvelle balise {tagsTxt} ?", "OK": "<PERSON><PERSON><PERSON> la balise"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAGS": {"MSG": "V<PERSON><PERSON><PERSON>-vous créer les nouvelles balises {tagsTxt} ?", "OK": "<PERSON><PERSON><PERSON> les balises"}, "D_REMINDER_VIEW": {"ADD_ALL_TO_TODAY": "Ajouter tout à aujourd'hui", "ADD_TO_TODAY": "Ajouter à aujourd'hui", "DONE": "<PERSON><PERSON><PERSON><PERSON>", "DUE_TASK": "Tâche due", "DUE_TASKS": "Tâches dues", "FOR_CURRENT": "La tâche est due. Voulez-vous commencer à travailler dessus ?", "FOR_OTHER": "La tâche est due. Voulez-vous commencer à travailler dessus ?", "FROM_PROJECT": "De<PERSON>is le projet", "FROM_TAG": "Depuis la balise: \"{{title}}\"", "RESCHEDULE_EDIT": "Editer (Re-planifier)", "RESCHEDULE_UNTIL_TOMORROW": "Jusqu'à demain", "SNOOZE": "Snooze", "SNOOZE_ALL": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "START": "<PERSON><PERSON><PERSON><PERSON> la tâche", "SWITCH_CONTEXT_START": "Changer de contexte et démarrer", "UNSCHEDULE": "Déprogrammer", "UNSCHEDULE_ALL": "Déprogram<PERSON> tout", "DISMISS_REMINDER_KEEP_TODAY": "Ignorer le rappel (Garder dans Aujourd'hui)", "DISMISS_ALL_REMINDERS_KEEP_TODAY": "Ignorer tous les rappels (Garder dans Aujourd'hui)"}, "D_SCHEDULE_TASK": {"MOVE_TO_BACKLOG": "Déplacer la tâche dans le backlog en atendant la planification", "QA_NEXT_MONTH": "Planifier pour le mois prochain", "QA_NEXT_WEEK": "Planifier pour la semaine prochaine", "QA_REMOVE_TODAY": "Supp<PERSON>er la tâche du jour", "QA_TODAY": "Planifier pour aujourd'hui", "QA_TOMORROW": "Planifier pour demain", "REMIND_AT": "<PERSON><PERSON><PERSON>", "RO_1H": "1 heure avant le début", "RO_5M": "5 minutes avant le début", "RO_10M": "10 minutes avant le début", "RO_15M": "15 minutes avant le début", "RO_30M": "30 minutes avant le début", "RO_NEVER": "<PERSON><PERSON>", "RO_START": "quand ça commence", "SCHEDULE": "Planifier", "UNSCHEDULE": "Dé-planifier"}, "D_TIME": {"ADD_FOR_OTHER_DAY": "Ajouter le temps passé pour un autre jour", "DELETE_FOR": "Supprimer l'entrée du jour", "ESTIMATE": "Estimation", "TIME_SPENT": "Te<PERSON> passé", "TIME_SPENT_ON": "Temps passé {{date}}", "TITLE": "Temps passé / estimations"}, "D_TIME_FOR_DAY": {"ADD_ENTRY_FOR": "Ajouter une nouvelle entrée pour {{date}}", "DATE": "Date de la nouvelle entrée", "HELP": "Exemples:<br> 30m => 30 minutes<br> 2h => 2 heures<br> 2h 30m => 2 heures et 30 minutes", "TINE_SPENT": "Te<PERSON> passé", "TITLE": "Ajouter pour le jour"}, "N": {"ESTIMATE_EXCEEDED": "Estimation du temps dépassée !", "ESTIMATE_EXCEEDED_BODY": "Vous avez dépassé votre temps estimé pour \"{{title}}\"."}, "S": {"CANNOT_ASSIGN_PROJECT_FOR_REPEATABLE_TASK": "Impossible d'attribuer le projet via une courte syntaxe pour les tâches répétées !", "CREATED_FOR_PROJECT": "T<PERSON><PERSON> \"{{taskTitle}}\" déplacée vers le projet \"{{projectTitle}}\"", "CREATED_FOR_PROJECT_ACTION": "Aller au projet", "DELETED": "Tâche supprimée \"{{title}}\"", "FOUND_MOVE_FROM_BACKLOG": "Déplacement de la tâche existante <strong>{{title}}</strong> vers la liste des tâches du jour", "FOUND_MOVE_FROM_OTHER_LIST": "Tâche <strong>{{title}}</strong> ajou<PERSON>e de <strong>{{contextTitle}}</strong> à la liste actuelle", "FOUND_RESTORE_FROM_ARCHIVE": "Tâche <strong>{{title}}</strong> restaurée en lien avec le ticket de l'archive", "LAST_TAG_DELETION_WARNING": "Vous essayez de supprimer la dernière balise d'une tâche non liée à un projet. Ce n'est pas permis!", "MOVED_TO_ARCHIVE": "Déplacé {{nr}} tâches vers l'archive", "MOVED_TO_PROJECT": "T<PERSON><PERSON> \"{{taskTitle}}\" déplacée vers le projet \"{{projectTitle}}\"", "MOVED_TO_PROJECT_ACTION": "Aller au projet", "REMINDER_ADDED": "Tâche planifiée \"{{title}}\"", "REMINDER_DELETED": "Rappel supprimé pour la tâche", "REMINDER_UPDATED": "<PERSON><PERSON> mis à jour pour la tâche \"{{title}}\"", "TASK_CREATED": "<PERSON><PERSON><PERSON> créée \"{{title}}\""}, "SELECT_OR_CREATE": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou créer une tâche", "SUMMARY_TABLE": {"ESTIMATE": "Estimation", "SPENT_TODAY": "<PERSON><PERSON><PERSON><PERSON><PERSON> aujou<PERSON>'hui", "SPENT_TOTAL": "Total dépensé", "TASK": "<PERSON><PERSON><PERSON>", "TOGGLE_DONE": "marquer comme fait/non fait"}}, "TASK_REPEAT": {"ADD_INFO_PANEL": {"CUSTOM": "Configuration de répétition customisée", "CUSTOM_AND_TIME": "Customisé, {timeStr}", "CUSTOM_WEEKLY": "{daysStr}", "CUSTOM_WEEKLY_AND_TIME": "{{daysStr}}, {{timeStr}}", "DAILY": "<PERSON><PERSON> jour", "DAILY_AND_TIME": "<PERSON><PERSON> j<PERSON>, {{timeStr}}", "EVERY_X_DAILY": "Tous les {{x}} jours", "EVERY_X_DAILY_AND_TIME": "Tous les {{x}} jours, {{timeStr}}", "EVERY_X_MONTHLY": "Tous les {{x}} mois", "EVERY_X_MONTHLY_AND_TIME": "Tous les {{x}} mois, {{timeStr}}", "EVERY_X_YEARLY": "Tous les {{x}} ans", "EVERY_X_YEARLY_AND_TIME": "Tous les {{x}} ans, {{timeStr}}", "MONDAY_TO_FRIDAY": "Lun-Ven", "MONDAY_TO_FRIDAY_AND_TIME": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {timeStr}", "MONTHLY_CURRENT_DATE": "Le {{dateDayStr}} de chaque mois", "MONTHLY_CURRENT_DATE_AND_TIME": "Le {dateDayStr} de chaque mois, {timeStr}", "WEEKLY_CURRENT_WEEKDAY": "<PERSON><PERSON> semaine le {weekdayStr}", "WEEKLY_CURRENT_WEEKDAY_AND_TIME": "<PERSON><PERSON> semaine le {weekdayStr}, {timeStr}", "YEARLY_CURRENT_DATE": "<PERSON><PERSON> année le {dayAndMonthStr}", "YEARLY_CURRENT_DATE_AND_TIME": "<PERSON><PERSON> année le {dayAndMonthStr}, {timeStr}"}, "D_CONFIRM_MOVE_TO_PROJECT": {"MSG": "Il y a {{tasksNr}} instances créées pour cette tâche répétée. Voulez-vous toutes les déplacer vers le projet « {{projectName}} » ?", "OK": "Mettre à jour toutes les instances"}, "D_CONFIRM_REMOVE": {"MSG": "En supprimant la configuration de répétition, toutes les instances précédentes de cette tâche seront converties en tâches ordinaires. Êtes-vous sur de vouloir continuer", "OK": "Supprimer complètement"}, "D_CONFIRM_UPDATE_INSTANCES": {"CANCEL": "Seulement aux tâches futures", "MSG": "Il y a {tasksNr} instances créées pour cette tâche répétée. Voulez-vous les mettre toutes à jour avec les nouveaux paramètres par défaut, ou juste appliquer le changement aux tâches futures ?", "OK": "Mettre à jour toutes les instances"}, "D_EDIT": {"ADD": "Ajouter une configuration de tâche répétitive", "ADVANCED_CFG": "Configuration avancée", "EDIT": "Modifier la configuration de tâche répétitive", "HELP1": "Les tâches répétitives sont destinées aux tâches quotidiennes, par exemple: \"Organisation\", \"Réunion quotidienne\", \"Vérification du code\", \"Vérification des emails\" ou des tâches similaires susceptibles de se reproduire.", "HELP2": "Une fois configurée, une tâche répétitive sera recréée chaque jour sélectionné ci-dessous dès que vous ouvrez votre projet et sera automatiquement marquée comme terminée à la fin de la journée. Ils seront traités comme des instances différentes. Vous pouvez donc ajouter librement des sous-tâches, etc.", "HELP3": "Les tâches importées de Jira ou d'issues Git ne peuvent pas être répétées. Tous les rappels seront également supprimés sur une tâche répétée.", "HELP4": "Une note sur le champ de commande : Se référant aux tâches répétables de l'ordre de création. Uniquement en vigueur pour les tâches répétables qui sont créées en même temps. Une valeur inférieure signifie qu'une tâche sera plus haut dans la liste, un nombre inférieur qu'elle sera plus bas. Une valeur supérieure à 0 signifie que les éléments sont créés au bas des tâches normales.", "TAG_LABEL": "Balises à ajouter"}, "F": {"C_DAY": "Jour", "C_MONTH": "<PERSON><PERSON>", "C_WEEK": "<PERSON><PERSON><PERSON>", "C_YEAR": "<PERSON><PERSON>", "DEFAULT_ESTIMATE": "Estimation par défaut", "FRIDAY": "vend<PERSON>i", "IS_ADD_TO_BOTTOM": "<PERSON>é<PERSON>r la tâche en bas de la liste", "MONDAY": "lundi", "NOTES": "Notes par défaut", "ORDER": "ordre", "ORDER_DESCRIPTION": "Ordre de création des tâches répétables. N'affecte que les tâches répétables créées en même temps. Une valeur inférieure signifie qu'une tâche sera créée plus haut dans la liste, un nombre inférieur qu'elle sera plus bas. Une valeur supérieure à 0 signifie que les éléments sont créés au bas des tâches normales.", "Q_CUSTOM": "Configuration de répétition customisée", "Q_DAILY": "<PERSON><PERSON> jour", "Q_MONDAY_TO_FRIDAY": "<PERSON>que semaine du lundi au vendredi", "Q_MONTHLY_CURRENT_DATE": "Chaque mois le {dateDayStr}", "Q_WEEKLY_CURRENT_WEEKDAY": "<PERSON><PERSON> semaine le {weekdayStr}", "Q_YEARLY_CURRENT_DATE": "<PERSON><PERSON> année le {dayAndMonthStr}", "QUICK_SETTING": "Ré<PERSON><PERSON><PERSON> la <PERSON>", "REMIND_AT": "<PERSON><PERSON><PERSON>", "REMIND_AT_PLACEHOLDER": "Sélectionnez quand rappeler", "REPEAT_CYCLE": "<PERSON><PERSON><PERSON><PERSON><PERSON> le cycle", "REPEAT_EVERY": "<PERSON><PERSON><PERSON><PERSON><PERSON> tous les", "SATURDAY": "<PERSON>di", "START_DATE": "Date de début", "START_TIME": "Heure de début programmée", "START_TIME_DESCRIPTION": "Par exemple. 15:00. <PERSON><PERSON> vide pour une tâche toute la journée", "SUNDAY": "dimanche", "THURSDAY": "jeudi", "TITLE": "<PERSON><PERSON><PERSON> de la tâche", "TUESDAY": "mardi", "WEDNESDAY": "merc<PERSON>i"}}, "TASK_VIEW": {"CUSTOMIZER": {"ENTER_PROJECT": "Entrer le projet", "ENTER_TAG": "Entrer le tag", "ESTIMATED_TIME": "Temps estimé", "FILTER_BY": "Filtrer par", "FILTER_DEFAULT": "Pas de filtre", "FILTER_ESTIMATED_TIME": "Temps estimé", "FILTER_PROJECT": "Projet", "FILTER_SCHEDULED_DATE": "Date prévue", "FILTER_TAG": "Étiquette", "FILTER_TIME_SPENT": "Te<PERSON> passé", "GROUP_BY": "Grouper par", "GROUP_DEFAULT": "Aucun groupe", "GROUP_PROJECT": "Projet", "GROUP_SCHEDULED_DATE": "Date prévue", "GROUP_TAG": "Étiquette", "RESET_ALL": "Réinitialiser tout", "SCHEDULED_DEFAULT": "Toute date", "SCHEDULED_NEXT_MONTH": "<PERSON><PERSON> prochain", "SCHEDULED_NEXT_WEEK": "Semaine prochaine", "SCHEDULED_THIS_MONTH": "Ce mois-ci", "SCHEDULED_THIS_WEEK": "<PERSON><PERSON> se<PERSON>", "SCHEDULED_TODAY": "<PERSON><PERSON><PERSON>'hui", "SCHEDULED_TOMORROW": "<PERSON><PERSON><PERSON>", "SORT_BY": "Trier par", "SORT_CREATION_DATE": "Date de création", "SORT_DEFAULT": "<PERSON><PERSON> <PERSON><PERSON>", "SORT_NAME": "Nom", "SORT_SCHEDULED_DATE": "Date prévue", "TIME_1HOUR": "> 1 Heure", "TIME_2HOUR": "> 2 Heures", "TIME_10MIN": "> 10 Minutes", "TIME_30MIN": "> 30 Minutes", "TIME_DEFAULT": "<PERSON>ute durée", "TIME_SPENT": "Te<PERSON> passé", "TITLE": "Personnaliser la vue des tâches"}}, "TIME_TRACKING": {"B": {"ALREADY_DID": "Je l'ai déjà fait", "SNOOZE": "Snooze {{time}}"}, "B_TTR": {"ADD_TO_TASK": "Ajouter à la tâche", "MSG": "Vous n'avez pas suivi le temps pour {{time}}"}, "D_IDLE": {"ADD_ENTRY": "Ajouter une entrée pour le suivi", "BREAK": "Pause", "CREATE_AND_TRACK": "<em><PERSON><PERSON><PERSON></em> et ajouter sur :", "IDLE_FOR": "V<PERSON> avez été inactif pendant : ", "RESET_BREAK_REMINDER_TIMER": "Réinitialiser le minuteur de rappel de pause", "SIMPLE_CONFIRM_COUNTER_CANCEL": "Sauter", "SIMPLE_CONFIRM_COUNTER_OK": "<PERSON><PERSON>", "SIMPLE_COUNTER_CONFIRM_TXT": "Vous avez sélectionné ignorer, mais activé {{nr}} bouton(s) de compteur simple. Voulez-vous suivre le temps d'inactivité pour eux ?", "SIMPLE_COUNTER_TOOLTIP": "Cliquez pour suivre jusqu'à {{title}}", "SIMPLE_COUNTER_TOOLTIP_DISABLE": "Cliquez pour ne PAS suivre jusqu'à {{title}}", "SKIP": "Passer", "SPLIT_TIME": "Diviser le temps en plusieurs tâches et pauses", "TASK": "<PERSON><PERSON><PERSON>", "TRACK_TO": "Ajouter à :"}, "D_TRACKING_REMINDER": {"CREATE_AND_TRACK": "<em><PERSON><PERSON><PERSON></em> et suivre vers", "IDLE_FOR": "Vous avez été inactif pour:", "NOTIFICATION_TITLE": "Su<PERSON>z votre temps !", "TASK": "<PERSON><PERSON><PERSON>", "TRACK_TO": "Suivre à:", "UNTRACKED_TIME": "Temps non suivi:"}}, "WORKLOG": {"CMP": {"DAYS_WORKED": "Jours travaillés : ", "MONTH_WORKED": "<PERSON><PERSON> trava<PERSON> : ", "REPEATING_TASK": "Tâche récurrent<PERSON>", "RESTORE_TASK_FROM_ARCHIVE": "Restaurer la tâche à partir de l'archive", "TASKS": "Tâches", "TOTAL_TIME": "Temps total passé :", "WEEK_NR": "<PERSON><PERSON><PERSON> {{nr}}", "WORKED": "Travaillé"}, "D_CONFIRM_RESTORE": "Êtes-vous sûr de vouloir déplacer la tâche <strong>\"{{title}}\"</strong> dans votre liste de tâches du jour ?", "D_EXPORT_TITLE": "Exportation du journal de travail {{start}}-{{end}}", "D_EXPORT_TITLE_SINGLE": "Exportation du journal de travail {{day}}", "EXPORT": {"ADD_COL": "Ajouter une colonne", "COPY_TO_CLIPBOARD": "Copier dans le presse-papier", "DONT_ROUND": "ne pas arrondir", "EDIT_COL": "Modifier la colonne", "GROUP_BY": "Grouper par", "O": {"DATE": "Date", "ENDED_WORKING": "<PERSON><PERSON><PERSON><PERSON> le travail", "ESTIMATE_AS_CLOCK": "Estimation format horaire (par exemple 5:23)", "ESTIMATE_AS_MILLISECONDS": "Estimation en millisecondes", "ESTIMATE_AS_STRING": "Estimation littérale (par exemple 5h 23m)", "FULL_HALF_HOURS": "demi heures pleines", "FULL_HOURS": "heures pleines", "FULL_QUARTERS": "quarts d'heure complets", "NOTES": "Descriptions des tâches", "PARENT_TASK": "Tâche parente", "PARENT_TASK_TITLES_ONLY": "Titres de tâches parentes uniquement", "PROJECTS": "Noms de projet", "STARTED_WORKING": "Commencé à travailler", "TAGS": "Balises", "TASK_SUBTASK": "Tâche / Sous-tâche", "TIME_AS_CLOCK": "Heure format horaire (par exemple 5:23)", "TIME_AS_MILLISECONDS": "Temps en millisecondes", "TIME_AS_STRING": "Temps au format littéral (par exemple 5h 23m)", "TITLES_AND_SUB_TASK_TITLES": "Titres et sous-titres de tâches", "WORKLOG": "Journal de travail"}, "OPTIONS": "Options", "ROUND_END_TIME_TO": "Arrondir l'heure de fin à", "ROUND_START_TIME_TO": "Arrondir l'heure de début à", "ROUND_TIME_WORKED_TO": "<PERSON><PERSON><PERSON><PERSON> le temps travaillé à", "SAVE_TO_FILE": "Enregistrer dans le fichier", "SEPARATE_TASKS_BY": "<PERSON><PERSON><PERSON><PERSON> les tâches par", "SHOW_AS_TEXT": "Afficher au format texte"}, "WEEK": {"EXPORT": "Exporter les données de la semaine", "NO_DATA": "Pas encore de tâche cette semaine.", "TITLE": "Titre"}}}, "FILE_IMEX": {"DIALOG_CONFIRM_URL_IMPORT": {"INITIATED_MSG": "Une importation automatique des données a été initiée.", "SOURCE_URL_DOMAIN": "Domaine source", "TITLE": "Confirmer l'importation des données depuis l'URL", "WARNING_MSG": "Continuer écrasera vos données et configurations actuelles de l'application avec le contenu de l'URL spécifiée. Cette action ne peut pas être annulée.", "WARNING_TITLE": "Avertissement"}, "EXPORT_DATA": "Exporter des données", "IMPORT_FROM_FILE": "Importer depuis un fichier", "IMPORT_FROM_URL": "Importer depuis l'URL", "IMPORT_FROM_URL_DIALOG_DESCRIPTION": "Veuillez entrer l'URL complète du fichier de sauvegarde JSON de Super Productivity que vous souhaitez importer.", "IMPORT_FROM_URL_DIALOG_TITLE": "Importer depuis l'URL", "OPEN_IMPORT_FROM_URL_DIALOG": "Importer depuis l'URL", "PRIVACY_EXPORT": "Exporter les données anonymisées (pour envoyer à <EMAIL> dans un but de débogage)", "S_BACKUP_DOWNLOADED": "Sauvegarde téléchargée dans le dossier documents d'android", "S_ERR_IMPORT_FAILED": "L'importation des données a échoué", "S_ERR_INVALID_DATA": "Échec de l'importation: JSON non valide", "S_ERR_INVALID_URL": "Échec de l'importation : URL fournie invalide", "S_ERR_NETWORK": "Échec de l'importation : E<PERSON>ur réseau lors de la récupération des données depuis l'URL", "S_IMPORT_FROM_URL_ERR_DECODE": "Erreur : Impossible de décoder le paramètre URL pour l'importation. Veuillez vous assurer qu'il est correctement formaté.", "URL_PLACEHOLDER": "Entrez l'URL à partir de laquelle importer"}, "G": {"ADD": "Ajouter", "ADVANCED_CFG": "Configuration avancée", "CANCEL": "annuler", "CLOSE": "<PERSON><PERSON><PERSON>", "CONFIRM": "Confirmer", "DELETE": "<PERSON><PERSON><PERSON><PERSON>", "DISMISS": "<PERSON><PERSON><PERSON>", "DO_IT": "Fais le !", "DURATION_DESCRIPTION": "ex: \"5h 23m\" ce qui équivaut à 5 heures et 23 minutes", "EDIT": "modifier", "ENABLED": "Activée", "EXAMPLE_VAL": "cliquer pour modifier", "EXTENSION_INFO": "Veuillez <a target=\"_blank\" href=\"https://chrome.google.com/webstore/detail/super-productivity/ljkbjodfmekklcoibdnhahlaalhihmlb\"> télécharger l'extension chrome</a> afin de permettre la communication avec l'Api Jira et la gestion du temps d'inactivité. Notez que cela ne fonctionne pas sur mobile.", "HIDE": "<PERSON><PERSON><PERSON><PERSON>", "ICON_INP_DESCRIPTION": "Tous les emojis utf-8 sont également pris en charge !", "INBOX_PROJECT_TITLE": "<PERSON><PERSON><PERSON>", "LOGIN": "S'identifier", "LOGOUT": "Se déconnecter", "MINUTES": "{{m}} minutes", "MOVE_BACKWARD": "<PERSON><PERSON><PERSON>", "MOVE_FORWARD": "Avancer", "NEXT": "Suivant", "NO_CON": "Vous êtes actuellement hors ligne. Veuillez vous reconnecter à Internet.", "NONE": "Aucun", "OK": "Ok", "OVERDUE": "En retard", "PREVIOUS": "p<PERSON><PERSON><PERSON>", "REMOVE": "<PERSON><PERSON><PERSON>", "RESET": "Réinitialiser", "SAVE": "sauve<PERSON><PERSON>", "SUBMIT": "So<PERSON><PERSON><PERSON>", "TITLE": "Titre", "TODAY_TAG_TITLE": "<PERSON><PERSON><PERSON>'hui", "TRACKING_INTERVAL_DESCRIPTION": "Suivre le temps en utilisant cet intervalle en millisecondes. Vous pourriez vouloir changer cela pour réduire les écritures disque. Voir le ticket #2355.", "UNDO": "annuler", "UPDATE": "Mettre à jour", "WITHOUT_PROJECT": "Sans projet", "YESTERDAY": "<PERSON>er"}, "GCF": {"AUTO_BACKUPS": {"HELP": "Sauvegardez automatiquement toutes les données dans votre dossier d'applications afin de les retrouver en cas de problème.", "LABEL_IS_ENABLED": "Activer les sauvegardes automatiques", "LOCATION_INFO": "Les sauvegardes sont enregistrées dans:", "TITLE": "Sauvegardes automatiques"}, "CALENDARS": {"BROWSER_WARNING": "<b><PERSON><PERSON> ne fonctionnera probablement PAS avec la version navigateur de Super Productivity.<br /> Veuillez <a href=\"https://super-productivity.com/download/\">télécharger la version de bureau</a> pour utiliser cette fonctionnalité !</b>", "CAL_PATH": "URL de la source iCal", "CAL_PROVIDERS": "Fournisseur de Calendrier (expérimental et optionnel)", "CHECK_UPDATES": "Vérifier les mises à jour distantes tous les X", "DEFAULT_PROJECT": "Projet par défaut pour les tâches de calendrier ajoutées", "HELP": "Vous pouvez intégrer des calendriers pour recevoir des rappels et les ajouter en tant que tâches dans Super Productivity. L'intégration fonctionne en utilisant le format iCal. Pour que cela fonctionne, vos calendriers doivent être accessibles soit sur Internet, soit via le système de fichiers.", "SHOW_BANNER_THRESHOLD": "Afficher une notification \"X temps\" avant l'événement (vide si désactivé)"}, "EVALUATION": {"IS_HIDE_EVALUATION_SHEET": "Masquer la fiche d'évaluation dans le résumé quotidien", "TITLE": "Évaluation et mesures"}, "FOCUS_MODE": {"HELP": "Le Mode Concentration ouvre un écran dépourvu de toute distraction pour vous aider à vous focaliser sur la tâche courante", "L_ALWAYS_OPEN_FOCUS_MODE": "Toujours ouvrir le Mode Concentration, quand le suivi est actif", "L_SKIP_PREPARATION_SCREEN": "Passer l'écran de préparation (étirement, etc.)", "TITLE": "Mode Concentration"}, "IDLE": {"HELP": "<div><p>Lors<PERSON> la gestion de la durée d'inactivité est activée, une boîte de dialogue s'ouvre après un délai spécifié pour vérifier si et sur quelle tâche vous souhaitez suivre votre temps, lorsque vous êtes inactif.</p></div>", "IS_ENABLE_IDLE_TIME_TRACKING": "Activer la gestion du temps d'inactivité", "IS_ONLY_OPEN_IDLE_WHEN_CURRENT_TASK": "<PERSON><PERSON><PERSON><PERSON>ez uniquement la boîte de dialogue d'inactivité lorsqu'une tâche en cours est sélectionnée", "MIN_IDLE_TIME": "Déclenchement d'inactivité après X", "TITLE": "Gestion de l'inactivité"}, "IMEX": {"HELP": "<p><PERSON><PERSON>, vous pouvez exporter toutes vos données sous forme de <strong>JSON</strong> pour les sauvegardes, mais également pour les utiliser dans un contexte différent (vous pouvez par exemple vouloir exporter vos projets dans le navigateur et les importer dans la version de bureau). ). </p> <p>L'importation nécessite la copie d'un fichier JSON valide dans la zone de texte. <strong>REMARQUE: une fois que vous avez cliqué sur le bouton d'importation, tous vos paramètres et données actuels seront écrasés!</strong></p>", "TITLE": "Import / Export"}, "KEYBOARD": {"ADD_NEW_NOTE": "Ajouter une nouvelle note", "ADD_NEW_TASK": "Ajouter une nouvelle tâche", "APP_WIDE_SHORTCUTS": "<PERSON><PERSON><PERSON><PERSON> globaux (sur toute l'application)", "COLLAPSE_SUB_TASKS": "Réduire les sous-tâches", "EXPAND_SUB_TASKS": "Développer les sous-tâches", "GLOBAL_ADD_NOTE": "Ajouter une nouvelle note", "GLOBAL_ADD_TASK": "Ajouter une nouvelle tâche", "GLOBAL_SHOW_HIDE": "Afficher / masquer Super Productivity", "GLOBAL_TOGGLE_TASK_START": "Basculer le suivi du temps pour la dernière tâche active", "GO_TO_DAILY_AGENDA": "Aller à l'agenda", "GO_TO_FOCUS_MODE": "Aller au mode Concentration", "GO_TO_SCHEDULE": "Aller à la chronologie", "GO_TO_SCHEDULED_VIEW": "Accéder aux tâches planifiées", "GO_TO_SETTINGS": "Aller aux paramètres", "GO_TO_WORK_VIEW": "Aller à la vue de travail", "HELP": "<p>Vous pouvez configurer ici tous les raccourcis clavier.</p> <p>Cliquez sur le champ texte et entrez la combinaison de touches souhaitée. Appuyez sur Entrée pour enregistrer et sur Échap pour annuler.</p> <p>Il existe trois types de raccourcis:</p> <ul> <li> <strong>Ra<PERSON>ur<PERSON> globaux:</strong> lorsque l'application est en cours d'exécution, elle déclenche l'action depuis n'importe quelle autre applications. </li> <li> <strong>Raccourcis au niveau de l'application:</strong> se déclenchera à partir de n'importe quel écran de l'application, mais pas si vous modifiez actuellement un champ de texte. </li> <li> <strong>Raccourcis au niveau des tâches:</strong> ils ne se déclenchent que si vous avez sélectionné une tâche à l'aide de la souris ou du clavier et déclenchent généralement une action spécifiquement liée à cette tâche. </li> </ul>", "MOVE_TASK_DOWN": "Descendre la tâche dans la liste", "MOVE_TASK_TO_BOTTOM": "<PERSON><PERSON><PERSON>r la Tâche tout en bas de la Liste", "MOVE_TASK_TO_TOP": "<PERSON><PERSON><PERSON>r la Tâche tout en haut de la Liste", "MOVE_TASK_UP": "Remonter la tâche dans la liste", "MOVE_TO_BACKLOG": "<PERSON><PERSON><PERSON><PERSON> une tâche dans le backlog", "MOVE_TO_REGULARS_TASKS": "<PERSON><PERSON><PERSON>r la tâche vers la liste des tâches du jour", "OPEN_PROJECT_NOTES": "Afficher / masquer les notes de projet", "SAVE_NOTE": "Sauvegarder la note", "SELECT_NEXT_TASK": "Sélectionner la tâche suivante", "SELECT_PREVIOUS_TASK": "Sélectionner la tâche précédente", "SHOW_SEARCH_BAR": "Afficher la barre de recherche", "SYSTEM_SHORTCUTS": "<PERSON><PERSON><PERSON><PERSON> globa<PERSON> (à l'échelle du système)", "TASK_ADD_ATTACHMENT": "<PERSON><PERSON><PERSON> un fichier ou un lien", "TASK_ADD_SUB_TASK": "Ajouter une sous-tâche", "TASK_DELETE": "Supp<PERSON>er la tâche", "TASK_EDIT_TAGS": "Modifier les balises", "TASK_EDIT_TITLE": "Modifier le titre", "TASK_MOVE_TO_PROJECT": "<PERSON><PERSON><PERSON><PERSON><PERSON> la tâche de déplacement vers le menu du projet", "TASK_OPEN_CONTEXT_MENU": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu contextuel de la tâche", "TASK_OPEN_ESTIMATION_DIALOG": "Modifier l'estimation / le temps passé", "TASK_PLAN_FORDAY": "Planifier pour le jour", "TASK_SCHEDULE": "Planifier une tâche", "TASK_SHORTCUTS": "Tâches", "TASK_SHORTCUTS_INFO": "Les raccourcis suivants s'appliquent à la tâche actuellement sélectionnée (sélectionnée via tab ou la souris).", "TASK_TOGGLE_DETAIL_PANEL_OPEN": "Afficher / masquer les informations supplémentaires de la tâche", "TASK_TOGGLE_DONE": "<PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "TOGGLE_BACKLOG": "Afficher / masquer le backlog", "TOGGLE_BOOKMARKS": "Afficher / masquer la barre de favoris", "TOGGLE_ISSUE_PANEL": "<PERSON><PERSON>/<PERSON><PERSON> le panneau des problèmes", "TOGGLE_PLAY": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>âche", "TOGGLE_SIDE_NAV": "Afficher et mettre au point / masquer Sidenav", "TOGGLE_TASK_VIEW_CUSTOMIZER_PANEL": "Basculer le panneau de filtre/groupe/tris", "TRIGGER_SYNC": "Déclencher la synchronisation (si configurée)", "ZOOM_DEFAULT": "Zoom par défaut (bureau uniquement)", "ZOOM_IN": "Zoom avant (bureau uniquement)", "ZOOM_OUT": "Zoom arrière (bureau uniquement)"}, "LANG": {"AR": "عرب<PERSON>", "CZ": "Tchèque", "DE": "De<PERSON>ch", "EN": "<PERSON><PERSON><PERSON>", "ES": "Espagnol", "FA": "فار<PERSON>ی", "FR": "Français", "HR": "Hrvatski", "ID": "Indonésien", "IT": "Italiano", "JA": "日本語", "KO": "한국어", "LABEL": "<PERSON><PERSON><PERSON> de choisir une langue", "NB": "Norsk Bokmål", "NL": "Pays-Bas", "PL": "Polonais", "PT": "Português", "RU": "<PERSON><PERSON>", "SK": "Slovaque", "TITLE": "<PERSON><PERSON>", "TR": "Türkçe", "UK": "Українська", "ZH": "中文(简体)", "ZH_TW": "中文(繁體)"}, "MISC": {"DEFAULT_PROJECT": "Projet par défaut à utiliser pour les tâches si aucun n'est spécifié", "FIRST_DAY_OF_WEEK": "Premier j<PERSON> de la semaine", "HELP": "<p><strong>Vous ne voyez pas les notifications de bureau ?</strong> <PERSON><PERSON>, vous pouvez vérifier Système> Notifications et actions et vérifier si les notifications requises ont été activées.</p>", "IS_AUTO_ADD_WORKED_ON_TO_TODAY": "Ajouter automatiquement la balise d'aujourd'hui aux tâches travaillées", "IS_AUTO_MARK_PARENT_AS_DONE": "Marquer la tâche parent comme terminée lorsque toutes les sous-tâches sont terminées", "IS_CONFIRM_BEFORE_EXIT": "Confirmez avant de quitter l'application", "IS_CONFIRM_BEFORE_EXIT_WITHOUT_FINISH_DAY": "Confirmez avant de quitter l'application sans terminer le jour en premier", "IS_DARK_MODE": "Mode sombre", "IS_DISABLE_ANIMATIONS": "Désactiver toutes les animations", "IS_HIDE_NAV": "Masquer la navigation jusqu'à ce que l'en-tête principal soit survolé (bureau uniquement)", "IS_MINIMIZE_TO_TRAY": "Réduire dans le bac (bureau uniquement)", "IS_SHOW_TIP_LONGER": "Afficher le conseil de productivité au démarrage de l'application un peu plus longtemps", "IS_TRAY_SHOW_CURRENT_COUNTDOWN": "Afficher le compte à rebours actuel dans la barre d'état / menu (mac de bureau uniquement)", "IS_TRAY_SHOW_CURRENT_TASK": "Afficher la tâche en cours dans la barre d'état / menu d'état (bureau uniquement)", "IS_TURN_OFF_MARKDOWN": "Désactiver l'analyse de démarquage pour les notes", "IS_USE_MINIMAL_SIDE_NAV": "Utiliser la barre minimale de navigation (affichage des icônes uniquement)", "START_OF_NEXT_DAY": "<PERSON><PERSON> de début le jour suivant", "START_OF_NEXT_DAY_HINT": "À partir de quelle heure souhaitez-vous considérer que le jour suivant commence. Par dé<PERSON>, minuit correspond à 0 heure.", "TASK_NOTES_TPL": "Modèle de description de tâche", "TITLE": "Réglages divers"}, "POMODORO": {"BREAK_DURATION": "<PERSON><PERSON><PERSON> des <PERSON> courtes", "CYCLES_BEFORE_LONGER_BREAK": "Commencez une pause plus longue après X sessions de travail", "DURATION": "Durée des séances de travail", "HELP": "<p>Le minuteur pomodoro peut être configurée via deux paramètres. La durée de chaque session de travail, la durée des pauses normales, le nombre de sessions de travail à exécuter avant le début d'une pause plus longue et la durée de cette pause plus longue.</p> <p>Vous pouvez également définir si vous souhaitez afficher vos distractions pendant vos pomodo.</p> <p>Le paramètre \"Suivi du temps de pause sur une pause pomodoro\" suit également vos pauses en tant que temps de travail consacré à une tâche. </p> <p>L'activation de \"Suspendre la session pomodoro en l'absence de tâche active\" suspend également la session pomodoro, lorsque vous suspendez une tâche.</p>", "IS_ENABLED": "<PERSON>r le minuteur pomodoro", "IS_MANUAL_CONTINUE": "Confirmez manuellement le démarrage de la prochaine session pomodoro", "IS_MANUAL_CONTINUE_BREAK": "Confirmez manuellement le démarrage de la prochaine pause", "IS_PLAY_SOUND": "Jouer un son quand la session est terminée", "IS_PLAY_SOUND_AFTER_BREAK": "Jouer un son quand la pause est terminée", "IS_PLAY_TICK": "Jouer un tick toutes les secondes", "IS_STOP_TRACKING_ON_BREAK": "<PERSON><PERSON><PERSON><PERSON> le suivi du temps pour la tâche en pause", "LONGER_BREAK_DURATION": "Du<PERSON>e des pauses plus longues", "TITLE": "Pomodoro Timer"}, "REMINDER": {"COUNTDOWN_DURATION": "Afficher la bannière \"X temps\" avant le rappel réel", "IS_COUNTDOWN_BANNER_ENABLED": "Afficher une bannière de compte à rebours avant l'échéance des rappels", "TITLE": "<PERSON><PERSON><PERSON>"}, "SCHEDULE": {"HELP": "La fonction de chronologie devrait vous fournir un aperçu rapide de la façon dont vos tâches planifiées se déroulent au fil du temps. Vous pouvez le trouver dans le menu de gauche sous <a href='#/schedule'>\"Chronologie\"</a>.", "L_IS_LUNCH_BREAK_ENABLED": "<PERSON>r la <PERSON> dé<PERSON>r", "L_IS_WORK_START_END_ENABLED": "Limitez le flux de tâches non planifiées à des heures de travail spécifiques", "L_LUNCH_BREAK_END": "Fin de la pause déjeuner", "L_LUNCH_BREAK_START": "Début de la <PERSON> déjeuner", "L_WORK_END": "Fin de la journée de travail", "L_WORK_START": "Début de la journée de travail", "LUNCH_BREAK_START_END_DESCRIPTION": "ex : 13:00", "TITLE": "Chronologie", "WORK_START_END_DESCRIPTION": "par exemple. 17h00"}, "SHORT_SYNTAX": {"HELP": "<p><PERSON><PERSON>, vous pouvez contrôler les options de syntaxe courte lors de la création d'une tâche</p>", "IS_ENABLE_DUE": "Activer la syntaxe courte pour la date d'échéance (@<Date d'échéance>)", "IS_ENABLE_PROJECT": "Activer la syntaxe courte pour le projet (+<Nom du Projet>)", "IS_ENABLE_TAG": "Activer la syntaxe courte pour les balises (#<Balise>)", "TITLE": "Syntaxe Courte"}, "SOUND": {"BREAK_REMINDER_SOUND": "Son pour rappel de prise de pause", "DONE_SOUND": "Son pour tâche terminée", "IS_INCREASE_DONE_PITCH": "Augmentez la hauteur pour chaque tâche effectuée", "TITLE": "Du son", "TRACK_TIME_SOUND": "Son de rappel de temps de piste", "VOLUME": "Le volume"}, "TAKE_A_BREAK": {"ADD_NEW_IMG": "Ajouter une image de motivation", "FULL_SCREEN_BLOCKER_DURATION": "Durée d'affichage du plein écran (application de bureau uniquement)", "HELP": "<div> <p>vous permet de configurer un rappel récurrent lorsque vous avez travaillé pendant une durée déterminée sans prendre de pause.</p> <p>Vous pouvez modifier le message affiché. ${duration} sera remplacé par le temps passé sans pause.</p> </div>", "IS_ENABLED": "<PERSON>r le rappel de pause", "IS_FOCUS_WINDOW": "Mettre le focus sur l'application lorsque le rappel est actif (bureau uniquement)", "IS_FULL_SCREEN_BLOCKER": "Afficher un message en plein écran (application de bureau uniquement)", "IS_LOCK_SCREEN": "Verrouiller l'écran lorsqu'une pause est due (bureau uniquement)", "MESSAGE": "Message invitant à prendre une pause", "MIN_WORKING_TIME": "Déclencher l'invitation à prendre une pause après avoir travaillé X sans pause", "MOTIVATIONAL_IMGS": "Image de motivation (URL Web)", "NOTIFICATION_TITLE": "Prendre une pause!", "SNOOZE_TIME": "Temps de répétition lorsque vous êtes invité à faire une pause", "TITLE": "Rappel de pause"}, "TIME_TRACKING": {"HELP": "Le rappel de suivi du temps est une bannière qui s'affiche si vous oubliez de démarrer le suivi du temps.", "L_DEFAULT_ESTIMATE": "Estimation du temps par défaut pour les nouvelles tâches", "L_DEFAULT_ESTIMATE_SUB_TASKS": "Estimation du temps par défaut pour les nouvelles sous-tâches", "L_IS_AUTO_START_NEXT_TASK": "<PERSON><PERSON><PERSON><PERSON> le suivi de la prochaine tâche quand la tâche courante est marquée comme terminée", "L_IS_NOTIFY_WHEN_TIME_ESTIMATE_EXCEEDED": "Notifier lorsque le temps estimé a été dépassé", "L_IS_TRACKING_REMINDER_ENABLED": "Rappel de suivi activé", "L_IS_TRACKING_REMINDER_FOCUS_WINDOW": "Fenêtre de l’application Focus lorsque le rappel est actif (ordinateur uniquement)", "L_IS_TRACKING_REMINDER_NOTIFY": "Avertir lorsque le rappel de suivi du temps s’affiche", "L_IS_TRACKING_REMINDER_SHOW_ON_MOBILE": "Afficher le rappel de suivi sur l'application mobile", "L_TRACKING_INTERVAL": "Intervalle de suivi du temps (EXPÉRIMENTAL)", "L_TRACKING_REMINDER_MIN_TIME": "Temps d'attente avant d'afficher la bannière de rappel de suivi", "TITLE": "<PERSON><PERSON><PERSON>"}}, "GLOBAL_RELATIVE_TIME": {"FUTURE": {"A_DAY": "dans un jour", "A_MINUTE": "dans une minute", "A_MONTH": "dans un mois", "A_YEAR": "dans un an", "AN_HOUR": "dans une heure", "DAYS": "dans {{count}} jours", "FEW_SECONDS": "dans quelques secondes", "HOURS": "dans {{count}} heures", "MINUTES": "dans {{count}} minutes", "MONTHS": "dans {{count}} mois", "YEARS": "dans {{count}} ans"}, "PAST": {"A_DAY": "il y a un jour", "A_MINUTE": "Il y a une minute", "A_MONTH": "il y a un mois", "A_YEAR": "Il y a un an", "AN_HOUR": "Il y a une heure", "DAYS": "il y a {{count}} jours", "FEW_SECONDS": "Il y a quelques secondes", "HOURS": "il y a {{count}} heures", "MINUTES": "il y a {{count}} minutes", "MONTHS": "il y a {{count}} mois", "YEARS": "il y a {{count}} ans"}}, "GLOBAL_SNACK": {"COPY_TO_CLIPPBOARD": "Copié dans le presse-papier", "ERR_COMPRESSION": "Erreur pour l'interface de compression", "FILE_DOWNLOADED": "{{fileName}} t<PERSON><PERSON><PERSON><PERSON><PERSON>", "FILE_DOWNLOADED_BTN": "<PERSON><PERSON><PERSON> ouvert", "NAVIGATE_TO_TASK_ERR": "Impossible de consulter le détail de la tâche. L'avez-vous supprimée ?", "PERSISTENCE_DISALLOWED": "Les données ne seront pas conservées de manière permanente. Sachez que cela peut entraîner une perte de données!", "PERSISTENCE_ERROR": "Erreur lors de la demande de persistance des données : {{err}}", "RUNNING_X": "\"{{str}}\" en cours.", "SHORTCUT_WARN_OPEN_BOOKMARKS_FROM_TAG": "{{keyCombo}} enfo<PERSON><PERSON>, mais le raccourci vers les favoris ouverts n'est disponible que dans le contexte du projet."}, "GPB": {"ASSETS": "Chargement des éléments ...", "DBX_DOWNLOAD": "Dropbox: Télécharger le fichier ...", "DBX_GEN_TOKEN": "Dropbox: générer un jeton ...", "DBX_META": "Dropbox: <PERSON><PERSON><PERSON><PERSON> la méta du fichi<PERSON> ...", "DBX_UPLOAD": "Dropbox: Importer un fichier ...", "GITHUB_LOAD_ISSUE": "GitHub: Charger les données de problème ...", "JIRA_LOAD_ISSUE": "Jira: Charger les données du problème ...", "SYNC": "Synchronisation des données...", "UNKNOWN": "Chargement des données à distance", "WEB_DAV_DOWNLOAD": "WebDAV: Téléchargement de données ...", "WEB_DAV_UPLOAD": "WebDAV: Téléchargement de données ..."}, "MH": {"ADD_NEW_TASK": "Ajouter une nouvelle tâche", "ALL_PLANNED_LIST": "Répété / Programmé", "BOARDS": "Tableaux", "CREATE_PROJECT": "Créer un projet", "CREATE_TAG": "<PERSON><PERSON><PERSON> une balise", "DELETE_PROJECT": "Supprimer le projet", "DELETE_TAG": "Supprimer la balise", "ENTER_FOCUS_MODE": "Entrer en mode concentration", "GO_TO_TASK_LIST": "Aller à la liste des tâches", "HELP": "Aide", "HM": {"CALENDARS": "Tutoriel : connecter les calendriers", "CONTRIBUTE": "Contribuer", "GET_HELP_ONLINE": "<PERSON><PERSON><PERSON><PERSON> de l'aide en ligne", "KEYBOARD": "Tutoriel : <PERSON><PERSON><PERSON><PERSON> clav<PERSON> a<PERSON>", "REDDIT_COMMUNITY": "Communauté Reddit", "REPORT_A_PROBLEM": "Signaler un problème", "START_WELCOME": "Démarrer le tour de bienvenue", "SYNC": "Tutoriel : Configurer la synchronisation"}, "METRICS": "Métriques", "NO_PROJECT_INFO": "Aucun projet disponible. V<PERSON> pouvez créer un nouveau projet en cliquant sur le bouton \"Créer un projet\".", "NO_TAG_INFO": "Il n'y a actuellement aucune balise. Vous pouvez ajouter des balises en saisissant \"#maBalise\" lors de l'ajout ou de la modification de tâches.", "NOTES": "<PERSON><PERSON><PERSON>", "NOTES_PANEL_INFO": "Les notes ne peuvent être affichées que depuis les vues de programmation et de listes de tâches.", "PLANNER": "Planificateur", "PROCRASTINATE": "Procras<PERSON><PERSON>", "PROJECT_MENU": "Menu du projet", "PROJECT_SETTINGS": "paramètres du projet", "PROJECTS": "Projets", "QUICK_HISTORY": "Historique rapide", "SCHEDULE": "Programmer", "SEARCH": "<PERSON><PERSON><PERSON>", "SETTINGS": "Réglages", "SHOW_SEARCH_BAR": "Afficher la barre de recherche", "TAGS": "Balises", "TASK_LIST": "Liste de tâches", "TASKS": "Tâches", "TOGGLE_SHOW_BOOKMARKS": "Afficher / masquer les favoris", "TOGGLE_SHOW_ISSUE_PANEL": "<PERSON><PERSON>/<PERSON><PERSON> le panneau des problèmes", "TOGGLE_SHOW_NOTES": "Afficher / masquer les notes de projet", "TOGGLE_TRACK_TIME": "<PERSON><PERSON><PERSON><PERSON> / <PERSON>rr<PERSON><PERSON> le suivi du temps", "TRIGGER_SYNC": "Déclencher la synchronisation manuellement", "WORKLOG": "Journal de travail"}, "MIGRATE": {"C_DOWNLOAD_BACKUP": "Voulez-vous télécharger une sauvegarde de vos données héritées (peut être utilisée avec des versions antérieures de Super Productivity) ?", "DETECTED_LEGACY": "Données héritées détectées. Nous allons les migrer pour vous !", "E_MIGRATION_FAILED": "Échec de la migration ! avec l'erreur :", "E_RESTART_FAILED": "Échec du redémarrage automatique. Veuillez redémarrer l'application manuellement !", "SUCCESS": "Migration terminée ! Redémarrage de l'application maintenant..."}, "PDS": {"ADD_TASKS_FROM_TODAY": "Ajouter des tâches d'aujourd'hui", "BACK": "Attends j'ai oublié quelque chose!", "BREAK_LABEL": "Pauses (nr / heure)", "CELEBRATE": "Prenez un moment pour célébrer <i>!</i>", "CLEAR_ALL_CONTINUE": "Effacer tout et continuer", "D_CONFIRM_APP_CLOSE": {"CANCEL": "Non, effacer seulement les tâches", "MSG": "Votre journée de travail est terminée. Il est temps de rentrer à la maison!", "OK": "Aye Aye ! Fermer !"}, "ESTIMATE_TOTAL": "Estimation totale :", "EVALUATE_DAY": "<PERSON><PERSON><PERSON>", "EXPORT_TASK_LIST": "Exporter la liste des tâches", "NO_TASKS": "Il n'y a pas de tâches pour aujourd'hui", "PLAN_TOMORROW": "Planifier", "REVIEW_TASKS": "Passer en revue", "ROUND_5M": "Arrondir au plus près 5 minutes", "ROUND_15M": "Arrondir au plus près 15 minutes", "ROUND_30M": "Arrondir au plus près 30 minutes", "ROUND_TIME_SPENT": "<PERSON><PERSON><PERSON><PERSON> le temps passé", "ROUND_TIME_SPENT_TITLE": "Arrondir le temps passé sur toutes les tâches. Faites attention! Pas de retour en arrière possible!", "ROUND_TIME_WARNING": "!!! Attention, cela ne peut pas être annulé !!!", "ROUND_UP_5M": "Arrondir à 5 minutes vers le haut", "ROUND_UP_15M": "Arrondir à 15 minutes vers le haut", "ROUND_UP_30M": "Arrondir à 30 minutes vers le haut", "SAVE_AND_GO_HOME": "Enregistrer et rentrer à la maison", "SAVE_AND_GO_HOME_TOOLTIP": "D<PERSON>placer toutes les tâches terminées vers l'archive (journal de travail) et, si vous le souhaitez, synchroniser toutes les données et fermer l'application.", "START_END": "Début – Fin", "SUMMARY_FOR": "Récapitulatif quotidien pour {{dayStr}}", "TASKS_COMPLETED": "Tâches terminées", "TIME_SPENT_AND_ESTIMATE_LABEL": "Temps passé / estimé", "TIME_SPENT_ESTIMATE_TITLE": "Temps passé: Temps total passé aujourd'hui. Tâches archivées non incluses. - Temps estimé: temps estimé pour les tâches effectuées aujourd'hui moins le temps déjà passé dessus les autres jours.", "TIME_SPENT_TODAY_BY_TAG": "Temps passé aujourd'hui par <PERSON>", "WEEK": "La semaine"}, "PM": {"TITLE": "Données du projet"}, "PS": {"GLOBAL_SETTINGS": "Paramètres globaux", "ISSUE_INTEGRATION": "Intégration des problèmes", "PRIVACY_POLICY": "Politique privée", "PRODUCTIVITY_HELPER": "Aide à la productivité", "PROJECT_SETTINGS": "Paramètres spécifiques au projet", "PROVIDE_FEEDBACK": "Fournir une réponse", "SYNC_EXPORT": "Synchroniser et exporter", "TAG_SETTINGS": "Paramètres spécifiques aux balises", "TOGGLE_DARK_MODE": "Basculer en mode sombre"}, "SCHEDULE": {"NO_REPEATABLE_TASKS": "Il n'y a actuellement aucune tâche répétée. Vous pouvez planifier une tâche en choisissant « Répéter la tâche » dans le panneau latéral des tâches. Pour l'ouvrir, cliquez sur l'icône la plus à droite qui apparaît lorsque vous survolez une tâche (ou appuyez simplement sur la tâche sur mobile).", "NO_SCHEDULED": "Il n'y a actuellement aucune tâche planifiée. <PERSON><PERSON> pouvez planifier une tâche en choisissant \"Planifier une tâche\" dans le menu contextuel de la tâche. Pour l'ouvrir, cliquez sur les 3 petits points à droite d'une tâche.", "NO_SCHEDULED_TITLE": "Planifier les tâches du jour", "REPEATED_TASKS": "Tâches répétées", "SCHEDULED_TASKS": "Tâches programmées", "SCHEDULED_TASKS_WITH_TIME": "Planifier les tâches avec rappel", "START_TASK": "Démarrer la tâche maintenant et supprimer le rappel"}, "THEMES": {"amber": "ambre", "blue": "Bleu", "blue-grey": "Bleu gris", "cyan": "<PERSON><PERSON>", "deep-orange": "orange foncé", "deep-purple": "<PERSON> foncé", "green": "<PERSON>ert", "indigo": "Indigo", "light-blue": "<PERSON><PERSON>u clair", "light-green": "Vert clair", "lime": "Vert citron", "pink": "rose", "purple": "Violet", "SELECT_THEME": "Sé<PERSON><PERSON>ner un thème", "teal": "Turquoise", "yellow": "Jaune"}, "V": {"E_1TO10": "Veuillez entrer une valeur entre 1 et 10", "E_DATETIME": "La valeur entrée n'est pas un datetime !", "E_DURATION": "Veuillez entrer une durée valide (par exemple, 1h)", "E_MAX": "Ne devrait pas être plus grand que {{val}}", "E_MAX_LENGTH": "Doit comporter au maximum {{val}} caractères", "E_MIN": "Devrait être plus petit que {{val}}", "E_MIN_LENGTH": "Doit contenir au moins {{val}} caractères", "E_PATTERN": "En<PERSON><PERSON> invalide", "E_REQUIRED": "Ce champ est requis"}, "WW": {"ADD_MORE": "Ajouter plus", "ADD_SCHEDULED_FOR_TOMORROW": "Ajouter des tâches prévues pour demain ({{nr}})", "ADD_SOME_TASKS": "Ajoutez quelques tâches pour planifier votre journée!", "DONE_TASKS": "Tâches terminées", "DONE_TASKS_IN_ARCHIVE": "Il n'y a actuellement aucune tâche terminée ici, mais certaines sont déjà archivées.", "ESTIMATE_REMAINING": "Tps. restant:", "FINISH_DAY": "Fin de journée", "FINISH_DAY_FOR_PROJECT": "Finir la journée pour ce Projet", "FINISH_DAY_FOR_TAG": "Finir la journée pour cette Bali<PERSON>", "FINISH_DAY_TOOLTIP": "<PERSON><PERSON><PERSON> votre journée, archiver toutes les tâches finies (optionnel) et/ou planifier la prochaine journée.", "HELP_PROCRASTINATION": "A l'aide je procrastine !", "MOVE_DONE_TO_ARCHIVE": "<PERSON>é<PERSON>r les tâches terminées vers l'archive", "NO_DONE_TASKS": "Il n'y a actuellement aucune tâche terminée", "NO_PLANNED_TASK_ALL_DONE": "Toutes les tâches terminées", "NO_PLANNED_TASKS": "Aucune tâche planifiée", "READY_TO_WORK": "<PERSON>r<PERSON><PERSON> à travailler !", "RESET_BREAK_TIMER": "Réinitialiser le timer 'sans prendre de pause'", "TIME_ESTIMATED": "Temps estimé :", "TODAY_REMAINING": "Restant pour aujourd'hui :", "WITHOUT_BREAK": "Sans pause :", "WORKING_TODAY": "<PERSON><PERSON><PERSON><PERSON><PERSON> aujou<PERSON>'hui :", "WORKING_TODAY_ARCHIVED": "Temps travaillé aujourd'hui sur les tâches archivées"}}