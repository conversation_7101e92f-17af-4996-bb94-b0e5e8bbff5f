// import { async, ComponentFixture, TestBed } from '@angular/core/testing';
//
// import { DialogFullscreenMarkdownComponent } from './dialog-fullscreen-markdown.component';
//
// describe('FullscreenMarkdownComponent', () => {
//   let component: DialogFullscreenMarkdownComponent;
//   let fixture: ComponentFixture<DialogFullscreenMarkdownComponent>;
//
//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [ DialogFullscreenMarkdownComponent ]
//     })
//     .compileComponents();
//   }));
//
//   beforeEach(() => {
//     fixture = TestBed.createComponent(DialogFullscreenMarkdownComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
