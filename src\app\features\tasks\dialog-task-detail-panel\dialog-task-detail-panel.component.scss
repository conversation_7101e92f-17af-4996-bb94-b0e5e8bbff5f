@import '../../../../common';

:host {
  mat-dialog-content {
    padding: 0;
    border-radius: 4px;
    max-width: 450px;
    max-height: 75vh;
    min-width: 340px;

    background: var(--theme-bg-lightest) !important;
    border: 4px solid var(--theme-bg-lightest);

    @include mq(xxxs) {
      min-width: 380px;
    }
    @include mq(xs) {
      min-width: 450px;
      max-height: 85vh;
    }
  }

  &::ng-deep {
    .task-title-wrapper {
      padding-right: 8px !important;
    }

    task .hover-controls {
      display: none !important;
    }

    .wrapper {
      //padding: 16px;
    }

    task-detail-item {
      margin-left: var(--s);
      margin-right: var(--s);

      @include mq(xxs) {
        margin-left: calc(var(--s) * 1.5);
        margin-right: calc(var(--s) * 1.5);
      }
    }
  }
}
