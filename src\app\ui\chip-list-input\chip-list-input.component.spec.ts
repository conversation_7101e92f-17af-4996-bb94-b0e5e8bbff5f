// import {async, ComponentFixture, TestBed} from '@angular/core/testing';
//
// import {ChipListInputComponent} from './chip-list-input.component';
//
// describe('ChipListInputComponent', () => {
//   let component: ChipListInputComponent;
//   let fixture: ComponentFixture<ChipListInputComponent>;
//
//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [ChipListInputComponent]
//     })
//       .compileComponents();
//   }));
//
//   beforeEach(() => {
//     fixture = TestBed.createComponent(ChipListInputComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
