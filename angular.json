{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"sp2": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "", "schematics": {"@schematics/angular:component": {"style": "scss", "changeDetection": "OnPush"}, "@ngrx/schematics:component": {"styleext": "scss", "changeDetection": "OnPush"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"aot": true, "outputPath": {"base": ".tmp/angular-dist"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "src/tsconfig.app.json", "preserveSymlinks": true, "assets": ["src/favicon.ico", "src/assets", "src/manifest.json", "src/static"], "styles": ["src/styles.scss"], "scripts": [], "webWorkerTsConfig": "src/tsconfig.worker.json", "browser": "src/main.ts", "stylePreprocessorOptions": {"includePaths": ["."], "sass": {"silenceDeprecations": ["mixed-decls", "color-functions", "global-builtin", "import"]}}}, "configurations": {"development": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": false, "aot": false, "serviceWorker": "ngsw-config.json"}, "production": {"baseHref": "", "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "outputHashing": "all", "sourceMap": true, "namedChunks": false, "aot": true, "extractLicenses": true, "serviceWorker": "ngsw-config.json"}, "productionWeb": {"outputPath": {"base": "dist", "browser": "browser"}, "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "outputHashing": "all", "sourceMap": true, "namedChunks": false, "aot": true, "extractLicenses": true, "serviceWorker": "ngsw-config.json"}, "stage": {"baseHref": "", "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "outputHashing": "all", "sourceMap": true, "namedChunks": false, "aot": true, "extractLicenses": true, "serviceWorker": false}, "stageWeb": {"outputPath": {"base": "dist", "browser": "browser"}, "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "outputHashing": "all", "sourceMap": true, "namedChunks": false, "aot": true, "extractLicenses": true, "serviceWorker": "ngsw-config.json"}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"open": false, "buildTarget": "sp2:build"}, "configurations": {"production": {"buildTarget": "sp2:build:production"}, "development": {"buildTarget": "sp2:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "sp2:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "preserveSymlinks": true, "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss"], "scripts": [], "assets": ["src/favicon.ico", "src/assets", "src/manifest.json"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"cache": true, "lintFilePatterns": ["src/**/*.ts", "src/**/*.html", "e2e/**/*.ts", "electron/**/*.ts"]}}}}}, "cli": {"analytics": false, "schematicCollections": ["@ngrx/schematics"]}, "schematics": {"@schematics/angular:component": {"style": "scss"}}}