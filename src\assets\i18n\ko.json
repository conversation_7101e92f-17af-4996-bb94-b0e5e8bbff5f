{"ANDROID": {"NOTIFICATIONS": {"SYNC_CONFLICT_MSG": "확인 후 조치 방법을 결정하세요.", "SYNC_CONFLICT_TITLE": "동기화 충돌이 발생했습니다."}, "PERMANENT_NOTIFICATION_MSGS": {"INITIAL": "앱이 백그라운드에서 실행되어 동기화를 허용하는 경우 활성화된 경우", "NO_ACTIVE_TASKS": "활성 작업 없음", "SYNCING": "동기화"}}, "APP": {"B_INSTALL": {"IGNORE": "무시", "INSTALL": "설치", "MSG": "PWA로 Super Productivity를 설치하시겠습니까?"}, "B_OFFLINE": "인터넷 연결이 끊어졌습니다. 문제 제공자 데이터 동기화 및 요청이 작동하지 않습니다.", "UPDATE_MAIN_MODEL": "슈퍼 생산성이 대폭 업데이트되었습니다! 데이터에 대한 일부 마이그레이션이 필요합니다. 이로 인해 데이터가 이전 버전의 앱과 호환되지 않습니다.", "UPDATE_MAIN_MODEL_NO_UPDATE": "선택된 모델 업데이트가 없습니다. 모델 업그레이드를 수행하지 않으려면 마지막 버전으로 다운그레이드해야합니다.", "UPDATE_WEB_APP": "새 버전을 사용할 수 있습니다. 새 버전을 로드하시겠습니까?"}, "BL": {"NO_TASKS": "현재 백 로그에 작업이 없습니다."}, "CONFIRM": {"AUTO_FIX": "데이터가 손상된 것 같습니다. 자동으로 수정 하시겠습니까? 이로 인해 데이터가 부분적으로 손실 될 수 있습니다.", "RELOAD_AFTER_IDB_ERROR": "데이터베이스에 액세스할 수 없음:(가능한 원인은 백그라운드에서 앱 업데이트 또는 디스크 공간 부족입니다. 앱을 다시 로드하려면 확인을 누르십시오(일부 플랫폼에서는 앱을 수동으로 다시 시작해야 할 수 있음).", "RESTORE_FILE_BACKUP": "데이터가 없는 것 같지만 \"{{dir}}\"에서 사용 가능한 백업이 있습니다. {{from}}에서 최신 백업을 복원 하시겠습니까?", "RESTORE_FILE_BACKUP_ANDROID": "데이터가 없는 것 같지만 사용 가능한 백업이 있습니다. 로드하시겠습니까?", "RESTORE_STRAY_BACKUP": "마지막 동기화 중에 오류가 발생했을 수 있습니다. 마지막 백업을 복원 하시겠습니까?"}, "DATETIME_SCHEDULE": {"LATER_TODAY": "나중에 오늘", "NEXT_WEEK": "다음 주", "PLACEHOLDER": "날짜를 선택하십시오", "PRESS_ENTER_AGAIN": "Enter를 다시 눌러 저장하십시오.", "TOMORROW": "내일"}, "F": {"ATTACHMENT": {"DIALOG_EDIT": {"ADD_ATTACHMENT": "첨부 파일 추가", "EDIT_ATTACHMENT": "첨부 파일 편집", "LABELS": {"FILE": "파일 경로", "IMG": "이미지", "LINK": "URL"}, "SELECT_TYPE": "유형 선택", "TYPES": {"FILE": "파일 (기본 시스템 앱에서 열림)", "IMG": "이미지 (미리보기 이미지로 표시)", "LINK": "링크 (브라우저에서 열림)"}}}, "BOARDS": {"DEFAULT": {"DONE": "수행", "EISENHAUER_MATRIX": "아이젠하우어 매트릭스", "IMPORTANT": "중요하다", "IN_PROGRESS": "진행 중", "KANBAN": "칸반", "NOT_URGENT_IMPORTANT": "긴급하지 않고 중요하지 않습니다.", "NOT_URGENT_NOT_IMPORTANT": "긴급하지 않고 중요하지 않습니다.", "TO_DO": "해야할 일", "URGENT": "긴급한", "URGENT_IMPORTANT": "긴급하고 중요한", "URGENT_NOT_IMPORTANT": "긴급하고 중요하지 않음"}, "FORM": {"ADD_NEW_PANEL": "새 패널 추가", "BACKLOG_TASK_FILTER_ALL": "모두", "BACKLOG_TASK_FILTER_NO_BACKLOG": "몰아내다", "BACKLOG_TASK_FILTER_ONLY_BACKLOG": "백로그만", "BACKLOG_TASK_FILTER_TYPE": "백로그 작업", "COLUMNS": "열", "TAGS_EXCLUDED": "제외된 태그", "TAGS_REQUIRED": "필수 태그", "TASK_DONE_STATE": "작업 완료 상태", "TASK_DONE_STATE_ALL": "모두", "TASK_DONE_STATE_DONE": "수행", "TASK_DONE_STATE_UNDONE": "취소할"}, "V": {"ADD_NEW_BOARD": "새 보드 추가", "CONFIRM_DELETE": "이 게시판을 삭제하시겠습니까?", "CREATE_NEW_TAG_BTN": "태그 만들기", "CREATE_NEW_TAG_MSG": "이 보드가 작동하려면 1개의 새 태그를 만들어야 합니다.", "CREATE_NEW_TAGS_BTN": "태그 만들기", "CREATE_NEW_TAGS_MSG": " 이 보드가 작동하려면{{nr}} 새 태그를 만들어야 합니다", "EDIT_BOARD": "게시판 편집", "NO_PANELS_BTN": "보드 구성", "NO_PANELS_MSG": "이 보드에는 패널이 구성되어 있지 않습니다. 여기에 몇 개의 패널을 추가합니다."}}, "CALDAV": {"DIALOG_INITIAL": {"TITLE": "프로젝트 용 CalDav 설정"}, "FORM": {"CALDAV_CATEGORY_FILTER": "문제를 필터링 할 카테고리 (없으면 비워 둡니다)", "CALDAV_PASSWORD": "CalDav 비밀번호", "CALDAV_RESOURCE": "CalDav 리소스의 이름 (캘린더)", "CALDAV_URL": "CalDav URL (기본 URL)", "CALDAV_USER": "CalDav 사용자 이름", "IS_TRANSITION_ISSUES_ENABLED": "작업 완료시 CalDav 할 일을 자동으로 완료"}, "FORM_SECTION": {"HELP": "<p>여기에서 일일 계획 보기의 작업 생성 패널에서 특정 프로젝트에 대해 완료되지 않은 CalDav 할 일을 나열하도록 SuperProductivity를 구성할 수 있습니다. 제안 사항으로 나열되며 할 일에 대한 링크와 이에 대한 추가 정보를 제공합니다.</p> <p>또한 완료되지 않은 모든 할 일을 작업 백로그에 자동으로 추가하고 동기화할 수 있습니다.</p><p>모바일 및 웹의 nextcloud에서 작동하도록 하려면 nextcloud 앱 <a href='https://apps.nextcloud.com/apps/webapppassword'>webapppassword<a>를 통해 \"https://app.super-productivity.com\"을 허용 목록에 추가해야 할 수 있습니다.</p>", "TITLE": "CalDav"}, "ISSUE_CONTENT": {"ASSIGNEE": "담당자", "AT": "에", "ATTACHMENTS": "첨부 파일", "CHANGED": "변경", "COMMENTS": "코멘트", "COMPONENTS": "구성 요소", "DESCRIPTION": "기술", "LABELS": "카테고리", "LIST_OF_CHANGES": "변경 사항 목록", "MARK_AS_CHECKED": "체크 된대로 업데이트 표시", "ON": "에", "RELATED": "관련", "STATUS": "상태", "STORY_POINTS": "스토리 포인트", "SUB_TASKS": "하위", "SUMMARY": "개요", "WORKLOG": "작업 로그", "WRITE_A_COMMENT": "덧글 써"}, "S": {"CALENDAR_NOT_FOUND": "CalDav : 캘린더 \"{{calendarName}}\"을 (를) 찾을 수 없음", "CALENDAR_READ_ONLY": "CalDav : 캘린더 \"{{calendarName}}\"은 (는) 읽기 전용입니다.", "ISSUE_NOT_FOUND": "CalDav : 서버에서 할 일 \"{{issueId}}\"이 (가) 삭제 된 것 같습니다."}}, "CALENDARS": {"BANNER": {"ADD_AS_TASK": "작업으로 추가", "FOCUS_TASK": "집중 작업", "TXT": "<strong>{{title}}</strong> 는 <strong>에서 시작 {{start}}</strong>!", "TXT_MULTIPLE": "<strong>{{title}}</strong> 는 <strong>에서 시작 {{start}}</strong>!<br> (및 {{nrOfOtherBanners}} 개의 다른 이벤트가 마감됨)", "TXT_PAST": "<strong>{{title}}</strong> 는 <strong>에서 시작 {{start}}</strong>!", "TXT_PAST_MULTIPLE": "<strong>{{title}}</strong> 는 <strong>에서 시작 {{start}}</strong>!<br> (및 {{nrOfOtherBanners}} 개의 다른 이벤트가 마감됨)"}, "S": {"CAL_PROVIDER_ERROR": "일정 공급자 오류입니다: {{errTxt}}"}}, "CONFIG": {"S": {"UPDATE_SECTION": " <strong>{{sectionKey}}의 업데이트 된 설정</strong>"}}, "D_RATE": {"A_HOW": "평가 방법 및 위치", "BTN_DONT_BOTHER": "다시는 나를 괴롭히지 마라", "TITLE": "🙈 부디 용서해 주세요만...", "TXT": "당신이 그것을 좋아한다면 <strong>좋은 평가를 주면 프로젝트에 엄청난 도움이 될 것입니다!</strong>"}, "DOMINA_MODE": {"FORM": {"HELP": "작업 시간을 추적할 때 구성된 문구를 X마다 반복합니다.", "L_INTERVAL": "구문 반복 간격", "L_TEXT": "텍스트", "L_TEXT_DESCRIPTION": "예: \"{currentTaskTitle}달러 작업!\"", "L_VOICE": "음색 선택", "L_VOICE_DESCRIPTION": "음성 선택", "TITLE": "도미나 모드"}}, "DROPBOX": {"S": {"ACCESS_TOKEN_ERROR": "Dropbox : 인증 코드에서 액세스 토큰을 생성 할 수 없습니다", "ACCESS_TOKEN_GENERATED": "Dropbox : 인증 코드에서 생성 된 액세스 토큰", "AUTH_ERROR": "보관 용 계정 : 잘못된 액세스 토큰 제공", "AUTH_ERROR_ACTION": "토큰 변경", "OFFLINE": "보관 용 계정 : 오프라인 상태이므로 동기화 할 수 없습니다", "SYNC_ERROR": "보관 용 계정 : 동기화 중 오류", "UNABLE_TO_GENERATE_PKCE_CHALLENGE": "Dropbox: PKCE 챌린지를 생성할 수 없습니다."}}, "FINISH_DAY_BEFORE_EXIT": {"C": {"FINISH_DAY_BEFORE_EXIT": "오늘 목록에 아직 아카이브로 이동되지 않은 완료된 작업이 {{nr}} 개 있습니다. 하루를 끝내지 않고 정말 종료하시겠습니까?"}}, "FOCUS_MODE": {"B": {"SESSION_RUNNING": "포커스 세션이 실행 중입니다.", "TO_FOCUS_OVERLAY": "오버레이에 초점을 맞추려면"}, "BACK_TO_PLANNING": "계획으로 돌아가기", "CONGRATS": "이 세션을 완료해 주셔서 축하드립니다!", "CONTINUE_FOCUS_SESSION": "포커스 세션 계속하기", "COUNTDOWN": "카운트다운", "FINISH_TASK_AND_SELECT_NEXT": "작업 완료 후 다음 선택", "FLOWTIME": "플로우 타임", "FOR_TASK": "작업용", "GET_READY": "집중 세션을 준비하세요!", "GO_TO_PROCRASTINATION": "미루는 일이 있을 때 도움 받기", "GOGOGO": "가자, 가자, 가자!!!", "NEXT": "다음", "ON": "켜짐", "OPEN_ISSUE_IN_BROWSER": "브라우저에서 열린 이슈", "POMODORO_BACK": "뒤로", "POMODORO_DISABLE": "포모도로 비활성화", "POMODORO_INFO": "포커스 세션은 포모도로 타이머가 활성화된 상태에서 함께 사용할 수 없습니다.", "PREP_GET_MENTALLY_READY": "집중력과 생산성을 높이기 위한 정신적 준비", "PREP_SIT_UPRIGHT": "똑바로 앉거나 서기", "PREP_STRETCH": "가벼운 스트레칭하기", "SELECT_ANOTHER_TASK": "다른 작업 선택", "SELECT_TASK": "집중할 작업 선택", "SESSION_COMPLETED": "포커스 세션 완료!", "SET_FOCUS_SESSION_DURATION": "포커스 세션 기간 설정", "SHOW_HIDE_NOTES_AND_ATTACHMENTS": "작업 노트 및 첨부 파일 표시/숨기기", "START_FOCUS_SESSION": "포커스 세션 시작", "START_NEXT_FOCUS_SESSION": "다음 포커스 세션 시작", "WORKED_FOR": "근무처"}, "GITEA": {"DIALOG_INITIAL": {"TITLE": "프로젝트용 Gitea 설정"}, "FORM": {"FILTER_USER": "사용자 아이디(예: 직접 변경 사항을 필터링하기 위해)", "HOST": "호스트(예: https://try.gitea.io)", "REPO_FULL_NAME": "사용자 이름 또는 조직 이름/프로젝트", "REPO_FULL_NAME_DESCRIPTION": "브라우저에서 프로젝트를 볼 때 URL의 일부로 찾을 수 있습니다.", "SCOPE": "범위", "SCOPE_ALL": "모두", "SCOPE_ASSIGNED": "나에게 할당됨", "SCOPE_CREATED": "작성자: 나", "TOKEN": "액세스 토큰"}, "FORM_SECTION": {"HELP": "<p>여기에서 일일 계획 보기의 작업 만들기 패널에서 특정 리포지토리에 대해 열려 있는 Gitea 이슈를 나열하도록 SuperProductivity를 구성할 수 있습니다. 이슈는 제안 사항으로 나열되며 이슈에 대한 링크와 자세한 정보를 제공합니다.</p> <p>또한 열려 있는 모든 이슈를 자동으로 추가하고 가져올 수 있습니다.</p><p>사용량 제한을 통과하고 액세스하려면 액세스 토큰을 제공할 수 있습니다.", "TITLE": "<PERSON><PERSON><PERSON>"}, "ISSUE_CONTENT": {"ASSIGNEE": "담당자", "AT": "에서", "DESCRIPTION": "설명", "LABELS": "레이블", "MARK_AS_CHECKED": "업데이트 확인됨으로 표시", "PROJECT": "프로젝트", "STATUS": "상태", "SUMMARY": "요약", "WRITE_A_COMMENT": "댓글 작성"}, "S": {"ERR_UNKNOWN": "Gitea: 알 수 없는 오류 {{statusCode}} {{errorMsg}}. API 속도 제한이 초과되었나요?"}}, "GITHUB": {"DIALOG_INITIAL": {"TITLE": "프로젝트를 위한 GitHub 설정"}, "FORM": {"FILTER_USER": "사용자 이름 (예 : 혼자서 변경 사항을 필터링)", "INVALID_TOKEN_MESSAGE": "유효한 GitHub 토큰이 아닙니다. \"ghp_\"로 시작해야 합니다.", "IS_ASSIGNEE_FILTER": "나에게 할당된 이슈만 백로그에 가져오기", "REPO": "추적하고자하는 git 저장소의 \"username / repositoryName\"", "TOKEN": "액세스 토큰"}, "FORM_SECTION": {"HELP": "<p>여기서는 일일 계획 보기의 작업 만들기 패널에서 특정 리포지토리에 대한 열린 GitHub 문제를 나열하도록 SuperProductivity를 구성할 수 있습니다. 제안으로 나열 되며 문제에 대 한 링크 뿐만 아니라 그것에 대 한 자세한 정보를 제공 합니다.</p> <p>또한 자동으로 추가하고 작업 백로그에 열려있는 모든 문제를 동기화 할 수 있습니다.</p>", "TITLE": "<PERSON><PERSON><PERSON>"}, "ISSUE_CONTENT": {"ASSIGNEE": "담당자", "AT": "...에서", "DESCRIPTION": "기술", "LABELS": "레이블", "LAST_COMMENT": "마지막 댓글", "LOAD_ALL_COMMENTS": "모든 {{nr}} 개의 댓글을 불러오기", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "설명 및 모든 댓글 로드", "MARK_AS_CHECKED": "체크 된대로 업데이트 표시", "STATUS": "상태", "SUMMARY": "개요", "WRITE_A_COMMENT": "의견 쓰기"}, "S": {"CONFIG_ERROR": "GitHub: 데이터를 매핑하는 동안 오류가 발생했습니다. 리포지토리 이름이 맞나요?", "ERR_UNKNOWN": "GitHub : 알 수없는 오류 {{statusCode}} {{errorMsg}}"}}, "GITLAB": {"DIALOG_INITIAL": {"TITLE": "프로젝트를위한 GitLab 설정"}, "*********************": {"PAST_DAY_INFO": "미리 채워진 기간에는 지난 날짜의 추적되지 않은 데이터가 포함되어 있습니다.", "T_ALREADY_TRACKED": "이미 추적 중", "T_TITLE": "제목", "T_TO_BE_SUBMITTED": "제출 대상", "TITLE": "이슈에 소요된 시간을 GitLab에 제출하기", "TOTAL_MSG": "오늘 총 <em>{{totalTimeToSubmit}}</em> 개의 작업 시간을 <em>{{nrOfTasksToSubmit}}</em> 개의 다른 문제에 제출해야 합니다."}, "FORM": {"FILTER": "사용자 지정 필터", "FILTER_DESCRIPTION": "Https://docs.gitlab.com/ee/api/issues.html#list-issues 참조. 여러 개를 &로 결합할 수 있습니다.", "FILTER_USER": "사용자 이름 (예 : 혼자서 변경 사항을 필터링)", "GITLAB_BASE_URL": "맞춤 GitLab 기본 URL", "PROJECT": "전체 경로 또는 사용자 이름 / 프로젝트", "PROJECT_HINT": "예: 요하네스조/슈퍼생산성", "SCOPE": "범위", "SCOPE_ALL": "모든", "SCOPE_ASSIGNED": "나에게 할당됨", "SCOPE_CREATED": "내가 만든", "SOURCE": "출처", "SOURCE_GLOBAL": "모든", "SOURCE_GROUP": "그룹", "SOURCE_PROJECT": "계획", "SUBMIT_TIMELOGS": "깃랩에 타임로그 제출하기", "SUBMIT_TIMELOGS_DESCRIPTION": "완료일을 클릭한 후 시간 추적 대화 상자 표시", "TOKEN": "액세스 토큰"}, "FORM_SECTION": {"HELP": "<p> 일별 계획보기의 작업 생성 패널에서 특정 프로젝트에 대해 열린 GitLab (온라인 버전 또는 자체 호스팅 인스턴스) 문제를 나열하도록 SuperProductivity를 구성 할 수 있습니다. 제안으로 표시되며 문제에 대한 자세한 정보뿐만 아니라 문제에 대한 링크를 제공합니다. </p> <p> 또한 열려있는 모든 문제를 작업 백 로그에 자동으로 추가하고 동기화 할 수 있습니다. </p>", "TITLE": "GitLab"}, "ISSUE_CONTENT": {"ASSIGNEE": "담당자", "AT": "...에서", "DESCRIPTION": "기술", "LABELS": "레이블", "MARK_AS_CHECKED": "체크 된대로 업데이트 표시", "PROJECT": "계획", "STATUS": "상태", "SUMMARY": "개요", "WRITE_A_COMMENT": "의견 쓰기"}, "S": {"ERR_UNKNOWN": "GitLab : 알 수없는 오류 {{statusCode}} {{errorMsg}}"}}, "ISSUE": {"CROSS_ORIGIN_BROWSER_WARNING": "이 통합 기능은 브라우저에서 작동하지 않을 수 있습니다. 데스크톱 또는 Android 버전의 슈퍼 생산성을 다운로드하세요!", "DEFAULT": {"ISSUE_STR": "발행물", "ISSUES_STR": "문제"}, "DEFAULT_PROJECT_DESCRIPTION": "이슈에서 생성된 작업에 프로젝트가 할당됩니다.", "DEFAULT_PROJECT_LABEL": "기본 슈퍼 생산성 프로젝트", "HOW_TO_GET_A_TOKEN": "토큰은 어떻게 받나요?", "ISSUE_CONTENT": {"ASSIGNEE": "담당자", "AT": "에", "ATTACHMENTS": "첨부 파일", "AUTHOR": "저자", "CATEGORY": "범주", "CHANGED": "변경", "COMMENTS": "코멘트", "COMPONENTS": "구성 요소", "DESCRIPTION": "묘사", "DONE_RATIO": "완료 비율", "DUE_DATE": "마감일", "LABELS": "레이블", "LAST_COMMENT": "마지막 코멘트", "LIST_OF_CHANGES": "변경 사항 목록", "LOAD_ALL_COMMENTS": "모든 {{nr}} 댓글 불러오기", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "설명 &모든 의견 불러오기", "LOCATION": "위치", "MARK_AS_CHECKED": "업데이트를 선택된 것으로 표시", "ON": "에", "PRIORITY": "우선권", "RELATED": "관련", "START": "시작하다", "STATUS": "상태", "STORY_POINTS": "스토리 포인트", "SUB_TASKS": "하위", "SUMMARY": "요약", "TIME_SPENT": "소요 시간", "TYPE": "형", "VERSION": "버전", "WORKLOG": "작업 로그", "WRITE_A_COMMENT": "덧글 써"}, "S": {"ERR_GENERIC": "{{issue<PERSON><PERSON><PERSON><PERSON>ame}} 오류입니다: {{errTxt}}", "ERR_NETWORK": "{{issueProviderName}}: 클라이언트 측 네트워크 오류로 인해 요청이 실패했습니다.", "ERR_NOT_CONFIGURED": "{{issueProviderName}}: 제대로 구성되지 않음", "IMPORTED_MULTIPLE_ISSUES": "{{issueProviderName}}: 백로그에 {{nr}} 개의 새 {{issuesStr}} 가져오기", "IMPORTED_SINGLE_ISSUE": "{{issueProviderName}}: {{issueStr}} \"{{issueTitle}}\"을(를) 백로그로 가져왔습니다.", "ISSUE_DELETED_OR_CLOSED": "{{issueProviderName}}: {{issueStrC}} \"{{issueTitle}}\"이(가) 삭제되었거나 닫힌 것 같습니다.", "ISSUE_NO_UPDATE_REQUIRED": "{{issueProviderName}}: 업데이트 필요 없음", "ISSUE_UPDATE_MULTIPLE": "{{issueProviderName}}: {{nr}} {{issuesStr}}에 대한 업데이트된 데이터", "ISSUE_UPDATE_SINGLE": "{{issueProviderName}}: \"{{issueTitle}}\"에 대한 업데이트된 데이터", "MANUAL_UPDATE_ISSUE_SUCCESS": "{{issueProviderName}}: \"{{issueTitle}}\"에 대한 업데이트된 데이터", "MISSING_ISSUE_DATA": "{{issueProviderName}}: 누락된 {{issueStr}} 데이터가 있는 작업을 찾았습니다. 다시 로드 중.", "NEW_COMMENT": "{{issueProviderName}}: \"{{issueTitle}}\"에 대한 새 댓글", "POLLING_BACKLOG": "{{issueProviderName}}: 새 {{issuesStr}}에 대한 폴링", "POLLING_CHANGES": "{{issueProviderName}}: {{issuesStr}}에 대한 폴링 변경 사항"}}, "JIRA": {"BANNER": {"BLOCK_ACCESS_MSG": "Jira : API에서 종료되는 것을 막기 위해 Super Productivity가 액세스를 차단했습니다. 아마도 jira 설정을 확인해야합니다!", "BLOCK_ACCESS_UNBLOCK": "차단 해제"}, "CFG_CMP": {"ALWAYS_ASK": "항상 대화 상자를 엽니 다.", "DO_NOT": "전환하지 마라.", "DONE": "작업 완료 상태", "ENABLE": "Jira 통합 사용", "ENABLE_TRANSITIONS": "전환 처리 사용", "IN_PROGRESS": "작업 시작 상태", "LOAD_SUGGESTIONS": "로드 제안", "MAP_CUSTOM_FIELDS": "사용자 정의 필드 매핑", "MAP_CUSTOM_FIELDS_INFO": "유감스럽게도 Jira의 데이터 중 일부는 모든 설치마다 다른 사용자 정의 필드 아래에 저장됩니다. 이 데이터를 포함 시키려면 적절한 사용자 정의 필드를 선택해야합니다.", "OPEN": "작업 일시 중지 상태", "SELECT_ISSUE_FOR_TRANSITIONS": "사용 가능한 전환을로드하려면 문제를 선택하십시오.", "STORY_POINTS": "스토리 포인트", "TRANSITION": "전환 처리"}, "DIALOG_CONFIRM_ASSIGNMENT": {"MSG": "<strong>{{summary}}</strong> 이 (가) 현재 <strong>{{assignee}}</strong>에게 할당되었습니다. 그것을 당신 자신에게 할당하고 싶습니까?", "OK": "해!"}, "DIALOG_INITIAL": {"TITLE": "프로젝트 용 Jira 설치"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "할당 할 상태 선택", "CURRENT_ASSIGNEE": "현재 담당자 :", "CURRENT_STATUS": "현재 상태:", "TASK_NAME": "작업 이름:", "TITLE": "Jira : 업데이트 상태", "UPDATE_STATUS": "업데이트 상태"}, "DIALOG_WORKLOG": {"CHECKBOXES": {"ALL_TIME": "항상 작업에 소요된 모든 시간을 기본값으로 사용", "ALL_TIME_MINUS_LOGGED": "항상 기본으로 기록된 시간에서 소요 시간을 뺀 시간만 사용", "TIME_SPENT_TODAY": "항상 오늘 보낸 시간만 기본값으로 사용", "TIME_SPENT_YESTERDAY": "항상 어제 보낸 시간만 기본값으로 사용"}, "CURRENTLY_LOGGED": "현재 기록 된 시간 :", "INVALID_DATE": "입력 한 값이 날짜가 아닙니다!", "SAVE_WORKLOG": "작업 기록 저장", "STARTED": "시작됨", "SUBMIT_WORKLOG_FOR": "Jira에게 작업 로그 제출", "TIME_SPENT": "지출 된 시간", "TIME_SPENT_TOOLTIP": "다른 시간 추가", "TITLE": "Jira : 작업 로그 제출"}, "FORM_ADV": {"AUTO_ADD_BACKLOG_JQL_QUERY": "백 로그에 작업을 자동으로 추가하는 데 사용되는 JQL", "IS_ADD_WORKLOG_ON_SUB_TASK_DONE": "하위 작업이 완료되면 작업 로그를 jira에 제출하기위한 열기 대화 상자", "IS_CHECK_TO_RE_ASSIGN_TICKET_ON_TASK_START": "현재 발행 된 문제가 현재 사용자에게 할당되었는지 확인", "IS_WORKLOG_ENABLED": "작업이 완료되면 작업 로그를 jira로 제출하기위한 열기 대화 상자", "SEARCH_JQL_QUERY": "검색 자 작업을 제한하기위한 JQL 쿼리", "WORKLOG_DEFAULT_ALL_TIME": "작업에 소요된 모든 시간 입력", "WORKLOG_DEFAULT_ALL_TIME_MINUS_LOGGED": "모든 소요 시간에서 기록 시간을 뺀 값을 입력하세요.", "WORKLOG_DEFAULT_TIME_MODE": "대화의 기본 시간 값", "WORKLOG_DEFAULT_TODAY": "오늘 보낸 시간만 채우세요", "WORKLOG_DEFAULT_YESTERDAY": "어제 보낸 시간만 채우기"}, "FORM_CRED": {"ALLOW_SELF_SIGNED": "자체 서명 된 인증서 허용", "HOST": "호스트 (예 : http://my-host.de:1234)", "PASSWORD": "토큰 / 비밀번호", "USE_PAT": "비밀번호 대신 개인 액세스 토큰 사용", "USER_NAME": "이메일 / 사용자 이름", "WONKY_COOKIE_MODE": "Wonky <PERSON><PERSON> Fallback Authentication (데스크톱 앱만 해당)"}, "FORM_SECTION": {"ADV_CFG": "고급 구성", "HELP_ARR": {"H1": "기본 구성", "H2": "작업 로그 설정", "H3": "기본 전환", "P1_1": "어떤 이유로 든 생성 할 수없는 경우 로그인 이름 (프로필 페이지에서 찾을 수 있음)과 <a target=\"_blank\" href=\"https://confluence.atlassian.com/cloud/api-tokens-938839638.html\" target=\"_blank\">API 토큰</a> 또는 비밀번호를 제공하십시오. 최신 버전의 jira가 토큰과 만 작동하는 경우도 있습니다.", "P1_2": "또한 Jira에서 작업을 추가하는 제안에 사용되는 JQL 쿼리를 지정해야합니다. 도움이 필요한 경우 <a target=\"_blank\" href=\"https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html\" target=\"_blank\">https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html</a>링크를 확인하십시오.", "P1_3": "또한 자동으로 (예 : 계획보기를 방문 할 때마다) 사용자 정의 JQL 쿼리에 지정된 새 작업을 모두 백 로그에 추가하도록 구성 할 수 있습니다.", "P1_4": "또 다른 옵션은 \"현재 티켓이 현재 사용자에게 지정되었는지 확인\"입니다. 활성화되어 있고 시작 중이면 현재 Jira에서 해당 티켓에 할당 된 경우 확인이 수행됩니다. 대화 상자가 나타나지 않으면 티켓을 직접 할당 할 수 있습니다.", "P2_1": "작업 로그를 제출할시기와 방법을 결정할 수있는 몇 가지 옵션이 있습니다. 작업 완료시 작업 로그를 추가하는 <em>'작업 로그 열기'대화 상자 사용 '</em> 은 Jira 작업을 완료로 표시 할 때마다 작업 로그를 추가하는 대화 상자를 엽니 다. 지금까지 추적 된 모든 항목 위에 작업 로그가 추가 될 것입니다. 따라서 두 번째로 작업을 완료로 표시하면 작업의 전체 작업 시간을 다시 제출하지 않을 수 있습니다.", "P2_2": "<em>'Jira 문제의 하위 작업을 완료로 표시 할 때마다'</em> 은 작업 로그 대화 상자를 열 때마다 하위 작업이 완료되면 작업 로그 열기 대화 상자가 열립니다. 하위 작업을 통해 시간을 이미 추적 했으므로 Jira 작업 자체를 완료로 표시하면 대화 상자가 열리지 않습니다.", "P2_3": "<em>'대화 상자없이 작업 로그에 자동으로 업데이트를 보냄'{</em> 은 (는) 말하는 것을 않습니다. 여러 번 완료된 작업을 표시하면 전체 작업 시간이 두 번 추적되기 때문에 권장하지 않습니다.", "P3_1": "여기서 기본 전환을 재구성 할 수 있습니다. Jira는 일반적으로 Jira 애자일 보드의 다른 컬럼으로 동작하는 다양한 전환을 가능하게합니다. 우리는 어디에서 언제 작업을 전환 할 것인지를 가정 할 수 없으며 수동으로 설정해야합니다."}}, "ISSUE_CONTENT": {"ASSIGNEE": "담당자", "AT": "...에서", "ATTACHMENTS": "첨부 파일", "CHANGED": "변경된", "COMMENTS": "코멘트", "COMPONENTS": "구성 요소", "DESCRIPTION": "기술", "LIST_OF_CHANGES": "변경 목록", "MARK_AS_CHECKED": "체크 된대로 업데이트 표시", "ON": "에", "RELATED": "관련", "STATUS": "상태", "STORY_POINTS": "스토리 포인트", "SUB_TASKS": "하위 작업", "SUMMARY": "개요", "WORKLOG": "작업 로그", "WRITE_A_COMMENT": "의견 쓰기"}, "S": {"ADDED_WORKLOG_FOR": "Jira : {{<PERSON><PERSON><PERSON>}}에 대한 작업 기록 추가됨", "EXTENSION_NOT_LOADED": "Super Productivity Extension이로드되지 않았습니다. 페이지를 새로 고침하면 도움이 될 수 있습니다.", "INSUFFICIENT_SETTINGS": "Jira에 대한 설정이 충분하지 않음", "INVALID_RESPONSE": "Jira: 응답에 잘못된 데이터가 포함되어 있습니다.", "ISSUE_NO_UPDATE_REQUIRED": "<PERSON><PERSON> : \"{{issueText}}\"이 (가) 이미 업데이트되었습니다.", "MANUAL_UPDATE_ISSUE_SUCCESS": "<PERSON>ra : \"{{issueText}}\"에 대한 데이터가 업데이트되었습니다.", "MISSING_ISSUE_DATA": "Jira : 누락 된 문제 데이터가있는 작업이 발견되었습니다. 다시로드 중입니다.", "NO_AUTO_IMPORT_JQL": "Jira : 자동 가져 오기에 대해 정의 된 검색어가 없습니다.", "NO_VALID_TRANSITION": "Jira : 유효한 전환이 설정되지 않았습니다.", "TIMED_OUT": "Jira : 요청 시간이 초과되었습니다.", "TRANSITION": "<PERSON><PERSON> : \"{{<PERSON><PERSON><PERSON>}}\"문제를 \"{{name}}\"(으)로 설정하십시오.", "TRANSITION_SUCCESS": "Jira : {{<PERSON><PERSON><PERSON>}} 문제를 <strong>{{chosenTransition}}</strong>(으)로 설정하십시오.", "TRANSITIONS_LOADED": "Jira : 전환이로드되었습니다. 아래 선택을 사용하여 지정하십시오", "UNABLE_TO_REASSIGN": "Jira : 사용자 이름을 지정하지 않았기 때문에 자신에게 티켓을 재 할당 할 수 없습니다. 설정을 방문하십시오."}, "STEPPER": {"CREDENTIALS": "신임장", "DONE": "이제 끝났어.", "LOGIN_SUCCESS": "성공적 로그인!", "TEST_CREDENTIALS": "자격증 명 테스트", "WELCOME_USER": "환영합니다,  {{user}}!"}}, "MARKDOWN_PASTE": {"CONFIRM_ADD_TO_SUB_TASK_NOTES": "붙여 넣은 마크 다운 목록을 하위 작업 \"{{parentTaskTitle}}\"의 노트에 추가 하시겠습니까?", "CONFIRM_PARENT_TASKS": "붙여 넣은 마크 다운 목록에서</strong>  <strong>{{tasksCount}} 새 작업을 생성 하시겠습니까?", "CONFIRM_PARENT_TASKS_WITH_SUBS": "붙여넣은 마크다운 목록에서</strong>  <strong>{{tasksCount}} 새 작업 및 {{subTasksCount}} 하위 작업을 생성하시겠습니까?", "CONFIRM_SUB_TASKS": "붙여 넣은 마크 다운 목록에서 {{tasksCount}} 개의 새로운 하위 작업을 만드시겠습니까?", "CONFIRM_SUB_TASKS_WITH_PARENT": "붙여넣은 마크다운 목록의 \"{{parentTaskTitle}}\" 아래에</strong>  <strong>{{tasksCount}} 새 하위 작업을 생성하시겠습니까?", "DIALOG_TITLE": "붙여 넣은 마크 다운 목록이 감지되었습니다!"}, "METRIC": {"BANNER": {"CHECK": "내가 해냈어!"}, "CMP": {"AVG_BREAKS_PER_DAY": "평균 하루에 휴식", "AVG_TASKS_PER_DAY_WORKED": "평균 일일 작업 수", "AVG_TIME_SPENT_ON_BREAKS": "평균 휴식 시간", "AVG_TIME_SPENT_PER_DAY": "평균 하루에 보낸 시간", "AVG_TIME_SPENT_PER_TASK": "평균 과제 당 소비 된 시간", "COUNTING_SUBTASKS": "(하위 작업 수 계산)", "DAYS_WORKED": "근무일", "GLOBAL_METRICS": "글로벌 지표", "IMPROVEMENT_SELECTION_COUNT": "개선 계수가 선택된 횟수", "MOOD_PRODUCTIVITY_OVER_TIME": "시간 경과에 따른 기분과 생산성", "NO_ADDITIONAL_DATA_YET": "추가로 수집 된 데이터가 없습니다. 일일 요약 '평가'패널에있는 양식을 사용하십시오.", "OBSTRUCTION_SELECTION_COUNT": "장애 요소가 선택 된 횟수", "SIMPLE_CLICK_COUNTERS_OVER_TIME": "시간 경과에 따른 클릭 카운터", "SIMPLE_COUNTERS": "간단한 카운터", "SIMPLE_STOPWATCH_COUNTERS_OVER_TIME": "시간 경과에 따른 스톱워치 카운터", "TASKS_DONE_CREATED": "할 일 목록 (완료 / 생성)", "TIME_ESTIMATED": "예상 시간", "TIME_SPENT": "지출 된 시간"}, "EVAL_FORM": {"ADD_NOTE_FOR_TOMORROW": "내일 메모 추가", "DISABLE_REPEAT_EVERY_DAY": "매일 반복 사용 안함", "ENABLE_REPEAT_EVERY_DAY": "매일 반복", "HELP_H1": "왜 내가 신경 써야하니?", "HELP_LINK_TXT": "통계 섹션으로 이동", "HELP_P1": "조금 자기 평가 시간! 여기에 귀하의 답변이 저장되어 메트릭 섹션에서 어떻게 작업하는지에 대한 통계를 제공합니다. 또한 내일을위한 제안은 다음날 작업 목록 위에 나타납니다.", "HELP_P2": "이는 정확한 측정 항목을 계산하거나 작업에 대한 사용자의 감각을 향상시키는 것보다 효율적이기는하지만 효율적인 작업이되도록하기위한 것입니다. 매일의 일상 생활에서 고통 점을 평가하는 것은 도움이 될뿐만 아니라 당신을 도울 요인을 찾는 것입니다. 그것에 대해 조금 체계적으로 다루기 만하면 이들에 대해 더 잘 이해하고 가능한 한 향상시키는 데 도움이되기를 바랍니다.", "IMPROVEMENTS": "무엇이 당신의 생산성을 향상 시켰습니까?", "IMPROVEMENTS_TOMORROW": "내일 개선하기 위해 당신은 무엇을 할 수 있습니까?", "MOOD": "기분 어때?", "MOOD_HINT": "1 : 끔찍 - 10 : 훌륭함", "NOTES": "내일위한주의 사항", "OBSTRUCTIONS": "당신의 생산성을 방해 한 것은 무엇입니까?", "PRODUCTIVITY": "얼마나 효율적으로 일 했습니까?", "PRODUCTIVITY_HINT": "1 : 시작조차하지 마라 - 10 : 매우 효율적이다."}, "S": {"SAVE_METRIC": "측정 항목이 성공적으로 저장되었습니다."}}, "NOTE": {"D_ADD": {"NOTE_LABEL": "메모로 저장할 텍스트를 입력하십시오 ..."}, "D_FULLSCREEN": {"VIEW_PARSED": "구문 분석된(편집 불가) 마크다운으로 보기", "VIEW_SPLIT": "분할 보기에서 구문 분석된 마크다운과 구문 분석되지 않은 마크다운 보기", "VIEW_TEXT_ONLY": "구문 분석되지 않은 텍스트로 보기"}, "NOTE_CMP": {"DISABLE_PARSE": "마크 다운 구문 분석 사용 중지", "ENABLE_PARSE": "markdown 구문 분석 사용"}, "NOTES_CMP": {"ADD_BTN": "새 메모 추가", "DROP_TO_ADD": "새 메모를 추가하려면 여기로 드래그하십시오.", "NO_NOTES": "현재 메모가 없습니다"}, "S": {"NOTE_ADDED": "노트 저장"}}, "OPEN_PROJECT": {"CFG_CMP": {"ALWAYS_ASK": "항상 열려 있는 대화 상자", "DO_NOT": "전환하지 않기", "DONE": "작업 완료 상태", "ENABLE": "오픈 프로젝트 통합 사용", "ENABLE_TRANSITIONS": "전환 처리 활성화", "IN_PROGRESS": "작업 시작 상태", "OPEN": "작업 일시 중지 상태", "PROGRESS_ON_SAVE": "저장 시 기본 진행 상황", "SELECT_ISSUE_FOR_TRANSITIONS": "이슈를 선택하여 사용 가능한 전환을 로드합니다.", "TRANSITION": "전환 처리"}, "DIALOG_INITIAL": {"TITLE": "프로젝트용 OpenProject 설정"}, "DIALOG_TRACK_TIME": {"ACTIVITY": "활동", "CURRENTLY_LOGGED": "현재 기록 된 시간 :", "INVALID_DATE": "입력 한 값이 날짜가 아닙니다!", "POST_TIME": "게시 시간", "STARTED": "시작됨", "SUBMIT_TIME_FOR": "OpenProject에 시간 제출", "TIME_SPENT": "지출 된 시간", "TITLE": "OpenProject: 작업 로그 제출"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "할당할 상태 선택", "CURRENT_ASSIGNEE": "현재 담당자:", "CURRENT_STATUS": "현재 상태:", "PERCENTAGE_DONE": "진행 상황:", "TASK_NAME": "작업 이름:", "TITLE": "오픈프로젝트: 업데이트 상태", "UPDATE_STATUS": "업데이트 상태"}, "FORM": {"FILTER_USER": "사용자 이름 (예 : 혼자서 변경 사항을 필터링)", "HOST": "호스트(예: https://www.openproject.org/)", "IS_SHOW_TIME_TRACKING_DIALOG": "OpenProject에 보고하는 시간 추적 대화 상자 표시", "IS_SHOW_TIME_TRACKING_DIALOG_DESCRIPTION": "OpenProject 프로젝트에 대해 시간 추적 모듈을 활성화해야 합니다.", "IS_SHOW_TIME_TRACKING_DIALOG_FOR_EACH_SUB_TASK": "하위 작업이 완료되면 시간 추적 대화 상자 표시", "PROJECT_ID": "프로젝트 ID", "PROJECT_ID_DESCRIPTION": "브라우저에서 프로젝트를 볼 때 URL의 일부로 찾을 수 있습니다.", "SCOPE": "범위", "SCOPE_ALL": "모든", "SCOPE_ASSIGNED": "나에게 할당됨", "SCOPE_CREATED": "내가 만든", "TOKEN": "액세스 토큰"}, "FORM_SECTION": {"HELP": "<p>여기에서 열려 있는 OpenProject 작업 패키지를 나열하도록 SuperProductivity를 구성할 수 있습니다. 이것이 브라우저에서 작동하려면 app.super-productivity.com</p>에서 액세스를 허용하도록 OpenProject 서버에 대해 CORS를 구성해야 합니다.", "TITLE": "오픈프로젝트"}, "ISSUE_CONTENT": {"ASSIGNEE": "담당자", "ATTACHMENTS": "첨부파일", "DESCRIPTION": "기술", "MARK_AS_CHECKED": "체크 된대로 업데이트 표시", "STATUS": "상태", "SUMMARY": "개요", "TYPE": "유형", "UPLOAD_ATTACHMENT": "작업에 업로드"}, "ISSUE_STRINGS": {"ISSUE_STR": "작업 패키지", "ISSUES_STR": "작업 패키지"}, "S": {"ERR_NO_FILE": "선택된 파일이 없습니다", "ERR_UNKNOWN": "OpenProject: 알 수 없는 오류 {{statusCode}} {{errorMsg}}. CORS가 서버에 대해 올바르게 구성되어 있습니까?", "POST_TIME_SUCCESS": "OpenProject: {{issueTitle}}에 대한 시간 항목을 성공적으로 생성했습니다.", "TRANSITION": "OpenProject: 문제 \"{{<PERSON><PERSON><PERSON>}}\"를 \"{{name}}\"로 설정합니다. \"", "TRANSITION_SUCCESS": "OpenProject: 문제 {{issue<PERSON><PERSON>}} 을(를) <strong>로 설정 {{chosenTransition}}</strong>", "TRANSITIONS_LOADED": "전환이 로드되었습니다. 아래 선택 항목을 사용하여 할당합니다."}}, "PLANNER": {"D": {"ADD_PLANNED": {"ADD_TO_TODAY": "오늘에 추가", "RE_PLAN_ALL": "모두 일정 변경", "TITLE": "오늘에 계획된 작업 추가"}}, "EDIT_REPEATED_TASK": "반복 작업 편집 '{{taskName}}'", "NO_TASKS": "작업 없음", "PLAN_VIEW": {"NO_SCHEDULED_ITEMS": "예약된 항목 없음"}, "S": {"REMOVED_PLAN_DATE": "작업 '{{taskTitle}}' 의 계획 날짜를 제거했습니다.", "TASK_ALREADY_PLANNED": "작업이 이미 {{date}}에 대해 계획되어 있습니다.", "TASK_PLANNED_FOR": " <strong>{{date}}</strong>{{extra}}에 대한 작업이 계획되었습니다."}, "TASK_DRAWER": "작업 서랍"}, "POMODORO": {"BACK_TO_WORK": "다시 일!", "BREAK_IS_DONE": "너의 휴식은 끝났어!", "ENJOY_YOURSELF": "자신을 즐기고, 스스로 움직여서 다시 들어가십시오.", "FINISH_SESSION_X": " <strong>{{nr}}</strong>세션을 성공적으로 마쳤습니다!", "NOTIFICATION": {"BREAK_TIME": "포모도로: 휴식 시간 {{nr}}!", "BREAK_X_START": "Pomodoro : 휴식 {{nr}} 시작!", "NO_TASKS": "Pomodoro 타이머가 시작되기 전에 작업을 추가해야 합니다.", "SESSION_X_START": "Pomodoro : 세션 {{nr}} 이 시작되었습니다."}, "S": {"RESET": "첫 번째 포모도로 세션으로 재설정", "SESSION_SKIP": "현재 포모도로 세션 끝으로 건너뛰기", "SESSION_X_START": "Pomodoro : 세션 {{nr}} 이 시작되었습니다."}, "SKIP_BREAK": "휴식 시간 건너 뛰기", "START_BREAK": "휴식 시작"}, "PROCRASTINATION": {"BACK_TO_WORK": "다시 일!", "COMP": {"INTRO": "연민을 보여주는 것은 언제나 좋은 생각입니다. 그것은 자기 가치 감을 향상시키고, 긍정적 인 감정을 키우며, 지연을 극복하는 데 도움을 줄 수 있습니다. 조금 연습 해보십시오.", "L1": "조금 앉아서 스트레칭을하고 싶다면, 조금이라도 진정하십시오.", "L2": "발생하는 생각과 느낌을 경청하십시오.", "L3": "친구에게 응답 할 수있는 방식으로 자신에게 응답하고 있습니까?", "L4": "대답이 '아니오'인 경우 친구의 상황을 상상해보십시오. 당신이 그들에게 뭐라고 말하겠습니까? 당신이 그들을 위해 무엇을 할 것입니까?", "OUTRO": "더 많은 연습 <a target=\"_blank\" href=\"https://drsoph.com/blog/2018/9/17/3-exercises-in-self-compassion\" target=\"_blank\">은</a> 또는 <a target=\"_blank\" href=\"https://www.google.com/search?q=self+compassion+exercises&oq=self+compassion+excers&aqs=chrome.1.69i57j0l5.4303j0j7&sourceid=chrome&ie=UTF-8\" target=\"_blank\">google</a>에 있습니다.", "TITLE": "자기 동정"}, "CUR": {"INTRO": "지연은 흥미 롭, 안 그래? 그것을하는 것이 이치에 맞지 않습니다. 장기적으로는 관심이 없습니다. 그러나 여전히 모든 사람들이 그것을합니다. 즐기고 탐험 해보세요!", "L1": "어떤 감정이 당신의 유혹을 이끌어 내고 있습니까?", "L2": "당신의 몸에서 그들을 어디에서 느낄 수 있습니까?", "L3": "그들이 당신에게 무엇을 생각 나게 하죠?", "L4": "당신이 그것을 관찰 할 때 미루는 생각은 어떻게됩니까? 그것이 강화됩니까? 소산? 다른 감정이 발생합니까?", "L5": "당신이 그들에 대한 인식을 계속해서 쉬면서 몸의 감각은 어떻게 움직입니까?", "PROCRASTINATION_TRIGGERS_TEXT": "또 다른 매우 효과적인 방법은 미루고 싶은 충동을 유발하는 요인을 기록하는 것입니다. 예를 들어 저는 개인적으로 브라우저 창이 뜨면 Reddit이나 즐겨 찾는 뉴스 사이트로 빠르게 이동하고 싶은 충동을 자주 느낍니다. 간단한 빈 텍스트 문서에 충동을 유발하는 요인을 적기 시작한 후, 이 패턴이 얼마나 뿌리 깊은지 알게 되었고 다양한 대응책을 실험해보는 데 도움이 되었습니다.", "PROCRASTINATION_TRIGGERS_TITLE": "미루게 만드는 트리거를 적어보세요.", "TITLE": "호기심"}, "H1": "여유를 가져요!", "INTRO": {"AVOIDING": "작업 피하기", "FEAR": "실패에 대한 두려움", "STRESSED": "일을 완수하지 못해서 스트레스", "TITLE": "소개"}, "P1": "우선 긴장을 풀어 라! 모두가 한 번씩 그렇게합니다. 그리고 네가해야 할 일을하지 않는다면, 적어도 그것을 즐겨라! 그런 다음 아래 섹션에서 유용한 정보를 확인하십시오.", "P2": "기억하십시오 : 지연은 시간 관리 문제가 아닌 감정 조절 문제입니다.", "REFRAME": {"INTRO": "그 임무에도 불구하고 무엇이 긍정적인지를 생각해보십시오.", "TITLE": "재구성", "TL1": "그것에 대해 흥미로운 것은 무엇입니까?", "TL2": "당신이 그것을 완성한다면 무엇을 얻을 것입니까?", "TL3": "당신이 그것을 완성한다면 어떻게 느낄 것인가?"}, "SPLIT_UP": {"INTRO": "가능한 한 많은 작은 덩어리로 작업을 분할하십시오.", "OUTRO": "끝난? 그런 다음 그것에 대해 생각해보십시오. - 엄격하게 이론적 인 것은 무엇입니까? - 당신이 작업에 착수하기 시작한 경우</i> 당신이 <i>할 첫 번째 일은 무엇입니까? 생각 해봐...", "TITLE": "그것을 나눠 라!"}}, "PROJECT": {"D_CREATE": {"CREATE": "프로젝트 만들기", "EDIT": "프로젝트 수정", "SETUP_CALDAV": "Caldav 통합 설정", "SETUP_GIT": "GitHub 통합 설치", "SETUP_GITEA_PROJECT": "Gitea 통합 설정", "SETUP_GITLAB": "GitLab 통합 설치", "SETUP_JIRA": "Jira 통합 설정", "SETUP_OPEN_PROJECT": "OpenProject 통합 설정", "SETUP_REDMINE_PROJECT": "레드마인 통합 설정"}, "D_DELETE": {"MSG": "프로젝트 \"{{title}}\"을 삭제하시겠습니까?"}, "FORM_BASIC": {"L_ENABLE_BACKLOG": "프로젝트 백로그 활성화", "L_IS_HIDDEN_FROM_MENU": "메뉴에서 프로젝트 숨기기", "L_TITLE": "프로젝트 이름", "TITLE": "기본 설정"}, "FORM_THEME": {"D_IS_DARK_THEME": "시스템이 글로벌 암 모드를 지원하면 사용되지 않습니다.", "HELP": "프로젝트의 테마 설정.", "L_BACKGROUND_IMAGE_DARK": "배경 이미지 URL (어두운 테마)", "L_BACKGROUND_IMAGE_LIGHT": "배경 이미지 URL (밝은 테마)", "L_COLOR_ACCENT": "악센트 색상", "L_COLOR_PRIMARY": "원색", "L_COLOR_WARN": "경고 / 오류 색상", "L_HUE_ACCENT": "악센트 색상 배경에 어두운 텍스트에 대한 임계 값", "L_HUE_PRIMARY": "기본 색상 배경에서 어두운 텍스트에 대한 임계 값", "L_HUE_WARN": "경고 색상 배경에 어두운 텍스트에 대한 임계 값", "L_IS_AUTO_CONTRAST": "최고의 가독성을위한 자동 텍스트 색상 설정", "L_IS_DISABLE_BACKGROUND_GRADIENT": "컬러 배경 그라디언트 비활성화", "L_IS_REDUCED_THEME": "축소 된 UI 사용 (작업 주위의 상자 없음)", "L_THEME_COLOR": "테마 색", "L_TITLE": "제목", "TITLE": "테마"}, "S": {"ARCHIVED": "보관 된 프로젝트", "CREATED": "생성 된 프로젝트 <strong>{{title}}</strong>. 왼쪽 상단의 메뉴에서 선택할 수 있습니다.", "DELETED": "삭제 된 프로젝트", "E_EXISTS": "프로젝트 \"{{title}}\"이 (가) 이미 있습니다.", "E_INVALID_FILE": "프로젝트 파일의 데이터가 잘못되었습니다.", "ISSUE_PROVIDER_UPDATED": " <strong>{{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}</strong>에 대한 업데이트 된 프로젝트 설정", "UNARCHIVED": "아카이브되지 않은 프로젝트", "UPDATED": "프로젝트 설정 업데이트"}}, "QUICK_HISTORY": {"NO_DATA": "현재 연도에 대한 데이터가 없습니다.", "PAGE_TITLE": "빠른 기록", "WEEK_TITLE": " {{nr}} 주({{timeSpent}})"}, "REDMINE": {"DIALOG_INITIAL": {"TITLE": "프로젝트용 레드마인 설정"}, "FORM": {"API_KEY": "API 액세스 키", "HOST": "호스트(예: https://redmine.org)", "PROJECT_ID": "프로젝트 식별자", "PROJECT_ID_DESCRIPTION": "브라우저에서 프로젝트를 볼 때, URL의 일부로 찾을 수 있습니다.", "SCOPE": "범위", "SCOPE_ALL": "모두", "SCOPE_ASSIGNED": "나에게 할당됨", "SCOPE_CREATED": "내가 만든"}, "FORM_SECTION": {"HELP": "<p>여기에서 일일 계획 보기의 작업 만들기 패널에서 특정 프로젝트에 대해 열려 있는 Redmine(온라인 버전 또는 자체 호스팅 인스턴스) 이슈를 나열하도록 SuperProductivity를 구성할 수 있습니다. 이슈는 제안 사항으로 나열되며 이슈에 대한 링크와 자세한 정보를 제공합니다.</p><p>또한 열려 있는 모든 이슈를 자동으로 가져올 수 있습니다.</p>", "TITLE": "레드마인"}, "ISSUE_CONTENT": {"AUTHOR": "작성자", "DESCRIPTION": "설명", "MARK_AS_CHECKED": "업데이트를 체크된 것으로 표시", "PRIORITY": "우선순위", "STATUS": "상태"}, "S": {"ERR_UNKNOWN": "Redmine: 알 수 없는 오류 {{statusCode}} {{errorMsg}}."}}, "REMINDER": {"COUNTDOWN_BANNER": {"HIDE": "숨기기", "START_NOW": "지금 시작하기", "TXT": "<strong>{{title}}</strong> 가 <strong>{{start}}</strong>에서 시작합니다!", "TXT_MULTIPLE": "<strong>{{title}}</strong> 는 <strong>에서 시작 {{start}}</strong>!<br> (및 {{nrOfOtherBanners}} 개의 다른 작업 마감일)"}, "S_ACTIVE_TASK_DUE": "현재 작업 중인 작업의 마감일이 되었습니다!<br/> ({{title}})", "S_REMINDER_ERR": "알림 인터페이스 오류"}, "SCHEDULE": {"CONTINUED": "계속되는", "D_INITIAL": {"TEXT": "<p>타임 라인의 개념은 시간이 지남에 따라 계획된 작업이 어떻게 진행되는지에 대한 더 나은 그림을 제공하는 것입니다. 작업에서 자동으로 생성되며 두 가지 다른 항목을 구분합니다. <strong>예정된 작업</strong>은 계획된 시간에 표시되고 <strong>일반 작업</strong> 은 이러한 고정 이벤트를 중심으로 진행됩니다. 모든 작업은 할당 한 예상 시간을 고려합니다.</p><p>이 외에도 작업 시작 및 종료 시간을 제공 할 수 있습니다. 구성된 경우 일반 작업은 이러한 외부에 표시되지 않습니다. 타임 라인에는 향후 30 일만 포함됩니다.</p>", "TITLE": "타임 라인"}, "END": "작업 종료", "LUNCH_BREAK": "점심 휴식", "NO_TASKS": "현재 작업이 없습니다. 상단 표시 줄의 + 버튼을 통해 몇 가지 작업을 추가하세요.", "NOW": "지금", "PLAN_END_DAY": "하루 일과 종료 시 계획", "PLAN_START_DAY": "하루 시작 시 계획 세우기", "START": "작업 시작", "TASK_PROJECTION_INFO": "예약된 반복 작업의 미래 예측"}, "SEARCH_BAR": {"INFO": "보관된 작업을 검색하려면 목록 아이콘을 클릭하십시오.", "INFO_ARCHIVED": "일반 작업을 검색하려면 아카이브 아이콘을 클릭하십시오.", "NO_RESULTS": "검색어와 일치하는 작업을 찾을 수 없습니다.", "PLACEHOLDER": "작업 또는 작업 설명 검색", "PLACEHOLDER_ARCHIVED": "보관된 작업 검색", "TOO_MANY_RESULTS": "결과가 너무 많습니다. 검색 범위를 좁히십시오."}, "SIMPLE_COUNTER": {"D_CONFIRM_REMOVE": {"MSG": "간단한 카운터를 삭제하면 추적 된 모든 과거 데이터도 삭제됩니다. 계속 하시겠습니까?", "OK": "해!"}, "D_EDIT": {"CURRENT_STREAK": "현재 행진", "DAILY_GOAL": "일일 목표", "DAYS": "일수", "L_COUNTER": "카운트"}, "FORM": {"ADD_NEW": "간단한 카운터 추가", "HELP": "여기 오른쪽 상단에 나타나는 간단한 버튼을 구성 할 수 있습니다. 타이머 또는 간단한 카운터가 될 수 있습니다. 카운터를 클릭하면 계산됩니다.", "L_COUNTDOWN_DURATION": "카운트다운 기간", "L_DAILY_GOAL": "성공적인 연승을 위한 일일 목표", "L_ICON": "아이콘", "L_ICON_ON": "전환시 아이콘", "L_IS_ENABLED": "사용", "L_TITLE": "제목", "L_TRACK_STREAKS": "트랙 스트라이크", "L_TYPE": "유형", "L_WEEKDAYS": "평일 연속 확인", "TITLE": "간단한 카운터", "TYPE_CLICK_COUNTER": "카운터 클릭", "TYPE_REPEATED_COUNTDOWN": "반복 카운트다운", "TYPE_STOPWATCH": "스톱워치"}, "S": {"GOAL_REACHED_1": "오늘의 목표를 달성했습니다!", "GOAL_REACHED_2": "현재 연속 기간입니다:"}}, "SYNC": {"A": {"ARCHIVE_ONLY_UPLOADED": "데이터가 일부만 업로드되었습니다. 나중에 다시 시도하세요! 그렇지 않으면 데이터를 다른 장치에 동기화할 수 없습니다.", "POSSIBLE_LEGACY_DATA": "슈퍼 생산성은 이제 하나의 파일이 아닌 두 개의 개별 파일을 사용하여 동기화를 개선하여 데이터 전송을 훨씬 더 적게 할 수 있습니다. 슈퍼 생산성의 모든 인스턴스를 업데이트하고 데이터가 가장 최신인 앱 인스턴스에서 먼저 데이터를 동기화하는 것이 좋습니다. 로컬 장치의 데이터인 경우에는 이 경고를 무시하고 다음 대화 상자를 확인하여 업로드를 진행하세요.", "REMOTE_MODEL_VERSION_NEWER": "원격 모델 버전이 로컬 버전보다 최신 버전입니다. 로컬 앱을 최신 버전으로 업데이트하세요!"}, "C": {"EMPTY_SYNC": "빈 데이터 개체를 동기화하려고합니다. 이것이 (거의) 처녀 앱의 첫 번째 동기화입니까?", "FORCE_UPLOAD": "그래도 로컬 데이터를 업로드 하시겠습니까?", "FORCE_UPLOAD_AFTER_ERROR": "로컬 데이터를 업로드하는 동안 오류가 발생했습니다. 강제로 업데이트 하시겠습니까?", "MIGRATE_LEGACY": "가져오는 동안 기존 데이터가 감지되었습니다. 마이그레이션을 시도하시겠습니까?", "NO_REMOTE_DATA": "원격 데이터가 없습니다. 원격에 로컬을 업로드 하시겠습니까?", "TRY_LOAD_REMOTE_AGAIN": "원격에서 데이터를 다시로드 하시겠습니까?", "UNABLE_TO_LOAD_REMOTE_DATA": "원격에서 데이터를 로드할 수 없습니다. 원격 데이터를 로컬 데이터로 덮어쓰기를 시도하시겠습니까? 이 과정에서 모든 원격 데이터가 손실됩니다."}, "D_AUTH_CODE": {"FOLLOW_LINK": "다음 링크를 열고 제공된 인증 코드를 아래 입력 필드에 복사하십시오.", "GET_AUTH_CODE": "인증 코드 받기", "L_AUTH_CODE": "인증 코드를 입력", "TITLE": "로그인 : {{provider}}"}, "D_CONFLICT": {"LAMPORT_CLOCK": "개정판", "LAST_CHANGE": "마지막 변경:", "LAST_SYNC": "마지막 동기화 :", "LOCAL": "로컬", "LOCAL_REMOTE": "로컬-> 원격", "REMOTE": "원격", "TEXT": "<p>Dropbox에서 업데이트. 로컬 및 원격 데이터가 모두 수정 된 것 같습니다.</p>", "TIMESTAMP": "타임 스탬프", "TITLE": "동기화: 데이터 충돌", "USE_LOCAL": "로컬 사용", "USE_REMOTE": "원격 사용"}, "D_DECRYPT_ERROR": {"BTN_OVER_WRITE_REMOTE": "원격 변경 및 덮어쓰기", "CHANGE_PW_AND_DECRYPT": "Change & Attempt Decrypt", "P1": "데이터가 암호화되어 암호 해독에 실패했습니다. 올바른 비밀번호를 입력하세요!", "P2": "모든 원격 데이터를 덮어쓰는 비밀번호를 변경할 수도 있습니다.", "PASSWORD": "암호"}, "D_INCOMPLETE_SYNC": {"BTN_CLOSE_APP": "앱 닫기", "BTN_DOWNLOAD_BACKUP": "로컬 백업 다운로드", "BTN_FORCE_UPLOAD": "로컬 업로드 강제", "P1": "원격 동기화 데이터가 일관성이 없습니다!", "P2": "영향을 받는 모델:", "P3": "2가지 옵션이 있습니다.", "P4": "1. 다른 장치로 이동하여 전체 동기화를 시도하십시오.", "P5": "2. 원격 데이터를 로컬 데이터로 덮어씁니다. 모든 원격 변경 사항이 손실됩니다!", "P6": "덮어쓰는 데이터의 백업을 만드는 것이 좋습니다!!", "T1": "마지막 동기화가 완료되지 않았습니다!", "T2": "마지막 동기화 중에 아카이브 데이터가 제대로 업로드되지 않았습니다:", "T3": "두 가지 옵션이 있습니다:", "T4": "1. 다른 디바이스로 이동하여 동기화를 완료합니다.", "T5": "2. 또는 원격 데이터를 로컬 데이터로 덮어쓸 수도 있습니다. 모든 원격 변경 사항은\n    손실됩니다!", "T6": "덮어쓴 데이터의 백업을 생성하는 것이 좋습니다!!!"}, "D_INITIAL_CFG": {"SAVE_AND_ENABLE": "Save & Enable Sync", "TITLE": "동기화 구성"}, "D_PERMISSION": {"DISABLE_SYNC": "동기화 비활성화", "PERM_FILE": "권한 부여", "TEXT": "<p>로컬 동기화에 대한 파일 권한이 해지되었습니다.</p>", "TITLE": "동기화: 로컬 파일 권한이 거부됨"}, "FORM": {"DROPBOX": {"L_ACCESS_TOKEN": "액세스 토큰 (인증 코드에서 생성)"}, "GOOGLE": {"L_SYNC_FILE_NAME": "파일 이름 동기화"}, "L_ENABLE_COMPRESSION": "압축 사용(데이터 전송 속도 향상)", "L_ENABLE_ENCRYPTION": "종단 간 암호화 사용(실험 중) - 동기화 공급자가 데이터에 액세스할 수 없도록 설정합니다.", "L_ENABLE_SYNCING": "동기화 활성화", "L_ENCRYPTION_NOTES": "중요 참고 사항: 다음 동기화</strong> 전에 다른 장치 <strong>에 동일한 비밀번호를 설정해야 모든 것이 작동합니다. 안전한 비밀번호를 선택하세요. 또한 이 비밀번호를 잊어버리면 <strong>데이터에 액세스할 수 없다는 점에 유의하세요. 복구할 수 없습니다</strong>본인만 키를 가지고 있으므로 복구가 불가능합니다. 암호화는 대부분의 공격자를 막을 수 있을 만큼 충분하지만 <strong>보장할 수 없습니다.</strong>", "L_ENCRYPTION_PASSWORD": "암호화 비밀번호(잊지 마세요)", "L_SYNC_INTERVAL": "동기화 간격", "L_SYNC_PROVIDER": "동기화 제공자", "LOCAL_FILE": {"L_SYNC_FILE_PATH_PERMISSION_VALIDATION": "파일 액세스 권한 필요", "L_SYNC_FOLDER_PATH": "폴더 경로 동기화"}, "TITLE": "동기화", "WEB_DAV": {"CORS_INFO": "<strong>실험적 !!</strong> 이 작업을 수행하려면 Nextcloud 인스턴스에 대한 CORS를 비활성화하거나 제한해야합니다. 이는 보안에 부정적인 영향을 미칠 수 있습니다. 자세한 내용은 <a href='https://github.com/nextcloud/server/issues/3131'>이 스레드</a> 를 참조하십시오. 자신의 책임하에 사용하십시오!", "L_BASE_URL": "기본 URL", "L_PASSWORD": "암호", "L_SYNC_FOLDER_PATH": "폴더 경로 동기화", "L_USER_NAME": "사용자 이름"}}, "S": {"ALREADY_IN_SYNC": "이미 동기화 중", "ALREADY_IN_SYNC_NO_LOCAL_CHANGES": "로컬 변경 사항 없음 - 이미 동기화 중", "BTN_CONFIGURE": "구성", "BTN_FORCE_OVERWRITE": "강제 덮어쓰기", "ERROR_DATA_IS_CURRENTLY_WRITTEN": "원격 데이터가 현재 작성 중입니다.", "ERROR_FALLBACK_TO_BACKUP": "데이터를 가져 오는 중에 문제가 발생했습니다. 로컬 백업으로 되돌아갑니다.", "ERROR_INVALID_DATA": "동기화하는 동안 오류가 발생했습니다. 유효하지 않은 데이터", "ERROR_NO_REV": "원격 파일에 유효한 수정본이 없습니다.", "ERROR_UNABLE_TO_READ_REMOTE_DATA": "동기화하는 동안 오류가 발생했습니다. 원격 데이터를 읽을 수 없습니다. 암호화를 사용 설정했는데 로컬 비밀번호가 원격 데이터를 암호화하는 데 사용된 비밀번호와 일치하지 않나요?", "IMPORTING": "데이터 가져 오기", "INCOMPLETE_CFG": "동기화 인증에 실패했습니다. 구성을 확인하십시오!", "INITIAL_SYNC_ERROR": "초기 동기화 실패", "SUCCESS_DOWNLOAD": "원격에서 동기화된 데이터", "SUCCESS_IMPORT": "가져온 데이터", "SUCCESS_VIA_BUTTON": "데이터가 성공적으로 동기화되었습니다.", "UNKNOWN_ERROR": "동기화하는 동안 알 수없는 오류가 발생했습니다. 콘솔을 확인하십시오.", "UPLOAD_ERROR": "알 수없는 업로드 오류 (설정이 맞습니까?) : {{err}}"}}, "TAG": {"D_CREATE": {"CREATE": "태그 생성", "EDIT": "태그 편집"}, "D_DELETE": {"CONFIRM_MSG": "태그 \"{{tagName}}\"을 (를) 정말로 삭제 하시겠습니까? 모든 작업에서 제거됩니다. 이 취소 할 수 없습니다."}, "D_EDIT": {"ADD": "\"{{title}}\"에 대한 태그 추가", "EDIT": "\"{{title}}\"에 대한 태그 편집", "LABEL": "태그"}, "FORM_BASIC": {"L_COLOR": "색상 (정의되지 않은 기본 테마 색상이 사용 된 경우)", "L_ICON": "아이콘", "L_TITLE": "태그 이름", "TITLE": "기본 설정"}, "S": {"UPDATED": "태그 설정이 업데이트되었습니다"}, "TTL": {"ADD_NEW_TAG": "새 태그 추가"}}, "TASK": {"ADD_TASK_BAR": {"ADD_EXISTING_TASK": "기존 작업 \"{{taskTitle}}\"추가", "ADD_ISSUE_TASK": " {{issueType}}의{{issueNr}} 호 추가", "ADD_TASK_TO_BOTTOM_OF_BACKLOG": "백로그 하단에 작업 추가", "ADD_TASK_TO_BOTTOM_OF_TODAY": "목록 맨 아래에 작업 추가", "ADD_TASK_TO_TOP_OF_BACKLOG": "백로그 맨 위에 작업 추가", "ADD_TASK_TO_TOP_OF_TODAY": "목록 맨 위에 작업 추가", "CREATE_TASK": "새 할 일 만들기", "EXAMPLE": "예 : \"일부 작업 제목 + projectName #some-tag #some-other-tag 10m / 3h\"", "START": "시작하려면 한 번 더 입력하십시오.", "TOGGLE_ADD_TO_BACKLOG_TODAY": "백로그/오늘의 목록에 작업 추가 토글하기'", "TOGGLE_ADD_TOP_OR_BOTTOM": "목록 상단 및 하단에 작업 추가 토글"}, "ADDITIONAL_INFO": {"ADD_ATTACHMENT": "첨부 파일 추가", "ADD_SUB_TASK": "하위 작업 추가", "ATTACHMENTS": "첨부 파일 {{nr}}", "DUE": "예정된 날짜", "FROM_PARENT": "(부모님)", "LOCAL_ATTACHMENTS": "로컬 첨부 파일", "NOTES": "노트", "PARENT": "부모의", "REMINDER": "조언", "REPEAT": "반복", "SCHEDULE_TASK": "작업 예약", "SUB_TASKS": "하위 작업 ({{nr}})", "TIME": "시각", "TITLE_PLACEHOLDER": "제목 입력"}, "B": {"ADD_HALF_HOUR": "1/2hou 추가", "ESTIMATE_EXCEEDED": "\"{{title}}\"에 대한 예상 시간을 초과했습니다."}, "CMP": {"ADD_SUB_TASK": "하위 작업 추가", "ADD_TO_MY_DAY": "내 날에 추가", "ADD_TO_PROJECT": "프로젝트에 추가", "CONVERT_TO_PARENT_TASK": "상위 작업으로 변환 작업", "DELETE": "작업 삭제", "DELETE_REPEAT_INSTANCE": "반복되는 작업 인스턴스 삭제", "DROP_ATTACHMENT": "'{{title}}'에 연결하려면 여기로 드래그하십시오.", "EDIT_SCHEDULED": "알림 수정", "EDIT_TAGS": "태그 편집", "EDIT_TASK_TITLE": "제목 수정", "FOCUS_SESSION": "포커스 세션 시작", "MARK_DONE": "완료로 표시", "MARK_UNDONE": "실행 취소로 표시", "MOVE_TO_BACKLOG": "백 로그로 이동", "MOVE_TO_OTHER_PROJECT": "다른 프로젝트로 이동", "MOVE_TO_REGULAR": "오늘의 목록으로 이동", "MOVE_TO_TOP": "목록 맨 위로 이동", "OPEN_ATTACH": "파일 또는 링크 첨부", "OPEN_ISSUE": "새 브라우저 탭에서 문제 열기", "OPEN_TIME": "시간 예측 / 소요 시간 추가", "REMOVE_FROM_MY_DAY": "내 날에서 제거", "REPEAT_EDIT": "반복 작업 구성 편집", "SCHEDULE": "작업 예약", "SHOW_UPDATES": "업데이트 표시", "TOGGLE_ATTACHMENTS": "첨부 파일 표시 / 숨기기", "TOGGLE_DETAIL_PANEL": "추가 정보 표시 / 숨기기", "TOGGLE_DONE": "완료 / 실행 취소로 표시", "TOGGLE_SUB_TASK_VISIBILITY": "하위 작업 가시성 토글", "TOGGLE_TAGS": "토글 태그", "TRACK_TIME": "추적 시작 시간", "TRACK_TIME_STOP": "추적 시간 일시 중지", "UNSCHEDULE_TASK": "작업 일정 취소하기", "UPDATE_ISSUE_DATA": "문제 데이터 업데이트"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAG": {"MSG": "새 태그 {{tagsTxt}}을 만들시겠습니까?", "OK": "태그 만들기"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAGS": {"MSG": "새 태그 {{tagsTxt}}을 만들시겠습니까?", "OK": "태그 만들기"}, "D_REMINDER_VIEW": {"ADD_ALL_TO_TODAY": "오늘 모두 추가", "ADD_TO_TODAY": "오늘 추가", "DONE": "끝난", "DUE_TASK": "마감일", "DUE_TASKS": "마감일", "FOR_CURRENT": "작업이 완료되었습니다. 그것에 착수하고 싶니?", "FOR_OTHER": "작업이 완료되었습니다. 그것에 착수하고 싶니?", "FROM_PROJECT": "프로젝트에서", "FROM_TAG": "태그에서 : \"{{title}}\"", "RESCHEDULE_EDIT": "편집(일정 재조정)", "RESCHEDULE_UNTIL_TOMORROW": "내일까지", "SNOOZE": "다시 알림", "SNOOZE_ALL": "모두 다시 알림", "START": "작업 시작", "SWITCH_CONTEXT_START": "상황 전환 및 시작", "UNSCHEDULE": "비정기", "UNSCHEDULE_ALL": "모두 일정 해제"}, "D_SCHEDULE_TASK": {"MOVE_TO_BACKLOG": "예약 될 때까지 작업을 백 로그로 이동하십시오.", "QA_NEXT_MONTH": "다음 달 일정", "QA_NEXT_WEEK": "다음 주 일정", "QA_REMOVE_TODAY": "오늘부터 작업 제거", "QA_TODAY": "오늘 예약하기", "QA_TOMORROW": "내일 일정", "REMIND_AT": "에 알림", "RO_1H": "시작 1 시간 전", "RO_5M": "시작 5 분 전", "RO_10M": "시작하기 10 분 전", "RO_15M": "시작하기 15 분 전", "RO_30M": "시작 30 분 전", "RO_NEVER": "Never", "RO_START": "시작될 때", "SCHEDULE": "시간표", "UNSCHEDULE": "일정 잡기"}, "D_TIME": {"ADD_FOR_OTHER_DAY": "다른 날에 보낸 시간을 추가하십시오.", "DELETE_FOR": "일일 항목 삭제", "ESTIMATE": "견적", "TIME_SPENT": "지출 된 시간", "TIME_SPENT_ON": "경과 시간 {{date}}", "TITLE": "소요 시간 / 견적"}, "D_TIME_FOR_DAY": {"ADD_ENTRY_FOR": " {{date}}에 대한 새 항목 추가", "DATE": "신규 등록 날짜", "HELP": "예 :<br> 30m => 30 분<br> 2 시간 => 2 시간<br> 2 시간 30 분 => 2 시간 30 분", "TINE_SPENT": "지출 된 시간", "TITLE": "오늘 추가"}, "N": {"ESTIMATE_EXCEEDED": "예상 시간을 초과했습니다!", "ESTIMATE_EXCEEDED_BODY": "\"{{title}}\"에 대한 예상 시간을 초과했습니다."}, "S": {"CANNOT_ASSIGN_PROJECT_FOR_REPEATABLE_TASK": "반복 가능한 작업의 경우 짧은 구문을 통해 프로젝트를 할당할 수 없습니다!", "CREATED_FOR_PROJECT": "작업 \"{{taskTitle}}\"를 프로젝트 \"{{projectTitle}}\"로 이동했습니다.", "CREATED_FOR_PROJECT_ACTION": "프로젝트로 이동", "DELETED": "\"{{title}}\"작업을 삭제했습니다.", "FOUND_MOVE_FROM_BACKLOG": "기존 작업 <strong>{{title}}</strong> 을 (를) 현재 작업 목록으로 이동했습니다.", "FOUND_MOVE_FROM_OTHER_LIST": " <strong>{{contextTitle}}</strong> 의 작업 <strong>{{title}}</strong> 을 (를) 현재 목록에 추가했습니다.", "FOUND_RESTORE_FROM_ARCHIVE": "아카이브의 문제와 관련된 복원 된 작업 <strong>{{title}}</strong> ", "LAST_TAG_DELETION_WARNING": "프로젝트가 아닌 작업의 마지막 태그를 제거하려고합니다. 이것은 허용되지 않습니다!", "MOVED_TO_ARCHIVE": " {{nr}} 작업을 보관으로 이동했습니다.", "MOVED_TO_PROJECT": "작업 \"{{taskTitle}}\"를 프로젝트 \"{{projectTitle}}\"로 이동했습니다.", "MOVED_TO_PROJECT_ACTION": "프로젝트로 이동", "REMINDER_ADDED": "예약 된 작업 \"{{title}}\"", "REMINDER_DELETED": "할 일 목록 삭제", "REMINDER_UPDATED": "\"{{title}}\"작업에 대한 알림이 업데이트되었습니다.", "TASK_CREATED": "\"{{title}}\"작업 생성"}, "SELECT_OR_CREATE": "작업 선택 또는 생성", "SUMMARY_TABLE": {"ESTIMATE": "견적", "SPENT_TODAY": "오늘 보낸", "SPENT_TOTAL": "사용한 총액", "TASK": "태스크", "TOGGLE_DONE": "완료로 표시 취소 / 표시"}}, "TASK_REPEAT": {"ADD_INFO_PANEL": {"CUSTOM": "사용자 지정 반복 구성", "CUSTOM_AND_TIME": "사용자 지정, {{timeStr}}", "CUSTOM_WEEKLY": "{{daysStr}}", "CUSTOM_WEEKLY_AND_TIME": "{{daysStr}}, {{timeStr}}", "DAILY": "매일", "DAILY_AND_TIME": "매일 {{timeStr}}", "EVERY_X_DAILY": " {{x}} 일마다", "EVERY_X_DAILY_AND_TIME": " {{x}} 일마다 {{timeStr}}", "EVERY_X_MONTHLY": "매월 {{x}} 개월", "EVERY_X_MONTHLY_AND_TIME": " {{x}} 개월마다 {{timeStr}}", "EVERY_X_YEARLY": " {{x}} 년마다", "EVERY_X_YEARLY_AND_TIME": " {{x}} 년마다 {{timeStr}}", "MONDAY_TO_FRIDAY": "월~금", "MONDAY_TO_FRIDAY_AND_TIME": "월~금, {{timeStr}}", "MONTHLY_CURRENT_DATE": "매월 {{dateDayStr}}일", "MONTHLY_CURRENT_DATE_AND_TIME": "매월 {{dateDayStr}}일, {{timeStr}}일", "WEEKLY_CURRENT_WEEKDAY": "주간 {{weekdayStr}}", "WEEKLY_CURRENT_WEEKDAY_AND_TIME": "주간 {{weekdayStr}}, {{timeStr}}", "YEARLY_CURRENT_DATE": "연간 {{dayAndMonthStr}}", "YEARLY_CURRENT_DATE_AND_TIME": "연간 {{dayAndMonthStr}}, {{timeStr}}"}, "D_CONFIRM_MOVE_TO_PROJECT": {"MSG": "이 반복 가능한 작업에 대해 {{tasksNr}} 개의 인스턴스가 만들어졌습니다. 이 인스턴스를 모두 \"{{projectName}}\" 프로젝트로 이동하시겠습니까? \"?", "OK": "모든 인스턴스 업데이트"}, "D_CONFIRM_REMOVE": {"MSG": "반복 구성을 제거하면이 작업의 모든 이전 인스턴스가 일반 작업으로 변환됩니다. 계속 하시겠습니까?", "OK": "완전히 제거하십시오."}, "D_CONFIRM_UPDATE_INSTANCES": {"CANCEL": "향후 작업만", "MSG": "이 반복 가능한 작업에 대해 {{tasksNr}} 개의 인스턴스가 만들어졌습니다. 모든 인스턴스를 새 기본값으로 업데이트하시겠습니까, 아니면 향후 작업만 업데이트하시겠습니까?", "OK": "모든 인스턴스 업데이트"}, "D_EDIT": {"ADD": "반복 작업 구성 추가", "ADVANCED_CFG": "고급 구성", "EDIT": "반복 작업 구성 편집", "HELP1": "반복 작업은 일별 잡일을 의미합니다 (예 : \"조직\", \"일일 모임\", \"코드 검토\", \"이메일 확인\"또는 여러 번 반복 될 수있는 유사한 작업).", "HELP2": "일단 구성되면 반복 작업은 프로젝트를 여는 즉시 아래에서 선택한 매일에 다시 작성되며 하루가 끝날 때 자동 완성으로 표시됩니다. 그들은 다른 인스턴스로 처리됩니다. 따라서 하위 작업 등을 자유롭게 추가 할 수 있습니다.", "HELP3": "Jira 또는 Git Issues에서 가져온 작업은 반복 할 수 없습니다. 모든 미리 알림은 반복 작업에서 삭제됩니다.", "HELP4": "주문 필드에 대한 참고 사항: 생성 주문 반복 가능한 작업 참조. 동시에 생성되는 반복 가능한 작업에만 적용됩니다. 값이 낮을수록 작업이 목록의 위쪽에 있고 숫자가 낮을수록 아래쪽에 있음을 의미합니다. 0보다 큰 값은 항목이 일반 작업의 맨 아래에 생성됨을 의미합니다.", "TAG_LABEL": "추가 할 태그"}, "F": {"C_DAY": "일", "C_MONTH": "월", "C_WEEK": "주", "C_YEAR": "연도", "DEFAULT_ESTIMATE": "기본 견적", "FRIDAY": "금요일", "IS_ADD_TO_BOTTOM": "작업을 목록의 맨 아래로 이동", "MONDAY": "월요일", "NOTES": "기본 노트", "ORDER": "주문", "ORDER_DESCRIPTION": "반복 가능한 작업의 생성 순서. 동시에 생성된 반복 가능한 작업에만 영향을 줍니다. 값이 낮을수록 작업이 목록 위쪽에 생성되고 숫자가 낮을수록 아래쪽에 생성됨을 의미합니다. 0보다 큰 값은 항목이 일반 작업의 맨 아래에 생성됨을 의미합니다.", "Q_CUSTOM": "사용자 정의 반복 구성", "Q_DAILY": "매일", "Q_MONDAY_TO_FRIDAY": "매주 월요일부터 금요일까지", "Q_MONTHLY_CURRENT_DATE": "매월 {{dateDayStr}}일", "Q_WEEKLY_CURRENT_WEEKDAY": "매주 {{weekdayStr}}에", "Q_YEARLY_CURRENT_DATE": "매년 {{dayAndMonthStr}}에", "QUICK_SETTING": "구성 반복", "REMIND_AT": "에 알림", "REMIND_AT_PLACEHOLDER": "알림 시간 선택", "REPEAT_CYCLE": "반복 주기", "REPEAT_EVERY": "모든", "SATURDAY": "토요일", "START_DATE": "시작 날짜", "START_TIME": "예정된 시작 시간", "START_TIME_DESCRIPTION": "예를 들어 15:00. 하루 종일 작업을 위해 비워 둡니다.", "SUNDAY": "일요일", "THURSDAY": "목요일", "TITLE": "작업 제목", "TUESDAY": "화요일", "WEDNESDAY": "수요일"}}, "TASK_VIEW": {"CUSTOMIZER": {"ENTER_PROJECT": "프로젝트 입력", "ENTER_TAG": "태그 입력", "ESTIMATED_TIME": "예상 시간", "FILTER_BY": "필터링 기준", "FILTER_DEFAULT": "필터 없음", "FILTER_ESTIMATED_TIME": "예상 시간", "FILTER_PROJECT": "프로젝트", "FILTER_SCHEDULED_DATE": "예정일", "FILTER_TAG": "태그", "FILTER_TIME_SPENT": "소요 시간", "GROUP_BY": "그룹화 기준", "GROUP_DEFAULT": "그룹 없음", "GROUP_PROJECT": "프로젝트", "GROUP_SCHEDULED_DATE": "예정일", "GROUP_TAG": "태그", "RESET_ALL": "모두 재설정", "SCHEDULED_DEFAULT": "모든 날짜", "SCHEDULED_NEXT_MONTH": "다음 달", "SCHEDULED_NEXT_WEEK": "다음 주", "SCHEDULED_THIS_MONTH": "이달", "SCHEDULED_THIS_WEEK": "이번 주", "SCHEDULED_TODAY": "오늘", "SCHEDULED_TOMORROW": "내일", "SORT_BY": "정렬 기준", "SORT_CREATION_DATE": "생성 날짜", "SORT_DEFAULT": "기본값", "SORT_NAME": "이름", "SORT_SCHEDULED_DATE": "예정일", "TIME_1HOUR": "> 1시간", "TIME_2HOUR": "> 2 시간", "TIME_10MIN": "> 10분", "TIME_30MIN": "> 30분", "TIME_DEFAULT": "모든 기간", "TIME_SPENT": "소요 시간", "TITLE": "작업 보기 사용자 지정"}}, "TIME_TRACKING": {"B": {"ALREADY_DID": "벌써 했어.", "SNOOZE": "다시 알림 {{time}}"}, "B_TTR": {"ADD_TO_TASK": "작업에 추가", "MSG": " {{time}}에 대한 시간을 추적하지 않았습니다."}, "D_IDLE": {"ADD_ENTRY": "추적할 항목 추가", "BREAK": "단절", "CREATE_AND_TRACK": "<em></em> 만들기 및 추적 :", "IDLE_FOR": "당신은 유휴 상태였습니다.", "RESET_BREAK_REMINDER_TIMER": "휴식 알림 타이머 재설정", "SIMPLE_CONFIRM_COUNTER_CANCEL": "버킷", "SIMPLE_CONFIRM_COUNTER_OK": "선로", "SIMPLE_COUNTER_CONFIRM_TXT": "건너뛰기를 선택했지만 간단한 카운터 버튼 {{nr}} 개를 활성화했습니다. 유휴 시간을 추적하시겠습니까?", "SIMPLE_COUNTER_TOOLTIP": " {{title}}까지 추적하려면 클릭하세요.", "SIMPLE_COUNTER_TOOLTIP_DISABLE": " {{title}}을(를) 추적하지 않으려면 클릭하십시오.", "SKIP": "버킷", "SPLIT_TIME": "여러 작업과 휴식 시간으로 시간 나누기", "TASK": "태스크", "TRACK_TO": "추적 대상 :"}, "D_TRACKING_REMINDER": {"CREATE_AND_TRACK": "<em></em> 생성 및 추적", "IDLE_FOR": "당신은 유휴 상태였습니다.", "NOTIFICATION_TITLE": "시간을 추적하세요!", "TASK": "태스크", "TRACK_TO": "추적 대상 :", "UNTRACKED_TIME": "추적되지 않은 시간 :"}}, "WORKLOG": {"CMP": {"DAYS_WORKED": "일한 날짜 :", "MONTH_WORKED": "월 근무일 :", "REPEATING_TASK": "반복 과제", "RESTORE_TASK_FROM_ARCHIVE": "아카이브에서 작업 복원", "TASKS": "할 일 목록", "TOTAL_TIME": "총 소요 시간 :", "WEEK_NR": "주 {{nr}}", "WORKED": "근무한"}, "D_CONFIRM_RESTORE": " <strong>\"{{title}}\"</strong> 작업을 현재 작업 목록으로 이동 하시겠습니까?", "D_EXPORT_TITLE": "작업 로그 내보내기 {{start}}-{{end}}", "D_EXPORT_TITLE_SINGLE": "작업 로그 내보내기 {{day}}", "EXPORT": {"ADD_COL": "열 추가", "COPY_TO_CLIPBOARD": "클립 보드에 복사", "DONT_ROUND": "돌지 마라.", "EDIT_COL": "열 편집", "GROUP_BY": "그룹화 기준", "O": {"DATE": "날짜", "ENDED_WORKING": "종료 된 작업", "ESTIMATE_AS_CLOCK": "예상 시간 (예 : 5:23)", "ESTIMATE_AS_MILLISECONDS": "밀리 초로 예상", "ESTIMATE_AS_STRING": "문자열로 예상 (예 : 5 시간 23 분)", "FULL_HALF_HOURS": "반 시간 반", "FULL_HOURS": "1 시간 내내", "FULL_QUARTERS": "전체 분기", "NOTES": "작업 설명", "PARENT_TASK": "학부모 과제", "PARENT_TASK_TITLES_ONLY": "상위 작업 제목 만", "PROJECTS": "프로젝트 이름", "STARTED_WORKING": "작업 시작", "TAGS": "태그", "TASK_SUBTASK": "작업 / 하위 작업", "TIME_AS_CLOCK": "시계와 시간 (예 : 5:23)", "TIME_AS_MILLISECONDS": "시간 (밀리 초)", "TIME_AS_STRING": "문자열을 나타내는 시간 (예 : 5h 23m)", "TITLES_AND_SUB_TASK_TITLES": "제목 및 하위 작업 제목", "WORKLOG": "작업 로그"}, "OPTIONS": "옵션", "ROUND_END_TIME_TO": "라운드 종료 시간 :", "ROUND_START_TIME_TO": "라운드 시작 시간 :", "ROUND_TIME_WORKED_TO": "왕복 근무 시간", "SAVE_TO_FILE": "파일에 저장", "SEPARATE_TASKS_BY": "작업 분리", "SHOW_AS_TEXT": "텍스트로 표시"}, "WEEK": {"EXPORT": "주 데이터 내보내기", "NO_DATA": "이번 주에는 아직 할 일이 없습니다.", "TITLE": "제목"}}}, "FILE_IMEX": {"DIALOG_CONFIRM_URL_IMPORT": {"INITIATED_MSG": "자동 데이터 가져오기가 시작되었습니다.", "SOURCE_URL_DOMAIN": "소스 도메인", "TITLE": "URL에서 데이터 가져오기 확인", "WARNING_MSG": "계속하면 지정된 URL의 내용으로 현재 애플리케이션 데이터 및 구성이 덮어씌워집니다. 이 작업은 취소할 수 없습니다.", "WARNING_TITLE": "경고"}, "EXPORT_DATA": "데이터 내보내기", "IMPORT_FROM_FILE": "파일에서 가져 오기", "IMPORT_FROM_URL": "URL에서 가져오기", "IMPORT_FROM_URL_DIALOG_DESCRIPTION": "가져오려는 Super Productivity 백업 JSON 파일의 전체 URL을 입력하세요.", "IMPORT_FROM_URL_DIALOG_TITLE": "URL에서 가져오기", "OPEN_IMPORT_FROM_URL_DIALOG": "URL에서 가져오기", "PRIVACY_EXPORT": "익명화된 데이터 내보내기(디버깅을 위해 <EMAIL> 으로 보내기)", "S_BACKUP_DOWNLOADED": "백업은 안드로이드 문서 폴더에 다운로드되었습니다.", "S_ERR_IMPORT_FAILED": "데이터 가져오기 실패", "S_ERR_INVALID_DATA": "가져 오기 실패 : 잘못된 JSON", "S_ERR_INVALID_URL": "가져오기 실패: 제공된 URL이 유효하지 않습니다", "S_ERR_NETWORK": "가져오기 실패: URL에서 데이터를 가져오는 중 네트워크 오류 발생", "S_IMPORT_FROM_URL_ERR_DECODE": "오류: 가져오기를 위한 URL 매개변수를 디코딩할 수 없습니다. 올바르게 형식화되었는지 확인하십시오.", "URL_PLACEHOLDER": "가져올 URL을 입력하세요"}, "G": {"ADD": "추가", "ADVANCED_CFG": "고급 구성", "CANCEL": "취소", "CLOSE": "닫기", "CONFIRM": "확인하다", "DELETE": "지우다", "DISMISS": "버리다", "DO_IT": "해!", "DURATION_DESCRIPTION": "예: \"5시간 23분\"은 5시간 23분을 의미합니다.", "EDIT": "편집하다", "ENABLED": "사용", "EXAMPLE_VAL": "예. 32분", "EXTENSION_INFO": "<PERSON>ra Api 및 Idle Time Handling과의 통신을 허용하려면 <a target=\"_blank\" href=\"https://chrome.google.com/webstore/detail/super-productivity/ljkbjodfmekklcoibdnhahlaalhihmlb\"> 크롬 확장</a> 을 다운로드하십시오. 이 기능은 모바일에서 작동하지 않습니다.", "HIDE": "숨기기", "ICON_INP_DESCRIPTION": "모든 utf-8 이모티콘도 지원됩니다!", "INBOX_PROJECT_TITLE": "받은 편지함", "LOGIN": "로그인", "LOGOUT": "로그 아웃", "MINUTES": "{{m}} 분", "MOVE_BACKWARD": "뒤로 이동", "MOVE_FORWARD": "앞으로 나아가기", "NEXT": "다음", "NO_CON": "현재 오프라인 상태입니다. 인터넷에 다시 연결하십시오.", "NONE": "없음", "OK": "승인", "OVERDUE": "기한이 지난", "PREVIOUS": "이전", "REMOVE": "삭제", "RESET": "초기화", "SAVE": "저장", "SUBMIT": "제출하기", "TITLE": "제목", "TODAY_TAG_TITLE": "오늘", "TRACKING_INTERVAL_DESCRIPTION": "이 간격을 밀리초 단위로 사용하여 시간을 추적합니다. 디스크 쓰기를 줄이려면 이 값을 변경할 수 있습니다. 이슈 #2355를 참조하세요.", "UNDO": "취소", "UPDATE": "최신 정보", "WITHOUT_PROJECT": "프로젝트없이", "YESTERDAY": "어제"}, "GCF": {"AUTO_BACKUPS": {"HELP": "뭔가 잘못되었을 때를 대비하여 모든 데이터를 앱 폴더에 자동 저장하십시오.", "LABEL_IS_ENABLED": "자동 백업 사용", "LOCATION_INFO": "백업은 다음 위치에 저장됩니다.", "TITLE": "자동 백업"}, "CALENDARS": {"BROWSER_WARNING": "교차 출처 제한 <b>으로 인해 브라우저 버전의 Super Productivity에서는 작동하지 않을 수 있습니다.<br /> 이 기능을 사용하려면 데스크톱 버전</a> 을 <a href=\"https://super-productivity.com/download/\">다운로드하세요!</b>", "CAL_PATH": "ICal 소스의 URL", "CAL_PROVIDERS": "캘린더 공급자(실험적 및 선택 사항)", "CHECK_UPDATES": "X마다 원격 업데이트 확인", "DEFAULT_PROJECT": "추가된 캘린더 작업의 기본 프로젝트", "HELP": "캘린더를 통합하여 알림을 받고 Super Productivity 내에서 작업으로 추가할 수 있습니다. 통합은 iCal 형식을 사용하여 작동합니다. 이 기능을 사용하려면 인터넷이나 파일 시스템을 통해 캘린더에 액세스할 수 있어야 합니다.", "SHOW_BANNER_THRESHOLD": "이벤트 전에 알림 X 표시(비활성화하면 비어 있음)"}, "EVALUATION": {"IS_HIDE_EVALUATION_SHEET": "일일 요약에서 평가 시트 숨기기", "TITLE": "평가 및 측정 항목"}, "FOCUS_MODE": {"HELP": "집중 모드에서는 방해 요소가 없는 화면을 열어 현재 작업에 집중할 수 있도록 도와줍니다.", "L_ALWAYS_OPEN_FOCUS_MODE": "추적 시 항상 초점 모드 열기", "L_SKIP_PREPARATION_SCREEN": "준비 화면 건너뛰기(스트레칭 등)", "TITLE": "초점 모드"}, "IDLE": {"HELP": "<div><p>유휴 시간 처리가 활성화되면 유휴 상태 일 때 지정된 시간 후에 대화 상자가 열리고 시간을 추적하려는 작업이 있는지 여부를 확인합니다.</p></div>", "IS_ENABLE_IDLE_TIME_TRACKING": "유휴 시간 처리 사용", "IS_ONLY_OPEN_IDLE_WHEN_CURRENT_TASK": "현재 작업이 선택되면 유휴 시간 대화 상자 만 트리거합니다.", "MIN_IDLE_TIME": "X 후에 유휴 트리거", "TITLE": "유휴 처리"}, "IMEX": {"HELP": "<p>여기서 모든 데이터를 백업용 <strong>JSON</strong> 로 내보낼 수 있지만 다른 컨텍스트에서 사용할 수도 있습니다 (예 : 브라우저에서 프로젝트를 내보내고 데스크탑 버전으로 가져 오기를 원할 수 있습니다 ). </p> <p>가져 오기에서는 유효한 JSON이 텍스트 영역에 복사 될 것으로 예상합니다. <strong>참고 : 가져 오기 버튼을 누르면 모든 현재 설정과 데이터를 덮어 씁니다!</strong></p>", "TITLE": "수입 수출"}, "KEYBOARD": {"ADD_NEW_NOTE": "새 메모 추가", "ADD_NEW_TASK": "새 작업 추가", "APP_WIDE_SHORTCUTS": "글로벌 단축키 (응용 프로그램 전체)", "COLLAPSE_SUB_TASKS": "하위 작업 축소", "EXPAND_SUB_TASKS": "하위 작업 확장", "GLOBAL_ADD_NOTE": "새 메모 추가", "GLOBAL_ADD_TASK": "새 작업 추가", "GLOBAL_SHOW_HIDE": "최고 생산성 표시 / 숨기기", "GLOBAL_TOGGLE_TASK_START": "마지막 활성 작업에 대한 시간 추적 토글", "GO_TO_DAILY_AGENDA": "일정 목록으로 이동", "GO_TO_FOCUS_MODE": "초점 모드로 이동", "GO_TO_SCHEDULE": "타임 라인으로 이동", "GO_TO_SCHEDULED_VIEW": "예약 된 작업으로 이동", "GO_TO_SETTINGS": "설정으로 바로 가기", "GO_TO_WORK_VIEW": "작업보기로 이동", "HELP": "<p>여기에서 모든 키보드 단축키를 구성 할 수 있습니다.</p> <p>텍스트 입력을 클릭하고 원하는 키보드 조합을 입력하십시오. 저장하려면 Enter 키를 누르고 중단하려면 Esc 키를 누르십시오.</p> <p>바로 가기에는 세 가지 유형이 있습니다.</p> <ul> <li> <strong>전역 바로 가기 :</strong> 앱이 실행 중일 때 다른 모든 애플리케이션에서 동작을 트리거합니다. </li> <li> <strong>응용 프로그램 수준 바로 가기 :</strong> 응용 프로그램의 모든 화면에서 트리거하지만 현재 텍스트 필드를 편집하는 경우에는 트리거하지 않습니다. </li> <li> <strong>작업 수준 바로 가기 :</strong> 마우스 또는 키보드를 통해 작업을 선택하고 일반적으로 해당 작업과 관련된 작업을 트리거하는 경우에만 트리거됩니다. </li> </ul>", "MOVE_TASK_DOWN": "목록에서 작업 이동", "MOVE_TASK_TO_BOTTOM": "작업을 목록 맨 아래로 이동", "MOVE_TASK_TO_TOP": "작업을 목록 상단으로 이동", "MOVE_TASK_UP": "목록에서 작업 이동", "MOVE_TO_BACKLOG": "작업을 작업 백 로그로 이동", "MOVE_TO_REGULARS_TASKS": "오늘의 작업 목록으로 작업 이동", "OPEN_PROJECT_NOTES": "프로젝트 노트 표시 / 숨기기", "SAVE_NOTE": "노트 저장", "SELECT_NEXT_TASK": "다음 작업 선택", "SELECT_PREVIOUS_TASK": "이전 작업 선택", "SHOW_SEARCH_BAR": "검색창 표시", "SYSTEM_SHORTCUTS": "글로벌 단축키 (시스템 전체)", "TASK_ADD_ATTACHMENT": "파일 또는 링크 첨부", "TASK_ADD_SUB_TASK": "하위 작업 추가", "TASK_DELETE": "작업 삭제", "TASK_EDIT_TAGS": "태그 편집", "TASK_EDIT_TITLE": "제목 수정", "TASK_MOVE_TO_PROJECT": "프로젝트 메뉴로 이동 작업 열기", "TASK_OPEN_CONTEXT_MENU": "작업 상황에 맞는 메뉴 열기", "TASK_OPEN_ESTIMATION_DIALOG": "견적 / 소요 시간 수정", "TASK_PLAN_FORDAY": "하루 계획", "TASK_SCHEDULE": "작업 예약", "TASK_SHORTCUTS": "할 일 목록", "TASK_SHORTCUTS_INFO": "다음 바로 가기가 현재 선택된 작업 (탭 또는 마우스를 통해 선택)에 적용됩니다.", "TASK_TOGGLE_DETAIL_PANEL_OPEN": "추가 작업 정보 표시 / 숨기기", "TASK_TOGGLE_DONE": "완료 여부 토글", "TITLE": "키보드 단축키", "TOGGLE_BACKLOG": "작업 백 로그 표시 / 숨기기", "TOGGLE_BOOKMARKS": "북마크 바 표시 / 숨기기", "TOGGLE_ISSUE_PANEL": "이슈 패널 표시/숨기기", "TOGGLE_PLAY": "작업 시작 / 중지", "TOGGLE_SIDE_NAV": "사이드 내비게이션 표시 및 초점 / 숨기기", "TOGGLE_TASK_VIEW_CUSTOMIZER_PANEL": "필터/그룹/정렬 패널 전환", "TRIGGER_SYNC": "동기화 트리거(구성된 경우)", "ZOOM_DEFAULT": "확대 / 축소 기본값 (데스크톱 만 해당)", "ZOOM_IN": "확대 (데스크톱에만 해당)", "ZOOM_OUT": "축소 (데스크톱에만 해당)"}, "LANG": {"AR": "عرب<PERSON>", "CZ": "체코어", "DE": "De<PERSON>ch", "EN": "영어", "ES": "Español", "FA": "فار<PERSON>ی", "FR": "français", "HR": "Hrvatski", "ID": "인도네시아어", "IT": "이탈리아어", "JA": "日本語", "KO": "한국어", "LABEL": "언어를 선택하세요.", "NB": "Norsk Bokmål", "NL": "Nederlands", "PL": "폴란드어", "PT": "포르투갈어", "RU": "러시아어", "SK": "슬로바키아어", "TITLE": "언어", "TR": "Türkçe", "UK": "Українська", "ZH": "中文(简体)", "ZH_TW": "中文(繁體)"}, "MISC": {"DEFAULT_PROJECT": "지정되지 않은 경우 작업에 사용할 기본 프로젝트", "FIRST_DAY_OF_WEEK": "주의 첫 요일", "HELP": "<p><strong>데스크톱 알림이 표시되지 않습니까?</strong> 창의 경우 시스템 > 알림 및 작업을 확인하고 필요한 알림이 활성화되었는지 확인할 수 있습니다.</p>", "IS_AUTO_ADD_WORKED_ON_TO_TODAY": "작업에 사용할 오늘 태그를 자동으로 추가", "IS_AUTO_MARK_PARENT_AS_DONE": "모든 하위 작업이 완료되면 상위 작업을 완료로 표시합니다.", "IS_CONFIRM_BEFORE_EXIT": "앱을 종료하기 전에 확인", "IS_CONFIRM_BEFORE_EXIT_WITHOUT_FINISH_DAY": "하루를 먼저 끝내지 않고 앱을 종료하기 전에 확인", "IS_DARK_MODE": "다크 모드", "IS_DISABLE_ANIMATIONS": "모든 애니메이션 비활성화", "IS_HIDE_NAV": "기본 헤더가 올라갈 때까지 탐색을 숨 깁니다 (데스크톱 만 해당).", "IS_MINIMIZE_TO_TRAY": "트레이로 최소화 (데스크탑만 해당)", "IS_SHOW_TIP_LONGER": "앱 시작 시 생산성 팁 표시좀 더 오래", "IS_TRAY_SHOW_CURRENT_COUNTDOWN": "트레이/상태 메뉴에 현재 카운트다운 표시(데스크톱 Mac만 해당)", "IS_TRAY_SHOW_CURRENT_TASK": "트레이 / 상태 메뉴에 현재 작업 표시 (데스크톱 전용)", "IS_TURN_OFF_MARKDOWN": "노트에 대한 마크 다운 파싱 끄기", "IS_USE_MINIMAL_SIDE_NAV": "최소한의 탐색 모음 사용(아이콘만 표시)", "START_OF_NEXT_DAY": "다음 날 시작 시간", "START_OF_NEXT_DAY_HINT": "다음 날을 계산하려는 시점(시간 단위)부터 시작됩니다. 기본값은 0인 자정입니다.", "TASK_NOTES_TPL": "작업 설명 템플릿", "TITLE": "기타 설정"}, "POMODORO": {"BREAK_DURATION": "짧은 휴식 시간", "CYCLES_BEFORE_LONGER_BREAK": "X 작업 세션 후에 더 긴 휴식을 시작하십시오.", "DURATION": "작업 세션의 지속 기간", "HELP": "<p>포모도로 타이머는 몇 가지 설정을 통해 구성 할 수 있습니다. 모든 작업 세션의 지속 시간, 정상 휴식 기간, 긴 휴식 시간이 시작되기 전에 실행할 작업 세션 수 및이 긴 휴식 기간.</p> <p>당신은 당신의 pomodoro 휴식 시간 동안 산만을 표시하고 싶다면 설정할 수 있습니다.</p> <p>\"Pomodoro break on pomodoro break\"설정은 작업에 소요되는 작업 시간으로 휴식 시간을 추적합니다. </p> <p>활성 작업이 없을 때 pomodoro 세션을 일시 중지하면 작업을 일시 중지 할 때 pomodoro 세션도 일시 중지됩니다.</p>", "IS_ENABLED": "포모도로 타이머 사용", "IS_MANUAL_CONTINUE": "수동으로 다음 pomodoro 세션 시작 확인", "IS_MANUAL_CONTINUE_BREAK": "다음 휴식 시간 시작 수동 확인", "IS_PLAY_SOUND": "세션이 끝나면 소리를냅니다.", "IS_PLAY_SOUND_AFTER_BREAK": "휴식이 끝나면 소리를 내십시오.", "IS_PLAY_TICK": "매초마다 틱 소리 재생", "IS_STOP_TRACKING_ON_BREAK": "휴식 시간에 작업 중지 시간 추적", "LONGER_BREAK_DURATION": "긴 휴식 시간", "TITLE": "포모도로 타이머"}, "REMINDER": {"COUNTDOWN_DURATION": "실제 알림 앞에 배너 X 표시", "IS_COUNTDOWN_BANNER_ENABLED": "미리 알림 기한 전에 카운트다운 배너 표시하기", "TITLE": "리마인더"}, "SCHEDULE": {"HELP": "타임 라인 기능은 계획된 작업이 시간에 따라 어떻게 진행되는지에 대한 빠른 개요를 제공해야합니다. <a href='#/schedule'>'타임 라인'</a>아래의 왼쪽 메뉴에서 찾을 수 있습니다.", "L_IS_LUNCH_BREAK_ENABLED": "점심 시간 사용", "L_IS_WORK_START_END_ENABLED": "예정되지 않은 작업 흐름을 특정 작업 시간으로 제한", "L_LUNCH_BREAK_END": "점심 시간 종료", "L_LUNCH_BREAK_START": "점심 시간 시작", "L_WORK_END": "근무일 종료", "L_WORK_START": "근무일 시작", "LUNCH_BREAK_START_END_DESCRIPTION": "예: 13:00", "TITLE": "타임 라인", "WORK_START_END_DESCRIPTION": "예 : 17:00"}, "SHORT_SYNTAX": {"HELP": "<p>작업</p>을 만들 때 짧은 구문 옵션을 제어할 수 있습니다.", "IS_ENABLE_DUE": "만기 짧은 구문 사용(@<Due time>)", "IS_ENABLE_PROJECT": "프로젝트 짧은 구문 사용(+<Project name>)", "IS_ENABLE_TAG": "태그 짧은 구문 사용(#<Tag>)", "TITLE": "짧은 구문"}, "SOUND": {"BREAK_REMINDER_SOUND": "휴식 시간 알림음", "DONE_SOUND": "작업 완료 소리", "IS_INCREASE_DONE_PITCH": "모든 작업에 대한 피치 높이기", "TITLE": "소리", "TRACK_TIME_SOUND": "시간 알림 소리 추적", "VOLUME": "음량"}, "TAKE_A_BREAK": {"ADD_NEW_IMG": "동기 부여 이미지 추가", "FULL_SCREEN_BLOCKER_DURATION": "전체 화면 창을 표시하는 기간(데스크톱만 해당)", "HELP": "<div> <p>중단하지 않고 지정된 시간 동안 작업 한 경우 다시 알림을 구성 할 수 있습니다.</p> <p>표시된 메시지를 수정할 수 있습니다. ${duration} 은 휴식 시간없이 바뀝니다.</p> </div>", "IS_ENABLED": "휴식 알림을 사용하도록 설정", "IS_FOCUS_WINDOW": "알림이 활성화되면 앱 응용 프로그램 창에 집중 (데스크톱에만 해당)", "IS_FULL_SCREEN_BLOCKER": "전체 화면 창에 메시지 표시(데스크톱 전용)", "IS_LOCK_SCREEN": "휴식 시간에 화면 잠금 (데스크톱에만 해당)", "MESSAGE": "휴식 메시지 받기", "MIN_WORKING_TIME": "트리거가없는 X 작업 후 휴식 알림을받습니다.", "MOTIVATIONAL_IMGS": "동기 부여 이미지 (웹 URL)", "NOTIFICATION_TITLE": "휴식을 취하다!", "SNOOZE_TIME": "휴식을 취하라는 메시지가 표시되면 일시 중지 시간", "TITLE": "휴식 알림"}, "TIME_TRACKING": {"HELP": "시간 추적 알림은 시간 추적 시작을 잊어버렸을 때 표시되는 배너입니다.", "L_DEFAULT_ESTIMATE": "새 작업에 대한 기본 예상 시간", "L_DEFAULT_ESTIMATE_SUB_TASKS": "새 하위 작업에 대한 기본 예상 시간", "L_IS_AUTO_START_NEXT_TASK": "현재 작업을 완료로 표시하면 다음 작업 추적 시작", "L_IS_NOTIFY_WHEN_TIME_ESTIMATE_EXCEEDED": "예상 시간 초과 시 알림", "L_IS_TRACKING_REMINDER_ENABLED": "추적 알림 사용", "L_IS_TRACKING_REMINDER_FOCUS_WINDOW": "미리 알림이 활성 상태일 때 포커스 앱 창(데스크톱 전용)", "L_IS_TRACKING_REMINDER_NOTIFY": "시간 추적 알림이 표시되면 알림", "L_IS_TRACKING_REMINDER_SHOW_ON_MOBILE": "모바일 앱에 추적 알림 표시", "L_TRACKING_INTERVAL": "시간 추적 간격(실험적)", "L_TRACKING_REMINDER_MIN_TIME": "추적 알림 배너가 표시되기까지 기다릴 시간", "TITLE": "시간 추적"}}, "GLOBAL_RELATIVE_TIME": {"FUTURE": {"A_DAY": "하루 만에", "A_MINUTE": "1분 안에", "A_MONTH": "한 달 안에", "A_YEAR": "일 년 안에", "AN_HOUR": "한 시간 안에", "DAYS": " {{count}} 일 만에", "FEW_SECONDS": "몇 초 안에", "HOURS": " {{count}} 시간 내", "MINUTES": " {{count}} 분 소요", "MONTHS": " {{count}} 개월 내", "YEARS": " {{count}} 년 안에"}, "PAST": {"A_DAY": "하루 전", "A_MINUTE": "조금 전", "A_MONTH": "한 달 전", "A_YEAR": "일 년 전", "AN_HOUR": "한 시간 전", "DAYS": "{{count}} 일 전", "FEW_SECONDS": "몇 초 전", "HOURS": "{{count}} 시간 전", "MINUTES": "{{count}} 분 전", "MONTHS": "{{count}} 개월 전", "YEARS": "{{count}} 년 전"}}, "GLOBAL_SNACK": {"COPY_TO_CLIPPBOARD": "클립 보드에 복사 됨", "ERR_COMPRESSION": "압축 인터페이스 오류", "FILE_DOWNLOADED": "{{fileName}} 다운로드됨", "FILE_DOWNLOADED_BTN": "폴더 열기", "NAVIGATE_TO_TASK_ERR": "작업에 집중할 수 없습니다. 삭제하셨나요?", "PERSISTENCE_DISALLOWED": "데이터는 영구적으로 유지되지 않습니다. 이로 인해 데이터가 손실 될 수 있습니다 !!", "PERSISTENCE_ERROR": "데이터 유지 요청 시 오류: {{err}}", "RUNNING_X": "\"{{str}}\"실행 중.", "SHORTCUT_WARN_OPEN_BOOKMARKS_FROM_TAG": "{{keyCombo}} 을 눌렀지만 북마크 열기 바로 가기는 프로젝트 컨텍스트에서만 사용할 수 있습니다."}, "GPB": {"ASSETS": "자산로드 중 ...", "DBX_DOWNLOAD": "Dropbox : 파일 다운로드 ...", "DBX_GEN_TOKEN": "보관 용 계정 : 토큰 생성 ...", "DBX_META": "Dropbox : 파일 메타 가져 오기 ...", "DBX_UPLOAD": "Dropbox : 파일 업로드 ...", "GITHUB_LOAD_ISSUE": "GitHub : 이슈 데이터로드 ...", "JIRA_LOAD_ISSUE": "Jira : 이슈 데이터로드 중 ...", "SYNC": "데이터 동기화...", "UNKNOWN": "원격 데이터 로드", "WEB_DAV_DOWNLOAD": "WebDAV : 데이터 다운로드 중 ...", "WEB_DAV_UPLOAD": "WebDAV : 데이터 업로드 중 ..."}, "MH": {"ADD_NEW_TASK": "새 작업 추가", "ALL_PLANNED_LIST": "반복/예약", "BOARDS": "보드", "CREATE_PROJECT": "프로젝트 만들기", "CREATE_TAG": "태그 생성", "DELETE_PROJECT": "프로젝트 삭제", "DELETE_TAG": "태그 삭제", "ENTER_FOCUS_MODE": "포커스 모드 시작", "GO_TO_TASK_LIST": "작업 목록으로 이동", "HELP": "도움말", "HM": {"CALENDARS": "방법: 캘린더 연결", "CONTRIBUTE": "기여하기", "GET_HELP_ONLINE": "온라인 도움말 받기", "KEYBOARD": "Howto: 고급 키보드", "REDDIT_COMMUNITY": "Reddit 커뮤니티", "REPORT_A_PROBLEM": "문제 신고하기", "START_WELCOME": "웰컴 투어 시작", "SYNC": "방법: 동기화 구성"}, "METRICS": "측정 항목", "NO_PROJECT_INFO": "사용 가능한 프로젝트가 없습니다. \"프로젝트 만들기\" 버튼을 클릭하여 새 프로젝트를 만들 수 있습니다.", "NO_TAG_INFO": "현재 태그가 없습니다. 작업을 추가하거나 편집할 때 `#yourTagName`을 입력하여 태그를 추가할 수 있습니다.", "NOTES": "노트", "NOTES_PANEL_INFO": "메모는 일정 및 일반 작업 목록 보기에서만 표시할 수 있습니다.", "PLANNER": "플래너", "PROCRASTINATE": "미루다", "PROJECT_MENU": "프로젝트 메뉴", "PROJECT_SETTINGS": "프로젝트 설정", "PROJECTS": "프로젝트", "QUICK_HISTORY": "빠른 기록", "SCHEDULE": "일정", "SEARCH": "검색", "SETTINGS": "설정", "SHOW_SEARCH_BAR": "검색창 표시", "TAGS": "태그", "TASK_LIST": "작업 목록", "TASKS": "할 일 목록", "TOGGLE_SHOW_BOOKMARKS": "북마크 표시 / 숨기기", "TOGGLE_SHOW_ISSUE_PANEL": "이슈 패널 표시/숨기기", "TOGGLE_SHOW_NOTES": "프로젝트 노트 표시 / 숨기기", "TOGGLE_TRACK_TIME": "추적 시작 / 중지 시간", "TRIGGER_SYNC": "수동으로 동기화 트리거", "WORKLOG": "작업 로그"}, "MIGRATE": {"C_DOWNLOAD_BACKUP": "레거시 데이터의 백업을 다운로드하시겠습니까(이전 버전의 Super Productivity와 함께 사용할 수 있음)?", "DETECTED_LEGACY": "레거시 데이터가 검색되었습니다. 우리는 당신을 위해 그것을 마이그레이션 할 것입니다!", "E_MIGRATION_FAILED": "마이그레이션에 실패했습니다! 오류 있음:", "E_RESTART_FAILED": "자동 다시 시작에 실패했습니다. 앱을 수동으로 다시 시작해주세요!", "SUCCESS": "마이그레이션이 모두 완료되었습니다! 지금 앱을 다시 시작하는 중..."}, "PDS": {"ADD_TASKS_FROM_TODAY": "오늘부터 할 일 추가", "BACK": "잠깐 내가 뭔가를 잊었 어!", "BREAK_LABEL": "휴식 (nr / 시간)", "CELEBRATE": "축하해 <i>기념일을 가져 가세요!</i>", "CLEAR_ALL_CONTINUE": "모두 완료하고 계속하기", "D_CONFIRM_APP_CLOSE": {"CANCEL": "아니요, 작업을 취소하십시오.", "MSG": "당신은 일을 끝냈습니다. 집에 갈 시간!", "OK": "네 네! 일시 휴업!"}, "ESTIMATE_TOTAL": "총 견적:", "EVALUATE_DAY": "평가", "EXPORT_TASK_LIST": "작업 목록 내보내기", "NO_TASKS": "오늘은 아무 작업도 없습니다.", "PLAN_TOMORROW": "계획", "REVIEW_TASKS": "검토", "ROUND_5M": "모든 작업을 5 분으로 반올림", "ROUND_15M": "모든 작업을 15 분으로 반올림", "ROUND_30M": "모든 작업을 30 분으로 반올림", "ROUND_TIME_SPENT": "소요 시간", "ROUND_TIME_SPENT_TITLE": "모든 작업에 소요 된 시간. 조심해! 이 작업을 실행 취소 할 수 없습니다!", "ROUND_TIME_WARNING": "!!! 이것이 취소 할 수 없다는 것을주의하십시오 !!!", "ROUND_UP_5M": "모든 작업을 5 분으로 올림", "ROUND_UP_15M": "모든 작업을 15 분으로 올림", "ROUND_UP_30M": "모든 작업을 30 분으로 올림", "SAVE_AND_GO_HOME": "저장하고 집에 가라.", "SAVE_AND_GO_HOME_TOOLTIP": "완료된 모든 작업을 아카이브(작업 로그)로 옮기고 선택적으로 모든 데이터를 동기화하고 앱을 닫습니다.", "START_END": "시작 - 끝", "SUMMARY_FOR": " {{dayStr}}의 일일 요약", "TASKS_COMPLETED": "완료된 작업", "TIME_SPENT_AND_ESTIMATE_LABEL": "소요 시간 / 예상", "TIME_SPENT_ESTIMATE_TITLE": "소요 시간 : 오늘 보낸 총 시간. 아카이브 된 태스크는 포함되지 않습니다. - 예상 시간 : 오늘 작업 한 작업에 대한 예상 시간에서 다른 날에 이미 소비 한 시간을 뺀 시간입니다.", "TIME_SPENT_TODAY_BY_TAG": "태그별로 오늘 보낸 시간", "WEEK": "주"}, "PM": {"TITLE": "프로젝트 측정 항목"}, "PS": {"GLOBAL_SETTINGS": "전체 설정", "ISSUE_INTEGRATION": "이슈 통합", "PRIVACY_POLICY": "개인 정책", "PRODUCTIVITY_HELPER": "생산성 도우미", "PROJECT_SETTINGS": "프로젝트 별 설정", "PROVIDE_FEEDBACK": "피드백을 제공하다", "SYNC_EXPORT": "동기화 및 내보내기", "TAG_SETTINGS": "태그 별 설정", "TOGGLE_DARK_MODE": "어두운 모드 전환"}, "SCHEDULE": {"NO_REPEATABLE_TASKS": "현재 반복 작업이 없습니다. 작업 사이드 패널에서 '작업 반복'을 선택해 작업을 예약할 수 있습니다. 작업을 열려면 작업 위에 마우스를 올려놓으면 나타나는 가장 오른쪽 아이콘을 클릭하거나 모바일에서는 작업을 탭하세요.", "NO_SCHEDULED": "현재 예약 된 작업이 없습니다. 작업 컨텍스트 메뉴에서 \"작업 예약\"을 선택하여 작업을 예약 할 수 있습니다. 그것을 열려면 작업 오른쪽의 작은 점 3 개를 클릭하십시오.", "NO_SCHEDULED_TITLE": "하루 동안 예약된 작업", "REPEATED_TASKS": "반복 작업", "SCHEDULED_TASKS": "예약된 작업", "SCHEDULED_TASKS_WITH_TIME": "미리 알림이 있는 예약된 작업", "START_TASK": "지금 작업을 시작하고 알림 제거"}, "THEMES": {"amber": "호박색", "blue": "푸른", "blue-grey": "청회색", "cyan": "시안 색", "deep-orange": "진한 오렌지", "deep-purple": "딥 퍼플", "green": "녹색", "indigo": "남빛", "light-blue": "하늘색", "light-green": "연한 초록색", "lime": "라임", "pink": "담홍색", "purple": "자주색", "SELECT_THEME": "주제 선택", "teal": "물오리", "yellow": "노랑"}, "V": {"E_1TO10": "1에서 10 사이의 값을 입력하십시오.", "E_DATETIME": "입력 된 값은 datetime이 아닙니다!", "E_DURATION": "유효한 기간을 입력하세요(예: 1시간).", "E_MAX": " {{val}}보다 크지 않아야합니다.", "E_MAX_LENGTH": "최대 {{val}} 자 여야합니다.", "E_MIN": " {{val}}보다 작아야합니다.", "E_MIN_LENGTH": "최소 {{val}} 자 이상이어야합니다.", "E_PATTERN": "잘못된 입력", "E_REQUIRED": "이 입력란은 필수 항목입니다."}, "WW": {"ADD_MORE": "더 추가하기", "ADD_SCHEDULED_FOR_TOMORROW": "내일 예정된 작업 추가 ({{nr}})", "ADD_SOME_TASKS": "하루 계획을 세우십시오!", "DONE_TASKS": "완료된 작업", "DONE_TASKS_IN_ARCHIVE": "현재 여기에는 완료된 작업이 없지만 이미 보관된 작업이 있습니다.", "ESTIMATE_REMAINING": "예상 남은 시간 :", "FINISH_DAY": "피니시 데이", "FINISH_DAY_FOR_PROJECT": "이 프로젝트의 마감일", "FINISH_DAY_FOR_TAG": "이 태그의 마감일", "FINISH_DAY_TOOLTIP": "하루를 평가하고, 완료된 모든 작업을 아카이브로 옮기거나(선택 사항) 다음 날을 계획하세요.", "HELP_PROCRASTINATION": "내가 미루는 걸 도와주세요!", "MOVE_DONE_TO_ARCHIVE": "보관으로 이동 완료", "NO_DONE_TASKS": "현재 완료된 작업이 없습니다", "NO_PLANNED_TASK_ALL_DONE": "모든 작업 완료", "NO_PLANNED_TASKS": "계획된 작업 없음", "READY_TO_WORK": "준비 완료!", "RESET_BREAK_TIMER": "중단 타이머없이 재설정", "TIME_ESTIMATED": "예상 시간 :", "TODAY_REMAINING": "오늘이 남았습니다:", "WITHOUT_BREAK": "휴식없이 :", "WORKING_TODAY": "오늘은 일한다:", "WORKING_TODAY_ARCHIVED": "보관된 작업에서 오늘 작업한 시간"}}