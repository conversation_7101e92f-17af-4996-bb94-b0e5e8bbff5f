{"ci": {"collect": {"staticDistDir": "./dist", "numberOfRuns": 3, "url": ["http://localhost/browser/index.html"]}, "assert": {"preset": "lighthouse:no-pwa", "assertions": {"categories:performance": ["warn", {"minScore": 0.8}], "categories:accessibility": ["warn", {"minScore": 0.9}], "categories:best-practices": ["warn", {"minScore": 0.9}], "categories:seo": ["warn", {"minScore": 0.9}], "first-contentful-paint": ["warn", {"maxNumericValue": 2500}], "largest-contentful-paint": ["warn", {"maxNumericValue": 4000}], "cumulative-layout-shift": ["warn", {"maxNumericValue": 0.1}], "total-blocking-time": ["warn", {"maxNumericValue": 300}], "interactive": ["warn", {"maxNumericValue": 5000}], "resource-summary.script.count": ["warn", {"maxNumericValue": 100}], "resource-summary.total.count": ["warn", {"maxNumericValue": 140}]}}, "upload": {"target": "temporary-public-storage"}}}