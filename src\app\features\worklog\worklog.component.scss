@import '../../../common';

:host {
  text-align: center;
}

.total-time {
  margin-top: var(--s4);
}

.year {
  border-top: 1px dashed var(--theme-separator-color);

  &-title {
    margin-top: var(--s3);
  }

  &-time-spent {
    font-size: 20px;
    line-height: 26px;
    margin-bottom: var(--s4);
  }
}

.month {
  margin-top: calc(3 * var(--s));
  margin-bottom: 0;
  border-top: 1px dashed var(--theme-separator-color);
  padding-top: var(--s2);

  &-title {
    margin-bottom: var(--s);

    button {
      margin-left: var(--s);
    }
  }

  &-time-spent {
    font-size: 18px;
    line-height: 22px;
    margin-bottom: var(--s3);
  }
}

.weeks {
  display: flex;
  justify-content: center;
  flex-wrap: wrap-reverse;

  @include mq(xs) {
    flex-wrap: wrap;
  }
}

.week {
  --table-outer-padding-left-right: var(--s6);
  margin: var(--s4) 0;
  display: flex;
  align-items: center;
  flex-direction: column;

  &-header {
    display: flex;
    margin-bottom: var(--s2);
    align-self: center;

    button {
      margin-left: var(--s);
    }
  }

  &-title {
    white-space: nowrap;
    margin-bottom: 0;
  }

  table.week-table {
    border-collapse: collapse;
    width: 320px;

    // tbody might or might not be there
    &,
    > tbody {
      > tr.week-row > {
        th,
        td {
          padding: var(--s-half) var(--s);
          white-space: nowrap;
          vertical-align: middle;

          > * {
            vertical-align: middle;
          }

          &:first-child {
            padding-left: var(--table-outer-padding-left-right);
            text-align: left;
            border-top-left-radius: var(--card-border-radius);
          }

          &:last-child {
            padding-right: var(--table-outer-padding-left-right);
            border-top-right-radius: var(--card-border-radius);

            mat-icon {
              // prevent table recalculation on icon change
              min-width: 28px;
              vertical-align: middle;
              // height is off for some reason
              height: auto;
              text-shadow: none !important;
            }
          }
        }
      }

      > tr.week-row {
        &:hover,
        &.isExpanded {
          cursor: pointer;

          > td {
            // bold without respacing
            text-shadow: 0.01px 0 0.01px;
            background: var(--theme-card-bg);
            box-shadow: var(--whiteframe-shadow-2dp);

            background-color: var(--theme-bg-lighter);
          }
        }
      }
    }
  }
}

.day-date {
  position: relative;

  span {
    &:first-child {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      visibility: visible;

      tr:hover & {
        visibility: hidden;
      }
    }

    &:last-child {
      visibility: hidden;

      tr:hover & {
        visibility: visible;
      }
    }
  }
}

.summary-table-wrapper {
  display: inline-block;
  width: 100%;
  margin-bottom: var(--s);
  border: none;
  // position above parent table shadow
  position: relative;
  z-index: 3;

  border-top-left-radius: 0;
  border-top-right-radius: 0;

  > table {
    width: 100%;
    text-align: left;
    border-collapse: collapse;

    * {
      user-select: text;
      -webkit-touch-callout: default;
    }

    // Overwrite default styles in table.scss
    tr {
      &:first-of-type {
        th,
        td {
          border-top-width: 1px;
        }
      }
    }
  }

  > table > tr:focus {
    outline: none;
    box-shadow: 0px 0px 0px 1px var(--palette-primary-400);
    border-radius: 3px;
  }

  caption {
    font-weight: bold;
  }

  td {
    text-align: left;
    white-space: normal;
    line-height: 16px;
    padding: var(--s) calc(var(--s) * 1.5);
    vertical-align: middle;

    &.title {
      padding-left: var(--s2);
      padding-right: 0;
    }

    &.worked {
      white-space: nowrap;
      text-align: center;
      padding: 0;

      ::ng-deep .inline-input-wrapper {
        padding: var(--s) calc(var(--s) * 1.5);
      }
    }

    &.actions {
      padding: 0 0 0 0;
    }

    &.isSubTask {
      .task-title {
        font-style: italic;
        padding-left: var(--s2);
        position: relative;
        display: inline-block;
        // prevent overflow for looooong words
        word-break: break-word;

        &:before {
          content: '•';
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
        }
      }
    }
  }
}

.repeat-task-icon {
  vertical-align: middle;
  margin-right: 4px;
  transform: rotate(45deg);
  opacity: 0.7;
}

.project-color {
  display: inline-block;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin-right: var(--s);
  vertical-align: middle;
}
