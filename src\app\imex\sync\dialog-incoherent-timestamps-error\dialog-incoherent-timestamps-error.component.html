<mat-dialog-content>
  <div style="text-align: center; margin-bottom: 16px">
    <mat-icon
      color="warn"
      style="font-size: 48px; height: 48px; width: 48px"
      >error
    </mat-icon>
  </div>

  <p><strong>The date of your remote data are from the future</strong></p>
  <p>You should check the time configured on your systems!</p>

  <div style="text-align: center; margin-bottom: 16px">
    <button
      (click)="downloadBackup()"
      color="primary"
      mat-stroked-button
      type="button"
    >
      <mat-icon>file_download</mat-icon>
      {{ T.F.SYNC.D_INCOMPLETE_SYNC.BTN_DOWNLOAD_BACKUP | translate }}
    </button>
  </div>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <div class="wrap-buttons">
    <button
      (click)="close()"
      color="primary"
      mat-button
    >
      {{ T.G.CANCEL | translate }}
    </button>

    <button
      (click)="close('FORCE_UPDATE_LOCAL')"
      color="warn"
      mat-stroked-button
    >
      <mat-icon>file_download</mat-icon>
      Force Download Remote
      <!--      {{ T.F.SYNC.D_INCOMPLETE_SYNC.BTN_FORCE_UPLOAD | translate }}-->
    </button>

    <button
      (click)="close('FORCE_UPDATE_REMOTE')"
      color="warn"
      mat-stroked-button
    >
      <mat-icon>file_upload</mat-icon>
      {{ T.F.SYNC.D_INCOMPLETE_SYNC.BTN_FORCE_UPLOAD | translate }}
    </button>
  </div>
</mat-dialog-actions>
