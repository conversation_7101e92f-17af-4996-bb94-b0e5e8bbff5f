name: Create android Play Store APK
on:
  push:
    branches: [master, release/*, test/git-actions]
    tags:
      - v*
  workflow_dispatch:
    inputs: {}

jobs:
  build-android:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v4
      - uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: 21
      - name: <PERSON>up Gradle
        uses: gradle/actions/setup-gradle@v4
      #      - name: Build with Gradle
      #        run: ./gradlew build
      - name: Setup android-sdk
        uses: android-actions/setup-android@v3
        with:
          accept-android-sdk-licenses: true
          log-accepted-android-sdk-licenses: true #make accepting the android sdk license verbose

      - name: Get npm cache directory
        id: npm-cache-dir
        run: |
          echo "::set-output name=dir::$(npm config get cache)"
      - uses: actions/cache@v4
        id: npm-cache # use this to check for `cache-hit` ==> if: steps.npm-cache.outputs.cache-hit != 'true'
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-
      - name: Install npm Packages
        #  if: steps.npm-cache.outputs.cache-hit != 'true'
        run: npm i

      - name: Decode Keystore
        env:
          RELEASE_KEYSTORE_PASSWORD: ${{ secrets.DROID_KEYSTORE_PASSWORD }}
          RELEASE_KEYSTORE_ALIAS: ${{ secrets.DROID_KEYSTORE_ALIAS }}
          RELEASE_KEY_PASSWORD: ${{ secrets.DROID_KEY_PASSWORD }}
        run: |
          echo "${{ secrets.DROID_KEYSTORE_BASE_64 }}" | base64 --decode > keystore.jks

      - name: Build
        run: npm run dist:android:prod
        env:
          RELEASE_KEYSTORE_PASSWORD: ${{ secrets.DROID_KEYSTORE_PASSWORD }}
          RELEASE_KEYSTORE_ALIAS: ${{ secrets.DROID_KEYSTORE_ALIAS }}
          RELEASE_KEY_PASSWORD: ${{ secrets.DROID_KEY_PASSWORD }}

      - name: DEBUG
        run: |
          ls -la android/app/build/outputs
          ls -la android/app/build/outputs/apk
          ls -la android/app/build/outputs/apk/play
          ls -la android/app/build/outputs/apk/play/release

      # APK is now signed automatically by Gradle using signingConfig

      - name: 'Upload APK files'
        uses: actions/upload-artifact@v4
        with:
          name: sup-android-release
          path: android/app/build/outputs/apk/**/*.apk

      - name: Wait for main release to be created
        if: startsWith(github.ref, 'refs/tags/v')
        run: |
          echo "Waiting for main release to be created..."
          for i in {1..30}; do
            if gh release view ${{ github.ref_name }} --repo ${{ github.repository }} >/dev/null 2>&1; then
              echo "Release found!"
              break
            fi
            echo "Waiting for release... (attempt $i/30)"
            sleep 30
          done
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload APK to existing GitHub Release
        if: startsWith(github.ref, 'refs/tags/v')
        run: |
          for apk in android/app/build/outputs/apk/play/release/*.apk; do
            if [ -f "$apk" ]; then
              echo "Uploading $apk to release ${{ github.ref_name }}"
              gh release upload ${{ github.ref_name }} "$apk" --repo ${{ github.repository }}
            fi
          done
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload to Google Play Console
        if: startsWith(github.ref, 'refs/tags/v')
        uses: r0adkll/upload-google-play@v1.1.3
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_JSON }}
          packageName: com.superproductivity.superproductivity
          releaseFiles: android/app/build/outputs/apk/play/release/*.apk
          track: internal
          status: draft
          inAppUpdatePriority: 2
