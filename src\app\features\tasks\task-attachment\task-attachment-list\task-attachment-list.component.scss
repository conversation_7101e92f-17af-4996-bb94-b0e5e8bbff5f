@import '../../../../../common';

:host {
  --this-height: 88px;
  --this-margin: var(--s);
  --this-upper-height: 60px;
  --this-lower-height: 28px; /* 88px - 60px */
  --larger-size: 65%;
  --smaller-size: 35%; /* 100% - 65% */

  display: block;
}

.attachments {
  list-style-type: none;
  display: flex;
  margin: calc(-1 * var(--this-margin));
  padding: 0;
  flex-flow: row;
  justify-content: left;
  flex-wrap: wrap;
}

.attachment {
  margin: var(--this-margin);
  padding: 0;
  border-radius: var(--card-border-radius);
  position: relative;
  overflow: hidden;

  color: var(--theme-text-color-less-intense);
  background-color: var(--theme-attachment-bg);
  border: 1px solid var(--theme-attachment-border);

  //.controls,
  > a:focus + .controls,
  &.focus .controls,
  &:hover .controls {
    visibility: visible;

    a,
    button {
      opacity: 1;
      transition: var(--transition-fast);
    }

    .view-btn,
    .copy-btn,
    .edit-btn,
    .trash-btn {
      transform: translate(0, 0);
    }
  }
}

.attachment-link {
  padding: var(--s);
  height: var(--this-height);
  min-width: calc(var(--this-height) + var(--s2));
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: var(--card-border-radius);
  cursor: pointer;
  box-sizing: border-box;

  &:focus {
    outline: none;
  }

  mat-icon {
    font-size: var(--s5);
    height: var(--s5);
    width: var(--s5);
  }

  &.isImage {
    padding: 0;
    cursor: zoom-in;

    img {
      cursor: zoom-in;
      height: var(--this-height);
      width: auto;
    }
  }

  .title {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  //transform: translateY(25%);
  z-index: 5;
  visibility: hidden;
  display: flex;
  flex-direction: row;
  justify-content: stretch;
  align-items: stretch;
  flex-wrap: wrap;
  pointer-events: none;

  a,
  button {
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--transition-enter);
    margin: 0;
    padding: 0;
    min-height: 0;
    flex-grow: 1;
    min-width: 20px;
    opacity: 0;
    border-width: 0;

    background: var(--theme-attachment-control-bg);
    border-color: var(--theme-attachment-control-border);
    &:hover {
      background: var(--theme-attachment-control-hover-bg);
    }
  }
}

.view-btn {
  pointer-events: all;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  flex: 1;
  flex-basis: var(--larger-size);
  width: var(--larger-size);
  min-width: var(--larger-size);
  line-height: var(--this-upper-height);
  height: var(--this-upper-height);
  transition: var(--transition-duration-xs) var(--ani-standard-timing) !important;

  @include mq(xs) {
    transform: translate(-100%, -50%);
  }

  &.isImage {
    pointer-events: none;

    @include mq(xs) {
      transform: translate(0, -50%);
    }
  }

  mat-icon {
    margin: 0 !important;
  }
}

.edit-btn,
.trash-btn {
  margin-top: 2px;
  line-height: var(--this-lower-height);
  height: var(--this-lower-height);
}

.copy-btn {
  pointer-events: all;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  border-bottom-right-radius: 0;
  flex-basis: var(--smaller-size);
  width: var(--smaller-size);
  line-height: var(--this-upper-height);
  height: var(--this-upper-height);

  @include mq(xs) {
    transform: translate(100%, -50%);
  }
}

.edit-btn {
  pointer-events: all;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  border-bottom-right-radius: 0;
  flex-basis: var(--larger-size);
  width: var(--larger-size);

  @include mq(xs) {
    transform: translate(-100%, 50%);
  }
}

.trash-btn {
  pointer-events: all;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 0;
  margin-left: 1px;
  flex-basis: var(--smaller-size);
  width: var(--smaller-size);

  &:hover {
    background: var(--c-warn) !important;

    mat-icon {
      color: var(--theme-card-bg) !important;
    }
  }

  @include mq(xs) {
    transform: translate(100%, 50%);
  }
}
