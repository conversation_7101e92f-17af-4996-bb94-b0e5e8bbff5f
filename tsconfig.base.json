{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2022", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "strict": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noUnusedLocals": false, "noImplicitAny": false, "noImplicitOverride": true, "strictPropertyInitialization": true, "useDefineForClassFields": false, "target": "ES2022", "resolveJsonModule": true, "typeRoots": ["node_modules/@types"], "lib": ["es2022", "dom"], "paths": {"@super-productivity/plugin-api": ["packages/plugin-api/src/index.ts"]}, "plugins": [{"transform": "typia/lib/transform"}]}}