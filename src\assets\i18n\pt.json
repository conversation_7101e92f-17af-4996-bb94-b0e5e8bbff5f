{"ANDROID": {"NOTIFICATIONS": {"SYNC_CONFLICT_MSG": "Por favor, revise e decida o que fazer.", "SYNC_CONFLICT_TITLE": "Houve um conflito de sincronização"}, "PERMANENT_NOTIFICATION_MSGS": {"INITIAL": "Aplicativo sendo executado em segundo plano para permitir a sincronização, se ativado", "NO_ACTIVE_TASKS": "<PERSON><PERSON><PERSON><PERSON> tarefa ativa", "SYNCING": "Sincronizando"}}, "APP": {"B_INSTALL": {"IGNORE": "<PERSON><PERSON><PERSON>", "INSTALL": "Instalar", "MSG": "Deseja instalar Super Productivity como PWA?"}, "B_OFFLINE": "Você está desconectado da internet. A sincronização e solicitação de dados do provedor de problemas não funcionará.", "UPDATE_MAIN_MODEL": "Super Productivity recebeu uma grande atualização! Algumas migrações para seus dados são necessárias. Observe que isso torna seus dados incompatíveis com versões mais antigas do aplicativo.", "UPDATE_MAIN_MODEL_NO_UPDATE": "Nenhuma atualização de modelo escolhida. Note que você deve fazer o downgrade para a última versão, se não desejar executar a atualização do modelo.", "UPDATE_WEB_APP": "Nova versão disponível. Carregar nova versão?"}, "BL": {"NO_TASKS": "Atualmente não há tarefas no seu backlog"}, "CONFIRM": {"AUTO_FIX": "Seus dados parecem estar danificados. Quer tentar consertar automaticamente? Isso pode resultar em perda parcial de dados.", "RELOAD_AFTER_IDB_ERROR": "Não é possível acessar o banco de dados :( As possíveis causas são uma atualização do aplicativo em segundo plano ou pouco espaço em disco. Pressione OK para recarregar o aplicativo (pode exigir a reinicialização manual do aplicativo em algumas plataformas).", "RESTORE_FILE_BACKUP": "Pa<PERSON>ce não haver NENHUM DADO, mas há backups disponíveis em \"{{dir}}\". Quer restaurar o backup mais recente de {{from}}?", "RESTORE_FILE_BACKUP_ANDROID": "Parece não haver NENHUM DADO, mas há um backup disponível. Você quer carregá-lo?", "RESTORE_STRAY_BACKUP": "Durante a última sincronização, pode ter ocorrido algum erro. Você quer restaurar o último backup?"}, "DATETIME_SCHEDULE": {"LATER_TODAY": "Hoje mais tarde", "NEXT_WEEK": "Semana que vem", "PLACEHOLDER": "Por favor selecione uma data", "PRESS_ENTER_AGAIN": "Pressione enter novamente para salvar", "TOMORROW": "aman<PERSON><PERSON>"}, "F": {"ATTACHMENT": {"DIALOG_EDIT": {"ADD_ATTACHMENT": "Adicionar anexo", "EDIT_ATTACHMENT": "Editar anexo", "LABELS": {"FILE": "Caminho do arquivo", "IMG": "Imagem", "LINK": "Url"}, "SELECT_TYPE": "Selecione um tipo", "TYPES": {"FILE": "Arquivo (aberto pelo aplicativo padrão do sistema)", "IMG": "Imagem (mostrar como miniatura)", "LINK": "Link (abre no navegador)"}}}, "BOARDS": {"DEFAULT": {"DONE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EISENHAUER_MATRIX": "<PERSON><PERSON>", "IMPORTANT": "Importante", "IN_PROGRESS": "Em progresso", "KANBAN": "Ka<PERSON><PERSON>", "NOT_URGENT_IMPORTANT": "Não Urgente & Importante", "NOT_URGENT_NOT_IMPORTANT": "Não Urgente & Não Importante", "TO_DO": "A Fazer", "URGENT": "Urgente", "URGENT_IMPORTANT": "Urgente & Importante", "URGENT_NOT_IMPORTANT": "Urgente & Não Importante"}, "FORM": {"ADD_NEW_PANEL": "Adicionar novo <PERSON>el", "BACKLOG_TASK_FILTER_ALL": "Todos", "BACKLOG_TASK_FILTER_NO_BACKLOG": "Excluir", "BACKLOG_TASK_FILTER_ONLY_BACKLOG": "Apenas backlog", "BACKLOG_TASK_FILTER_TYPE": "Tarefas de lista de pendências", "COLUMNS": "Colunas", "TAGS_EXCLUDED": "Tags Excluídas", "TAGS_REQUIRED": "Tags Obrigatórias", "TASK_DONE_STATE": "Estado da Tarefa Concluída", "TASK_DONE_STATE_ALL": "Todos", "TASK_DONE_STATE_DONE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TASK_DONE_STATE_UNDONE": "Não concluído"}, "V": {"ADD_NEW_BOARD": "Adicionar novo quadro", "CONFIRM_DELETE": "Tem certeza de que deseja excluir este quadro?", "CREATE_NEW_TAG_BTN": "Criar Tag", "CREATE_NEW_TAG_MSG": "1 nova tag precisa ser criada para que este quadro funcione", "CREATE_NEW_TAGS_BTN": "Criar Tags", "CREATE_NEW_TAGS_MSG": "{{nr}} novas tags precisam ser criadas para que este quadro funcione", "EDIT_BOARD": "Editar quadro", "NO_PANELS_BTN": "<PERSON><PERSON><PERSON><PERSON>", "NO_PANELS_MSG": "Este quadro não tem painéis configurados. Adicione alguns painéis a ele."}}, "CALDAV": {"DIALOG_INITIAL": {"TITLE": "Configurar CalDav para o projeto"}, "FORM": {"CALDAV_CATEGORY_FILTER": "Categoria para a qual filtrar problemas (não deixe em branco para nenhum)", "CALDAV_PASSWORD": "<PERSON><PERSON> se<PERSON><PERSON>", "CALDAV_RESOURCE": "O nome do recurso CalDav (o calendário)", "CALDAV_URL": "URL CalDav (o URL base)", "CALDAV_USER": "Seu nome de usu<PERSON><PERSON>", "IS_TRANSITION_ISSUES_ENABLED": "Concluir automaticamente o CalDav todos na conclusão da tarefa"}, "FORM_SECTION": {"HELP": "<p>Aqui você pode configurar SuperProductivity para listar CalDav todos incompletos para um projeto específico no painel de criação de tarefas na visualização de planejamento diário. Eles serão listados como sugestões e fornecerão um link para a tarefa, bem como mais informações sobre ela.</p> <p>Além disso, você pode adicionar e sincronizar automaticamente todas as tarefas não concluídas com a lista de pendências de suas tarefas.</p>", "TITLE": "CalDav"}, "ISSUE_CONTENT": {"ASSIGNEE": "Atribu<PERSON><PERSON>", "AT": "Em", "ATTACHMENTS": "Anexos", "CHANGED": "mudou", "COMMENTS": "Comentários", "COMPONENTS": "Componentes", "DESCRIPTION": "Descrição", "LABELS": "Categorias", "LIST_OF_CHANGES": "Lista de alterações", "MARK_AS_CHECKED": "Marcar atualizações como marcadas", "ON": "Em", "RELATED": "Relacionado", "STATUS": "Status", "STORY_POINTS": "Pontos de História", "SUB_TASKS": "Subtarefas", "SUMMARY": "Resumo", "WORKLOG": "Registo de Trabalho", "WRITE_A_COMMENT": "Escrever um comentário"}, "S": {"CALENDAR_NOT_FOUND": "CalDav: <PERSON><PERSON><PERSON><PERSON> \"{{calendarName}}\" não encontrado", "CALENDAR_READ_ONLY": "CalDav: o calendário \"{{calendarName}}\" é somente leitura", "ISSUE_NOT_FOUND": "CalDav: Todo \"{{issueId}}\" parece ter sido excluído do servidor."}}, "CALENDARS": {"BANNER": {"ADD_AS_TASK": "Adicionar como Tarefa", "FOCUS_TASK": "Tarefa focada", "TXT": "<strong>{{title}}</strong> começa em <strong>{{start}}</strong>!", "TXT_MULTIPLE": "<strong>{{title}}</strong> começa em <strong>{{start}}</strong>!<br> (e {{nrOfOtherBanners}} outros eventos estão pendentes)", "TXT_PAST": "<strong>{{title}}</strong> iniciou em <strong>{{start}}</strong>!", "TXT_PAST_MULTIPLE": "<strong>{{title}}</strong> iniciou em <strong>{{start}}</strong>!<br> (e {{nrOfOtherBanners}} outros eventos estão pendentes)"}, "S": {"CAL_PROVIDER_ERROR": "Erro no provedor de calendá<PERSON>: {{errTxt}}"}}, "CONFIG": {"S": {"UPDATE_SECTION": "Configurações atualizadas para <strong>{{sectionKey}}</strong>"}}, "D_RATE": {"A_HOW": "Como e onde avaliar", "BTN_DONT_BOTHER": "Não me incomode novamente", "TITLE": "🙈 Por favor, perdoe-nos, mas...", "TXT": "Você ajudaria imensamente o projeto <strong>dando uma boa classificação, se gostar!</strong>"}, "DOMINA_MODE": {"FORM": {"HELP": "Repete a frase configurada a cada X ao monitorar o tempo de uma tarefa.", "L_INTERVAL": "Intervalo para repetir frase", "L_TEXT": "Texto", "L_TEXT_DESCRIPTION": "Ex.: \"Trabalhar em ${currentTaskTitle}!\"", "L_VOICE": "Selecionar uma Voz", "L_VOICE_DESCRIPTION": "Escolher uma voz", "TITLE": "<PERSON><PERSON>"}}, "DROPBOX": {"S": {"ACCESS_TOKEN_ERROR": "Dropbox: Não é possível gerar o token de acesso a partir do código de autenticação", "ACCESS_TOKEN_GENERATED": "Dropbox: token de acesso gerado a partir do código de autenticação", "AUTH_ERROR": "Dropbox: token de acesso inválido fornecido", "AUTH_ERROR_ACTION": "<PERSON><PERSON>", "OFFLINE": "Dropbox: não é possível sincronizar, porque off-line", "SYNC_ERROR": "Dropbox: erro ao sincronizar", "UNABLE_TO_GENERATE_PKCE_CHALLENGE": "Dropbox: Não foi possível gerar o desafio PKCE."}}, "FINISH_DAY_BEFORE_EXIT": {"C": {"FINISH_DAY_BEFORE_EXIT": "Existem {{nr}} tarefas concluídas na sua lista de hoje que ainda não foram movidas para o arquivo. Quer mesmo parar sem terminar o dia?"}}, "FOCUS_MODE": {"B": {"SESSION_RUNNING": "A Sessão de Foco está em execução", "TO_FOCUS_OVERLAY": "Para Sobreposição de Foco"}, "BACK_TO_PLANNING": "Retornar ao Planejamento", "CONGRATS": "Parabéns por completar essa sessão!", "CONTINUE_FOCUS_SESSION": "<PERSON><PERSON><PERSON>r Sessão de Foco", "COUNTDOWN": "Contagem regressiva", "FINISH_TASK_AND_SELECT_NEXT": "Finalizar tarefa e selecionar a próxima", "FLOWTIME": "Flowtime", "FOR_TASK": "para a tarefa", "GET_READY": "Prepare-se para sua sessão de foco!", "GO_TO_PROCRASTINATION": "<PERSON>que ajuda quando procrastinar", "GOGOGO": "Vamos, vamos, vamos!", "NEXT": "Próxima", "ON": "<PERSON><PERSON>do", "OPEN_ISSUE_IN_BROWSER": "Abrir tarefa em um navegador", "POMODORO_BACK": "Retornar", "POMODORO_DISABLE": "Desativar Pomodoro", "POMODORO_INFO": "As sessões de foco não podem ser usadas com o Pomodoro ativado", "PREP_GET_MENTALLY_READY": "Prepare-se mentalmente para focar e ser produtivo", "PREP_SIT_UPRIGHT": "Corrija sua postura (sentado ou em pé)", "PREP_STRETCH": "Faça alguns suaves alongamentos ", "SELECT_ANOTHER_TASK": "Selecione outra tarefa", "SELECT_TASK": "Selecione a tarefa que irá focar", "SESSION_COMPLETED": "Sessão de Foco Concluída!", "SET_FOCUS_SESSION_DURATION": "Definir duração de sessão", "SHOW_HIDE_NOTES_AND_ATTACHMENTS": "Mostrar/ocultar notas e anexos da tarefa", "START_FOCUS_SESSION": "Iniciar <PERSON> F<PERSON>", "START_NEXT_FOCUS_SESSION": "Iniciar próxima Sessão de Foco", "WORKED_FOR": "<PERSON><PERSON><PERSON> trabalhou por"}, "GITEA": {"DIALOG_INITIAL": {"TITLE": "Configurar Gitea para Projeto"}, "FORM": {"FILTER_USER": "Nome de usuário (ex.: para filtrar suas próprias alterações)", "HOST": "Endereço do servidor (ex: https://try.gitea.io)", "REPO_FULL_NAME": "Nome do usuário ou organização/projeto", "REPO_FULL_NAME_DESCRIPTION": "Pode ser encontrado como parte da url, ao acessar o projeto no navegador", "SCOPE": "Escopo", "SCOPE_ALL": "Todos", "SCOPE_ASSIGNED": "Atribuídos a mim", "SCOPE_CREATED": "Criados por mim", "TOKEN": "<PERSON><PERSON> de acesso"}, "FORM_SECTION": {"HELP": "<p>Aqui você pode configurar o SuperProductivity para listar tarefas do Gitea de um repositório específico no painel de tarefas na tela de planejamento diário. Elas também serão listadas como sugestões e será disponibilizado um link para a tarefa bem como mais informações sobre a mesma.</p> <p>Além disso você pode adicionar e sincronizar todas as tarefas abertas para o seu backlog.</p><p>", "TITLE": "<PERSON><PERSON><PERSON>"}, "ISSUE_CONTENT": {"ASSIGNEE": "Responsável", "AT": "em", "DESCRIPTION": "Descrição", "LABELS": "Etiquetas", "MARK_AS_CHECKED": "Marcar atualizações como verificadas", "PROJECT": "Projeto", "STATUS": "Estado", "SUMMARY": "Resumo", "WRITE_A_COMMENT": "Escreva um comentário"}, "S": {"ERR_UNKNOWN": "Gitea: <PERSON>rro desconhecido {{statusCode}} {{errorMsg}}. Limit de uso da api excedido?"}}, "GITHUB": {"DIALOG_INITIAL": {"TITLE": "Configurar GitHub para Projeto"}, "FORM": {"FILTER_USER": "Nome de usuário (ex.: para filtrar as alterações sozinho)", "INVALID_TOKEN_MESSAGE": "Não é um token válido do GitHub. Ele deve começar com \"ghp_\".", "IS_ASSIGNEE_FILTER": "Filtrar problemas atribuídos a mim", "REPO": "username/repositoryName", "TOKEN": "<PERSON><PERSON> de acesso"}, "FORM_SECTION": {"HELP": "<p>Aqui você pode configurar o SuperProductivity para listar os problemas abertos do GitHub para um repositório específico no painel de criação de tarefas na visualização de planejamento diário. Eles serão listados como sugestões e fornecerão um link para o problema, além de mais informações sobre ele. </p> <p> Além disso, você pode adicionar e sincronizar automaticamente todos os problemas em aberto no seu log de tarefas.</p>", "TITLE": "GitHub"}, "ISSUE_CONTENT": {"ASSIGNEE": "Responsável", "AT": "em", "DESCRIPTION": "Descrição", "LABELS": "Etiquetas", "LAST_COMMENT": "<PERSON><PERSON><PERSON>", "LOAD_ALL_COMMENTS": "<PERSON><PERSON><PERSON> todos os {{nr}} coment<PERSON><PERSON><PERSON>", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "Carregar descrição e todos os comentários", "MARK_AS_CHECKED": "Marcar atualizações como checadas", "STATUS": "Status", "SUMMARY": "<PERSON><PERSON><PERSON>", "WRITE_A_COMMENT": "Comentar"}, "S": {"CONFIG_ERROR": "GitHub: Erro ao mapear dados. O nome do seu repositório está correto?", "ERR_UNKNOWN": "GitHub: Erro desconhecido {{statusCode}} {{errorMsg}}. Limite de taxa de API excedido?"}}, "GITLAB": {"DIALOG_INITIAL": {"TITLE": "Configurar GitLab para Projeto"}, "*********************": {"PAST_DAY_INFO": "A duração pré-preenchida contém dados não rastreados de dias anteriores.", "T_ALREADY_TRACKED": "<PERSON><PERSON>", "T_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "T_TO_BE_SUBMITTED": "A ser enviado", "TITLE": "Enviar tempo gasto em problemas para o GitLab", "TOTAL_MSG": "Você enviará o total de tempo trabalhado hoje <em>{{totalTimeToSubmit}}</em> para <em>{{nrOfTasksToSubmit}}</em> diferentes questões."}, "FORM": {"FILTER": "Filtro customizado", "FILTER_DESCRIPTION": "Consulte https://docs.gitlab.com/ee/api/issues.html#list-issues. Vários podem ser combinados por &", "FILTER_USER": "Nome de usuário (ex.: para filtrar as alterações sozinho)", "GITLAB_BASE_URL": "URL base GitLab personalizado", "PROJECT": "<PERSON><PERSON><PERSON> completo ou nome de usuário / projeto", "PROJECT_HINT": "e.g. johannesjo/super-produtividade", "SCOPE": "escopo", "SCOPE_ALL": "Todos", "SCOPE_ASSIGNED": "Atribuído a mim", "SCOPE_CREATED": "Criado por mim", "SOURCE": "Fonte", "SOURCE_GLOBAL": "Todos", "SOURCE_GROUP": "grupo", "SOURCE_PROJECT": "projeto", "SUBMIT_TIMELOGS": "Enviar registros de tempo para o Gitlab", "SUBMIT_TIMELOGS_DESCRIPTION": "Mostrar a caixa de diálogo de controle de tempo após clicar no dia de término", "TOKEN": "<PERSON><PERSON> de acesso"}, "FORM_SECTION": {"HELP": "<p> Aqui você pode configurar o SuperProductivity para listar problemas abertos do GitLab (seja a versão on-line ou uma instância auto-hospedada) para um projeto específico no painel de criação de tarefas na exibição de planejamento diário. Eles serão listados como sugestões e fornecerão um link para o problema, além de mais informações sobre o assunto. </p> <p> Alé<PERSON> disso, você pode adicionar e sincronizar automaticamente todos os problemas em aberto no backlog de tarefas. </p>", "TITLE": "GitLab"}, "ISSUE_CONTENT": {"ASSIGNEE": "Responsável", "AT": "em", "DESCRIPTION": "Descrição", "LABELS": "Etiquetas", "MARK_AS_CHECKED": "Marcar atualizações como checadas", "PROJECT": "projeto", "STATUS": "Status", "SUMMARY": "Indíce", "WRITE_A_COMMENT": "Comentar"}, "S": {"ERR_UNKNOWN": "GitLab: Erro desconhecido {{statusCode}} {{errorMsg}}"}}, "ISSUE": {"CROSS_ORIGIN_BROWSER_WARNING": "É provável que esta integração não funcione no seu browser. Descarregue a versão para computador ou Android do Super Productivity!", "DEFAULT": {"ISSUE_STR": "Questão", "ISSUES_STR": "Problemas"}, "DEFAULT_PROJECT_DESCRIPTION": "Projeto atribuído a tarefas criadas a partir de problemas.", "DEFAULT_PROJECT_LABEL": "Projeto de Super Produtividade por defeito", "HOW_TO_GET_A_TOKEN": "Como obter um token?", "ISSUE_CONTENT": {"ASSIGNEE": "Atribu<PERSON><PERSON>", "AT": "Em", "ATTACHMENTS": "Anexos", "AUTHOR": "Autor", "CATEGORY": "Categoria", "CHANGED": "mudou", "COMMENTS": "Comentários", "COMPONENTS": "Componentes", "DESCRIPTION": "Descrição", "DONE_RATIO": "<PERSON><PERSON><PERSON>", "DUE_DATE": "Data de vencimento", "LABELS": "Etiquetas", "LAST_COMMENT": "<PERSON><PERSON><PERSON>", "LIST_OF_CHANGES": "Lista de alterações", "LOAD_ALL_COMMENTS": "<PERSON><PERSON><PERSON> todos os {{nr}} coment<PERSON><PERSON><PERSON>", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "Carregar descrição e todos os comentários", "LOCATION": "Localização", "MARK_AS_CHECKED": "Marcar atualizações como verificadas", "ON": "Em", "PRIORITY": "Prioridade", "RELATED": "Relacionado", "START": "Iniciar", "STATUS": "Estado", "STORY_POINTS": "Pontos de História", "SUB_TASKS": "Subtarefas", "SUMMARY": "Resumo", "TIME_SPENT": "Tempo gasto", "TYPE": "Tipo", "VERSION": "Vers<PERSON>", "WORKLOG": "Registo de Trabalho", "WRITE_A_COMMENT": "Escrever um comentário"}, "S": {"ERR_GENERIC": "{{issue<PERSON><PERSON>ider<PERSON>ame}} Erro: {{errTxt}}", "ERR_NETWORK": "{{issueProvider<PERSON>ame}}: O pedido falhou devido a um erro de rede do lado do cliente", "ERR_NOT_CONFIGURED": "{{issueProviderName}}: Não configurado corretamente", "IMPORTED_MULTIPLE_ISSUES": "{{issueProviderName}}: Importado {{nr}} novo {{issuesStr}} para o backlog", "IMPORTED_SINGLE_ISSUE": "{{issueProviderName}}: Importado {{issueStr}} \"{{issueTitle}}\" para o backlog", "ISSUE_DELETED_OR_CLOSED": "{{issueProviderName}}: {{issueStrC}} \"{{issueTitle}}\" parece ter sido excluído ou fechado", "ISSUE_NO_UPDATE_REQUIRED": "{{issueProviderName}}: Nenhuma atualização necessária", "ISSUE_UPDATE_MULTIPLE": "{{issueProviderName}}: Dados atualizados para {{nr}} {{issuesStr}}", "ISSUE_UPDATE_SINGLE": "{{issueProviderName}}: Dados atualizados para \"{{issueTitle}}\"", "MANUAL_UPDATE_ISSUE_SUCCESS": "{{issueProviderName}}: Dados atualizados para \"{{issueTitle}}\"", "MISSING_ISSUE_DATA": "{{issueProviderName}}: <PERSON><PERSON><PERSON>s sem dados {{issueStr}} encontrados. Recarregando.", "NEW_COMMENT": "{{issueProviderName}}: <PERSON>o comentá<PERSON> para \"{{issueTitle}}\"", "POLLING_BACKLOG": "{{issueProviderName}}: Sondagem para novo {{issuesStr}}", "POLLING_CHANGES": "{{issueProviderName}}: Alterações de sondagem para {{issuesStr}}"}}, "JIRA": {"BANNER": {"BLOCK_ACCESS_MSG": "Jira: A fim de impedir o desligamento da API, o acesso foi bloqueado pelo Super Productivity. Você provavelmente deve verificar suas configurações do jira!", "BLOCK_ACCESS_UNBLOCK": "Desb<PERSON>que<PERSON>"}, "CFG_CMP": {"ALWAYS_ASK": "Sempre abrir dialogo", "DO_NOT": "Não faça a transição", "DONE": "Status para conclusão da tarefa", "ENABLE": "Ativar integração com Jira", "ENABLE_TRANSITIONS": "Ativar tratamento de transição", "IN_PROGRESS": "Status para iniciar a tarefa", "LOAD_SUGGESTIONS": "<PERSON>egar su<PERSON>", "MAP_CUSTOM_FIELDS": "Mapear campos personalizados", "MAP_CUSTOM_FIELDS_INFO": "Infelizmente, alguns dados do Jira são salvos em campos personalizados que são diferentes para cada instalação. Se você deseja incluir esses dados, precisa selecionar o campo personalizado apropriado para eles.", "OPEN": "Status para Pausar Tarefa", "SELECT_ISSUE_FOR_TRANSITIONS": "Selecione uma issue para carregar as transições disponíveis", "STORY_POINTS": "Pontos de estória", "TRANSITION": "Ativar transição"}, "DIALOG_CONFIRM_ASSIGNMENT": {"MSG": "<strong>{{summary}}</strong> está atualmente atribuído a <strong>{{assignee}}</strong>. Deseja atribuir a si mesmo?", "OK": "Fazer isso!"}, "DIALOG_INITIAL": {"TITLE": "Configurar Jira para o Projeto"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "Escolha o status para atribuir", "CURRENT_ASSIGNEE": "Responsável atual:", "CURRENT_STATUS": "Status atual:", "TASK_NAME": "Nome da Tarefa:", "TITLE": "Jira: Atualizar status", "UPDATE_STATUS": "Atualizar status"}, "DIALOG_WORKLOG": {"CHECKBOXES": {"ALL_TIME": "Sempre use todo o tempo gasto na tarefa como padrão", "ALL_TIME_MINUS_LOGGED": "Sempre use apenas o tempo gasto menos o tempo registrado como padrão", "TIME_SPENT_TODAY": "Sempre use apenas o tempo gasto hoje como padrão", "TIME_SPENT_YESTERDAY": "Sempre use apenas o tempo gasto ontem como padrão"}, "CURRENTLY_LOGGED": "Hora atualmente registrada:", "INVALID_DATE": "O valor digitado não é uma data!", "SAVE_WORKLOG": "<PERSON><PERSON> log de trabalho", "STARTED": "Iniciado", "SUBMIT_WORKLOG_FOR": "Envie um log de trabalho para Jira para", "TIME_SPENT": "Tempo gasto", "TIME_SPENT_TOOLTIP": "Adicionar hor<PERSON> diferentes", "TITLE": "Jira: Enviar log de trabalho"}, "FORM_ADV": {"AUTO_ADD_BACKLOG_JQL_QUERY": "JQL usado para adicionar tarefas automaticamente ao backlog", "IS_ADD_WORKLOG_ON_SUB_TASK_DONE": "Abrir caixa de diálogo para enviar o log de trabalho a jira quando a subtarefa for concluída", "IS_CHECK_TO_RE_ASSIGN_TICKET_ON_TASK_START": "Verifique se o problema atualmente trabalhado está atribuído ao usuário atual", "IS_WORKLOG_ENABLED": "Abrir caixa de diálogo para enviar o log de trabalho a Jira quando a tarefa estiver concluída", "SEARCH_JQL_QUERY": "Consulta JQL para limitar as tarefas do pesquisador", "WORKLOG_DEFAULT_ALL_TIME": "Preencha todo o tempo gasto na tarefa", "WORKLOG_DEFAULT_ALL_TIME_MINUS_LOGGED": "Preencha todo o tempo gasto menos o tempo registrado", "WORKLOG_DEFAULT_TIME_MODE": "Valor de tempo padrão para a caixa de diálogo", "WORKLOG_DEFAULT_TODAY": "Preencha apenas o tempo gasto hoje", "WORKLOG_DEFAULT_YESTERDAY": "Preencha apenas o tempo gasto ontem"}, "FORM_CRED": {"ALLOW_SELF_SIGNED": "Permitir certificado autoassinado", "HOST": "Host (e.g.: http://my-host.de:1234)", "PASSWORD": "Token / Senha", "USE_PAT": "Use token de acesso pessoal em vez de senha", "USER_NAME": "Email / Usuário", "WONKY_COOKIE_MODE": "Autenticação de reserva de cookie Wonky (somente aplicativo de desktop)"}, "FORM_SECTION": {"ADV_CFG": "Configurações avançadas", "HELP_ARR": {"H1": "Configurações Básicas", "H2": "Configurações do log de trabalho", "H3": "Transições padrão", "P1_1": "Forneça um nome de login (pode ser encontrado na sua página de perfil) e um <a target=\"_blank\" href=\"https://confluence.atlassian.com/cloud/api-tokens-938839638.html\" target=\"_blank\">API token</a> ou senha, se você não puder gerar uma por algum motivo. Às vezes, as versões mais recentes do Jira funcionam apenas com o token. ", "P1_2": "Você também precisa especificar uma consulta JQL que é usada para as sugestões para adicionar tarefas do Jira. Se precisar de ajuda, confira este link <a target=\"_blank\" href=\"https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html\" target=\"_blank\">https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html</a>.", "P1_3": "Você também pode configurar, se desejar automaticamente (por exemplo, sempre que visitar a exibição de planejamento), adicionar to<PERSON> as novas tarefas especificadas por uma consulta JQL customizada ao backlog.", "P1_4": "Outra opção é \"Verifique se o ticket atual está atribuído ao usuário atual\". Se ativado e você estiver começando, será feita uma verificação se você estiver atualmente atribuído a esse ticket no Jira, se não aparecer uma caixa de diálogo na qual você pode optar por atribuir o ticket a si mesmo.", "P2_1": "Existem várias opções para determinar quando e como você deseja enviar um log de trabalho. Ativar <em>'Abrir caixa de diálogo do log de trabalho para adicionar um log de trabalho ao Jira quando a tarefa estiver concluída'</em> abre uma caixa de diálogo para adicionar um log de trabalho toda vez que você marcar uma tarefa do Jira como concluída. Portanto, lembre-se de que os logs de trabalho serão adicionados além de tudo rastreado até o momento. Portanto, se você marcar uma tarefa como concluída pela segunda vez, talvez não queira enviar o tempo de trabalho completo para a tarefa novamente.", "P2_2": "<em>'Abre a caixa de diálogo do log de trabalho quando a subtarefa é concluída e não para tarefas com as próprias subtarefas'</em> abre uma caixa de diálogo do log de trabalho toda vez que você marca uma subtarefa de um problema do Jira como concluída. Você já acompanha seu tempo através das subtarefas, nenhuma caixa de diálogo é aberta depois que você marca a tarefa Jira como concluída.", "P2_3": "<em>'Enviar atualizações para o log de trabalho automaticamente sem caixa de diálogo'</em> faz o que diz. Como marcar uma tarefa como executada várias vezes faz com que todo o tempo de trabalho seja rastreado duas vezes, isso não é recomendado.", "P3_1": "Aqui você pode reconfigurar suas transições padrão. O Jira permite uma ampla configuração de transições que geralmente entram em ação como colunas diferentes no seu painel ágil <PERSON>. Não podemos fazer suposições sobre onde e quando fazer a transição de suas tarefas e você precisa defina-o manualmente"}}, "ISSUE_CONTENT": {"ASSIGNEE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AT": "em", "ATTACHMENTS": "Anexos", "CHANGED": "Alterado", "COMMENTS": "Comentarios", "COMPONENTS": "Componentes", "DESCRIPTION": "Descrição", "LIST_OF_CHANGES": "Lista de mudanças", "MARK_AS_CHECKED": "Marcar atualizações como checadas", "ON": "em", "RELATED": "Relacionado", "STATUS": "Status", "STORY_POINTS": "Story Points", "SUB_TASKS": "Sub Tarefas", "SUMMARY": "Indíce", "WORKLOG": "Log de <PERSON>ho", "WRITE_A_COMMENT": "Comentar"}, "S": {"ADDED_WORKLOG_FOR": "Jira: log de trabalho adicionado para {{issueKey}}", "EXTENSION_NOT_LOADED": "Extensão de superprodutividade não carregada. Atualizar a página pode ajudar", "INSUFFICIENT_SETTINGS": "Configurações insuficientes fornecidas para Jira", "INVALID_RESPONSE": "Jira: A resposta continha dados inválidos", "ISSUE_NO_UPDATE_REQUIRED": "<PERSON><PERSON>: \"{{issueText}}\" já está atualizado", "MANUAL_UPDATE_ISSUE_SUCCESS": "Jira: dados atualizados para \"{{issueText}}\" ", "MISSING_ISSUE_DATA": "Jira: Foram encontradas tarefas com dados de problemas ausentes. Recarregando.", "NO_AUTO_IMPORT_JQL": "Jira: Nenhuma consulta de pesquisa definida para importação automática", "NO_VALID_TRANSITION": "Jira: nenhuma transição válida configurada", "TIMED_OUT": "Jira: solicitação expirada", "TRANSITION": "Jira: defina o problema \"{{issue<PERSON>ey}}\" como \"{{name}}\" ", "TRANSITION_SUCCESS": "Jira: defina o problema {{issue<PERSON><PERSON>}} como <strong> {{selectedTransition}} </strong>", "TRANSITIONS_LOADED": "Jira: Transições carregadas. Use as opções abaixo para atribuí-las", "UNABLE_TO_REASSIGN": "Jira: Não foi possível reatribuir o ticket a si mesmo, porque você não especificou um nome de usuário. Visite as configuraç<PERSON><PERSON>."}, "STEPPER": {"CREDENTIALS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DONE": "Agora você terminou.", "LOGIN_SUCCESS": "Login efetuado com sucesso!", "TEST_CREDENTIALS": "Testar credenciais", "WELCOME_USER": "Bem-vindo {{user}}!"}}, "MARKDOWN_PASTE": {"CONFIRM_ADD_TO_SUB_TASK_NOTES": "Adicionar a lista de markdown colada às notas da subtarefa \"{{parentTaskTitle}}\"?", "CONFIRM_PARENT_TASKS": "<PERSON><PERSON>r <strong>{{tasksCount}} novas tarefas</strong> a partir da lista de markdown colada?", "CONFIRM_PARENT_TASKS_WITH_SUBS": "Criar <strong>{{tasksCount}} novas tarefas e {{subTasksCount}} subtarefas</strong> a partir da lista de markdown colada?", "CONFIRM_SUB_TASKS": "Criar {{tasksCount}} novas subtarefas a partir da lista de markdown colada?", "CONFIRM_SUB_TASKS_WITH_PARENT": "C<PERSON>r <strong>{{tasksCount}} novas subtarefas sob \"{{parentTaskTitle}}\"</strong> a partir da lista de markdown colada?", "DIALOG_TITLE": "Lista de Markdown colada detectada!"}, "METRIC": {"BANNER": {"CHECK": "Consegui!"}, "CMP": {"AVG_BREAKS_PER_DAY": "Intervalos médios por dia", "AVG_TASKS_PER_DAY_WORKED": "<PERSON><PERSON><PERSON> de tarefas por dia trabalhadas", "AVG_TIME_SPENT_ON_BREAKS": "Tempo médio gasto em pausas", "AVG_TIME_SPENT_PER_DAY": "Tempo médio gasto por dia", "AVG_TIME_SPENT_PER_TASK": "Tempo médio gasto por tarefa", "COUNTING_SUBTASKS": "(contagem de subtarefas)", "DAYS_WORKED": "<PERSON><PERSON> t<PERSON>", "GLOBAL_METRICS": "Global Metrics", "IMPROVEMENT_SELECTION_COUNT": "Número de vezes que um fator de melhoria foi selecionado", "MOOD_PRODUCTIVITY_OVER_TIME": "Humor e produtividade ao longo do tempo", "NO_ADDITIONAL_DATA_YET": "Ainda não foram coletados dados adicionais. Use o formulário no painel Resumo diário \"Avaliação\" para fazer isso.", "OBSTRUCTION_SELECTION_COUNT": "Número de vezes que um fator obstrutivo foi selecionado", "SIMPLE_CLICK_COUNTERS_OVER_TIME": "Contadores de cliques ao longo do tempo", "SIMPLE_COUNTERS": "Contadores simples", "SIMPLE_STOPWATCH_COUNTERS_OVER_TIME": "Contadores de cronômetro ao longo do tempo", "TASKS_DONE_CREATED": "<PERSON><PERSON><PERSON><PERSON> (concluídas / criadas)", "TIME_ESTIMATED": "Tempo estimado", "TIME_SPENT": "Tempo gasto"}, "EVAL_FORM": {"ADD_NOTE_FOR_TOMORROW": "Adicionar nota para amanhã", "DISABLE_REPEAT_EVERY_DAY": "Desativar repetição todos os dias", "ENABLE_REPEAT_EVERY_DAY": "<PERSON><PERSON> todos os dias", "HELP_H1": "Por que devo me importar?", "HELP_LINK_TXT": "Vá para a seção de métricas", "HELP_P1": "Tempo para uma pequena auto-avaliação! Suas respostas aqui são salvas e fornecem um pouco de estatísticas sobre como você trabalha na seção de métricas. Além disso, as sugestões para amanhã aparecerão acima da sua lista de tarefas no dia seguinte.", "HELP_P2": "O objetivo é ser menos sobre calcular as métricas exatas ou tornar-se eficiente como uma máquina em tudo o que você faz, isso é sobre melhorar a maneira como você se sente em relação ao seu trabalho. Pode ser útil avaliar pontos de dificuldades em sua rotina diária, assim como encontrar fatores que o ajudem. Ser um pouco sistemático sobre o assunto ajuda a entender melhor essas questões e melhorar o que você pode.", "IMPROVEMENTS": "O que melhorou sua produtividade?", "IMPROVEMENTS_TOMORROW": "O que você poderia fazer para melhorar amanhã?", "MOOD": "Como você se sente?", "MOOD_HINT": "1: <PERSON><PERSON><PERSON><PERSON> - 10: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NOTES": "Notas para amanhã", "OBSTRUCTIONS": "O que prejudicou sua produtividade?", "PRODUCTIVITY": "Quão eficiente você trabalhou?", "PRODUCTIVITY_HINT": "1: nem <PERSON><PERSON> - 10: enorme efici<PERSON>ncia"}, "S": {"SAVE_METRIC": "Métrica salva com sucesso"}}, "NOTE": {"D_ADD": {"NOTE_LABEL": "Digite algum texto para salvar como nota ..."}, "D_FULLSCREEN": {"VIEW_PARSED": "Ver como marcação analisada (não editável)", "VIEW_SPLIT": "Ver markdown analisado e não analisado em visualização dividida", "VIEW_TEXT_ONLY": "Ver como texto não analisado"}, "NOTE_CMP": {"DISABLE_PARSE": "Desativar análise de remarcação", "ENABLE_PARSE": "Ativar análise de remarcação"}, "NOTES_CMP": {"ADD_BTN": "Adicionar nova nota", "DROP_TO_ADD": "Solte aqui para adicionar nova nota", "NO_NOTES": "Atualmente não há notas"}, "S": {"NOTE_ADDED": "Nota guardada"}}, "OPEN_PROJECT": {"CFG_CMP": {"ALWAYS_ASK": "Sempre abrir diálogo", "DO_NOT": "Não faça a transição", "DONE": "Status para conclusão da tarefa", "ENABLE": "Ativar integração com OpenProject", "ENABLE_TRANSITIONS": "Ativar tratamento de transição", "IN_PROGRESS": "Status para iniciar a tarefa", "OPEN": "Status para pausar tarefa", "PROGRESS_ON_SAVE": "Progresso padrão ao salvar", "SELECT_ISSUE_FOR_TRANSITIONS": "Selecione uma tarefa para carregar as transições disponíveis", "TRANSITION": "Ativar transição"}, "DIALOG_INITIAL": {"TITLE": "Configurar OpenProject para Projeto"}, "DIALOG_TRACK_TIME": {"ACTIVITY": "atividade", "CURRENTLY_LOGGED": "Tempo atualmente registrado:", "INVALID_DATE": "O valor inserido não é uma data!", "POST_TIME": "Post Time", "STARTED": "Começado", "SUBMIT_TIME_FOR": "Enviar tempo para OpenProject para", "TIME_SPENT": "Tempo gasto", "TITLE": "OpenProject: enviar log de trabalho"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "Escolha o status para atribuir", "CURRENT_ASSIGNEE": "Responsável atual:", "CURRENT_STATUS": "Estado atual:", "PERCENTAGE_DONE": "Progresso:", "TASK_NAME": "Nome da tarefa:", "TITLE": "OpenProject: Atualizar estado", "UPDATE_STATUS": "Atualizar estado"}, "FORM": {"FILTER_USER": "Nome de usuário (por exemplo, para filtrar as alterações você mesmo)", "HOST": "Host (por exemplo: https://www.openproject.org/)", "IS_SHOW_TIME_TRACKING_DIALOG": "Mostrar caixa de diálogo de rastreamento de tempo para relatar ao OpenProject", "IS_SHOW_TIME_TRACKING_DIALOG_DESCRIPTION": "Requer que o módulo de controle de tempo seja habilitado para o projeto OpenProject", "IS_SHOW_TIME_TRACKING_DIALOG_FOR_EACH_SUB_TASK": "Mostrar caixa de diálogo de controle de tempo quando as subtarefas forem concluídas", "PROJECT_ID": "ID do projeto", "PROJECT_ID_DESCRIPTION": "Pode ser encontrado como parte da url, ao visualizar o projeto no navegador.", "SCOPE": "Escopo", "SCOPE_ALL": "Todos", "SCOPE_ASSIGNED": "Atribuído a mim", "SCOPE_CREATED": "Criado por mim", "TOKEN": "<PERSON><PERSON>"}, "FORM_SECTION": {"HELP": "<p>Aqui você pode configurar SuperProductivity para listar pacotes de trabalho OpenProject abertos. Observe que, para que isso funcione no navegador, você provavelmente precisará configurar o CORS para o servidor OpenProject, para permitir o acesso de app.super-productivity.com</p>", "TITLE": "Projeto a<PERSON>"}, "ISSUE_CONTENT": {"ASSIGNEE": "Responsável", "ATTACHMENTS": "Anexos", "DESCRIPTION": "Descrição", "MARK_AS_CHECKED": "Marcar atualizações como marcadas", "STATUS": "Status", "SUMMARY": "Resumo", "TYPE": "Tipo", "UPLOAD_ATTACHMENT": "Carregar para a tarefa"}, "ISSUE_STRINGS": {"ISSUE_STR": "Pacote de trabalho", "ISSUES_STR": "paco<PERSON> de trabalho"}, "S": {"ERR_NO_FILE": "Nenhum ficheiro se<PERSON>", "ERR_UNKNOWN": "OpenProject: <PERSON>rro desconhecido {{statusCode}} {{errorMsg}}. O CORS está configurado corretamente para o servidor?", "POST_TIME_SUCCESS": "OpenProject: entrada de hora criada com sucesso para {{issueTitle}}", "TRANSITION": "OpenProject: <PERSON><PERSON><PERSON><PERSON> tarefa \"{{issueKey}}\" para \"{{name}}\"", "TRANSITION_SUCCESS": "OpenProject: <PERSON><PERSON><PERSON> {{issue<PERSON><PERSON>}} atualizada para <strong>{{chosenTransition}}</strong>", "TRANSITIONS_LOADED": "Transições carregadas. Utilize os campos abaixo para associá-las"}}, "PLANNER": {"D": {"ADD_PLANNED": {"ADD_TO_TODAY": "Adicionar a <PERSON>", "RE_PLAN_ALL": "Reagendar tudo", "TITLE": "Adicionar ta<PERSON> para Hoje"}}, "EDIT_REPEATED_TASK": "Editar tarefa repetida '{{taskName}}'", "NO_TASKS": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "PLAN_VIEW": {"NO_SCHEDULED_ITEMS": "Nenhum item agendado"}, "S": {"REMOVED_PLAN_DATE": "Data do plano removida para a tarefa ' {{taskTitle}}'", "TASK_ALREADY_PLANNED": "A tarefa já está planeada para {{date}}", "TASK_PLANNED_FOR": "<PERSON><PERSON><PERSON> para {{date}}"}, "TASK_DRAWER": "Gaveta de trabalho"}, "POMODORO": {"BACK_TO_WORK": "Voltar ao trabalho!", "BREAK_IS_DONE": "Seu intervalo acabou!", "ENJOY_YOURSELF": "Divirta-se, mexa-se, volte em:", "FINISH_SESSION_X": "Você terminou a sessão com sucesso <strong>{{nr}}</strong>!", "NOTIFICATION": {"BREAK_TIME": "Pomodoro: <PERSON>ra do intervalo {{nr}}!", "BREAK_X_START": "Pomodoro: <PERSON><PERSON> {{nr}} iniciada!", "NO_TASKS": "Você precisa adicionar tarefas antes que o cronômetro Pomodor<PERSON> possa iniciar.", "SESSION_X_START": "Pomodoro: <PERSON><PERSON><PERSON> {{nr}} iniciada!"}, "S": {"RESET": "Redefinir para a primeira sessão Pomodoro", "SESSION_SKIP": "Pular para o final da Sessão Pomodoro atual", "SESSION_X_START": "Pomodoro: <PERSON><PERSON><PERSON> {{nr}} iniciada!"}, "SKIP_BREAK": "Pular pausa", "START_BREAK": "<PERSON><PERSON><PERSON> break"}, "PROCRASTINATION": {"BACK_TO_WORK": "Voltar ao trabalho!", "COMP": {"INTRO": "Mostrar compaixão a si mesmo é sempre uma ótima idéia. Melhora o sentimento de valor próprio, promove emoções positivas e pode ajudá-lo a superar a procrastinação, é claro. Tente fazer um pequeno exercício:", "L1": "Sente-se um pouco e estique-se, se quiser, acalme-se um pouco", "L2": "Tente ouvir os pensamentos e sentimentos que surgem", "L3": "Você está respondendo a si mesmo de uma maneira que responderia a um amigo?", "L4": "Se a resposta for não, imagine seu amigo na sua situação. O que você diria a eles? O que você faria por eles?", "OUTRO": "<PERSON><PERSON> ex<PERSON> <a target=\"_blank\" href=\"https://drsoph.com/blog/2018/9/17/3-exercises-in-self-compassion\" target=\"_blank\">can be found here</a> or on <a target=\"_blank\" href=\"https://www.google.com/search?q=self+compassion+exercises&oq=self+compassion+excers&aqs=chrome.1.69i57j0l5.4303j0j7&sourceid=chrome&ie=UTF-8\" target=\"_blank\">google</a>.", "TITLE": "Auto-compaixão"}, "CUR": {"INTRO": "A procrastinação é interessante, não é? Não faz sentido fazê-lo. Não é do seu interesse a longo prazo. Mas ainda todo mundo faz. Aprecie e explore!", "L1": "Que sentimentos estão provocando sua tentação de procrastinar?", "L2": "Onde você os sente em seu corpo?", "L3": "O que eles lembram?", "L4": "O que acontece com o pensamento de procrastinar enquanto você o observa? Ele se intensifica? Dissipa? Faz com que surjam outras emoções?", "L5": "Como as sensações em seu corpo estão mudando à medida que você repousa sua consciência nelas?", "PROCRASTINATION_TRIGGERS_TEXT": "Outro método muito eficaz é registrar o que desencadeou sua vontade de procrastinar. Por exemplo, muitas vezes tenho vontade de ir rapidamente para o Reddit ou para o meu site de notícias favorito sempre que a janela do meu navegador entra em foco. Desde que comecei a escrever meus gatilhos em um simples documento de texto vazio, percebi como esse padrão estava arraigado e isso me ajudou a experimentar diferentes contra-medidas.", "PROCRASTINATION_TRIGGERS_TITLE": "Anotando seus gatilhos de procrastinação", "TITLE": "Curiosidade"}, "H1": "Dê uma folga!", "INTRO": {"AVOIDING": "<PERSON><PERSON><PERSON> da tare<PERSON>", "FEAR": "<PERSON>do de falhar", "STRESSED": "Estressado por não conseguir concluir as tarefas", "TITLE": "Introdução"}, "P1": "Antes de tudo, relaxe! Todo mundo faz isso de vez em quando. E se você não estiver fazendo o que de<PERSON>ia, pelo menos aproveite! Em seguida, verifique as seções abaixo para obter algo útil.", "P2": "Lembre-se: a procrastinação é um problema de regulação emocional, não um problema de gerenciamento de tempo.", "REFRAME": {"INTRO": "Pense no que pode ser positivo sobre a tarefa.", "TITLE": "Reenquadrando", "TL1": "O que pode ser interessante sobre isso?", "TL2": "O que você ganha se você completá-lo?", "TL3": "Como você se sentirá sobre isso se concluir o processo?"}, "SPLIT_UP": {"INTRO": "Divida a tarefa em tantos pequenos pedaços quanto possível.", "OUTRO": "Concluído? Então pense sobre isso. Qual seria -estritamente teórica- a primeira coisa que você faria se você começasse a trabalhar na tarefa? Pense nisso ...", "TITLE": "Divida!"}}, "PROJECT": {"D_CREATE": {"CREATE": "<PERSON><PERSON>r <PERSON>", "EDIT": "<PERSON><PERSON>", "SETUP_CALDAV": "Configurar integração com Caldav", "SETUP_GIT": "Configurar integração com GitHub ", "SETUP_GITEA_PROJECT": "Configurar integração com o Gitea", "SETUP_GITLAB": "Configurar integração com GitLab ", "SETUP_JIRA": "Configurar integração com Jira ", "SETUP_OPEN_PROJECT": "Configurar integração com OpenProject", "SETUP_REDMINE_PROJECT": "Configurar Integração com Redmine"}, "D_DELETE": {"MSG": "Tem a certeza de que pretende apagar o projeto \"{{title}}\"?"}, "FORM_BASIC": {"L_ENABLE_BACKLOG": "Habilitar Backlog do Projeto", "L_IS_HIDDEN_FROM_MENU": "Ocultar projeto do menu", "L_TITLE": "Nome do Projeto", "TITLE": "Configurações básicas"}, "FORM_THEME": {"D_IS_DARK_THEME": "Não será usado se o sistema suportar o modo escuro global.", "HELP": "Configurações do tema para o seu projeto.", "L_BACKGROUND_IMAGE_DARK": "URL da imagem de fundo (tema escuro)", "L_BACKGROUND_IMAGE_LIGHT": "URL da imagem de fundo (tema claro)", "L_COLOR_ACCENT": "<PERSON>r <PERSON>", "L_COLOR_PRIMARY": "Cor principal", "L_COLOR_WARN": "Cor de aviso / erro", "L_HUE_ACCENT": "Limite para texto escuro em fundo de cor de destaque", "L_HUE_PRIMARY": "Limite para texto escuro em fundo de cor primária", "L_HUE_WARN": "Limite para texto escuro em aviso de cor de fundo", "L_IS_AUTO_CONTRAST": "Definir cores de texto automaticamente para melhor legibilidade", "L_IS_DISABLE_BACKGROUND_GRADIENT": "Desativar gradiente de fundo colorido", "L_IS_REDUCED_THEME": "Usar UI reduzida (sem caixas nas tarefas)", "L_THEME_COLOR": "Cor do tema", "L_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON>"}, "S": {"ARCHIVED": "Projeto Arquivado", "CREATED": "<PERSON><PERSON>o criado <strong> {{title}} </strong>. Você pode selecioná-lo no menu no canto superior esquerdo.", "DELETED": "Projeto excluído", "E_EXISTS": "Projeto \"{{title}}\" já existe", "E_INVALID_FILE": "Dados inválidos para o arquivo de projeto", "ISSUE_PROVIDER_UPDATED": "Configurações atualizadas do projeto para <strong> {{issueProviderKey}} </strong>", "UNARCHIVED": "Projeto não arquivado", "UPDATED": "Configurações atualizadas do projeto"}}, "QUICK_HISTORY": {"NO_DATA": "Sem dados para o ano atual", "PAGE_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "WEEK_TITLE": "Semana {{nr}} ({{timeSpent}})"}, "REDMINE": {"DIALOG_INITIAL": {"TITLE": "Configurar Redmine para projeto"}, "FORM": {"API_KEY": "Chave de acesso API", "HOST": "Hospedar (e.g.: https://redmine.org)", "PROJECT_ID": "Identificador do projeto", "PROJECT_ID_DESCRIPTION": "Pode ser encontrado como parte da url, ao visualizar o projeto no Navegador.", "SCOPE": "Escopo", "SCOPE_ALL": "Todos", "SCOPE_ASSIGNED": "Definido para mim", "SCOPE_CREATED": "Criado por mim"}, "FORM_SECTION": {"HELP": "<p>Aqui você pode configurar o SuperProductivity para listar problemas abertos do Redmine (seja a versão online ou uma instância auto-hospedada) para um projeto específico no painel de criação de tarefas na visualização de planejamento diário. Eles serão listados como sugestões e fornecerão um link para o problema, bem como mais informações sobre ele.</p><p>Além disso, você pode importar automaticamente todos os problemas em aberto.</p>", "TITLE": "Redmine"}, "ISSUE_CONTENT": {"AUTHOR": "Autor", "DESCRIPTION": "Descrição", "MARK_AS_CHECKED": "Marcar atualizações como checadas", "PRIORITY": "Prioridade", "STATUS": "Status"}, "S": {"ERR_UNKNOWN": "Redmine: <PERSON><PERSON> desconhecido {{statusCode}} {{errorMsg}}."}}, "REMINDER": {"COUNTDOWN_BANNER": {"HIDE": "Esconder", "START_NOW": "Iniciar agora", "TXT": "<strong>{{title}}</strong> começa em <strong>{{start}}</strong> !", "TXT_MULTIPLE": "<strong>{{title}}</strong> começa em <strong>{{start}}</strong> !<br> (e {{nrOfOtherBanners}} outras tarefas estão pendentes)"}, "S_ACTIVE_TASK_DUE": "A tarefa em que está a trabalhar atualmente já tem prazo de validade!<br/> ({{title}})", "S_REMINDER_ERR": "Erro na interface do lembrete"}, "SCHEDULE": {"CONTINUED": "<PERSON><PERSON><PERSON><PERSON>", "D_INITIAL": {"TEXT": "<p>A ideia da linha do tempo é fornecer uma imagem melhor de como as tarefas planejadas de alguém funcionam ao longo do tempo. Ele é gerado automaticamente a partir de suas tarefas e distingue duas coisas diferentes: <strong><PERSON><PERSON><PERSON><PERSON> agendadas</strong>, que são mostradas no tempo planejado e <strong>Tarefas regulares</strong> que devem fluir em torno desses eventos fixos. <PERSON>das as tarefas consideram as estimativas de tempo que você atribuiu a elas.</p><p>Além disso, você também pode fornecer os horários de início e término do trabalho. Se configuradas, as tarefas regulares nunca aparecerão fora delas. Observe que o cronograma inclui apenas os próximos 30 dias.</p>", "TITLE": "Schedule"}, "END": "Fim do trabalho", "LUNCH_BREAK": "Intervalo para o almoço", "NO_TASKS": "Atualmente não há tarefas. Adicione algumas tarefas através do botão + na barra superior.", "NOW": "agora", "PLAN_END_DAY": "Planeje no final do dia", "PLAN_START_DAY": "Planeje o início do dia", "START": "Início do Trabalho", "TASK_PROJECTION_INFO": "Projeção futura de uma tarefa repetível programada"}, "SEARCH_BAR": {"INFO": "Clique no ícone da lista para procurar tarefas arquivadas", "INFO_ARCHIVED": "Clique no ícone de arquivo para procurar tarefas normais", "NO_RESULTS": "Nenhuma tarefa encontrada correspondente à sua pesquisa", "PLACEHOLDER": "Pesquisa por tarefa ou descrição de tarefa", "PLACEHOLDER_ARCHIVED": "<PERSON>es<PERSON><PERSON> tare<PERSON> arqui<PERSON>", "TOO_MANY_RESULTS": "Mu<PERSON>s resultados, por favor restrinja sua pesquisa"}, "SIMPLE_COUNTER": {"D_CONFIRM_REMOVE": {"MSG": "A exclusão de um contador simples também excluirá todos os dados antigos rastreados nele. Tem certeza de que deseja continuar?", "OK": "Faça!"}, "D_EDIT": {"CURRENT_STREAK": "<PERSON><PERSON><PERSON><PERSON><PERSON> atual", "DAILY_GOAL": "Objetivo diário", "DAYS": "<PERSON><PERSON>", "L_COUNTER": "Contagem"}, "FORM": {"ADD_NEW": "Adicionar contador simples", "HELP": "Aqui você pode configurar botões simples que aparecerão no canto superior direito. Eles podem ser temporizadores ou apenas um contador simples, que é contado, clicando nele.", "L_COUNTDOWN_DURATION": "Duração da contagem decrescente", "L_DAILY_GOAL": "Objetivo diário para uma série de sucesso", "L_ICON": "Ícone", "L_ICON_ON": "Ícone quando alternado", "L_IS_ENABLED": "<PERSON><PERSON>do", "L_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "L_TRACK_STREAKS": "<PERSON><PERSON><PERSON><PERSON> raias", "L_TYPE": "Tipo", "L_WEEKDAYS": "Dias da semana para verificar a existência de raias", "TITLE": "Contadores simples", "TYPE_CLICK_COUNTER": "Contador de cliques", "TYPE_REPEATED_COUNTDOWN": "Contagem decrescente repetida", "TYPE_STOPWATCH": "<PERSON><PERSON><PERSON><PERSON>"}, "S": {"GOAL_REACHED_1": "Atingiu o seu objetivo para hoje!", "GOAL_REACHED_2": "Duração atual da série:"}}, "SYNC": {"A": {"ARCHIVE_ONLY_UPLOADED": "Seus dados foram carregados apenas parcialmente. Tente novamente mais tarde! <PERSON><PERSON><PERSON> contr<PERSON>, você não poderá sincronizar seus dados com outros dispositivos.", "POSSIBLE_LEGACY_DATA": "O Super Productivity melhorou a sincronização usando agora dois arquivos separados em vez de um único, permitindo muito menos transferência de dados. É recomendado atualizar todas as instâncias do Super Productivity e primeiro sincronizar os dados da instância do aplicativo onde os dados são mais recentes. Se esses forem os dados do seu dispositivo local, ignore este aviso e prossiga para carregá-los confirmando o próximo diálogo.", "REMOTE_MODEL_VERSION_NEWER": "A versão do modelo remoto é mais recente que a local. Atualize seu aplicativo local para a versão mais recente!"}, "C": {"EMPTY_SYNC": "Você está tentando sincronizar um objeto de dados vazio. Esta é a sua primeira sincronização de um aplicativo (quase) virgem?", "FORCE_UPLOAD": "Enviar dados locais mesmo assim?", "FORCE_UPLOAD_AFTER_ERROR": "Ocorreu um erro ao enviar seus dados locais. Tentar forçar a atualização?", "MIGRATE_LEGACY": "Dados legados detectados durante a importação, deseja tentar migrá-los?", "NO_REMOTE_DATA": "Nenhum dado remoto encontrado. Carregar local para remoto?", "TRY_LOAD_REMOTE_AGAIN": "Tentar recarregar os dados remotamente mais uma vez?", "UNABLE_TO_LOAD_REMOTE_DATA": "Não é possível carregar dados do remoto. Você quer tentar sobrescrever os dados remotos com seus dados locais? Todos os dados remotos serão perdidos no processo."}, "D_AUTH_CODE": {"FOLLOW_LINK": "Abra o link a seguir e copie o código de autenticação fornecido no campo de entrada abaixo.", "GET_AUTH_CODE": "Obter Código de Autorização", "L_AUTH_CODE": "Digite o código de autenticação", "TITLE": "Login: {{provider}}"}, "D_CONFLICT": {"LAMPORT_CLOCK": "<PERSON><PERSON><PERSON>", "LAST_CHANGE": "Última mudança:", "LAST_SYNC": "Última sincronização:", "LOCAL": "Local", "LOCAL_REMOTE": "Local -> <PERSON><PERSON>", "REMOTE": "<PERSON><PERSON>", "TEXT": "<p>Atualização do Dropbox. Os dados locais e remotos parecem ter sido modificados.</p>", "TIMESTAMP": "Carimbo de data/hora", "TITLE": "Dropbox: dados conflitantes", "USE_LOCAL": "Use local", "USE_REMOTE": "Usar controle remoto"}, "D_DECRYPT_ERROR": {"BTN_OVER_WRITE_REMOTE": "Alterar e substituir remoto", "CHANGE_PW_AND_DECRYPT": "Alterar e Tentar Descriptografar", "P1": "Seus dados são criptografados e a descriptografia falhou. Digite a senha correta!", "P2": "Você também pode alterar sua senha, que substituirá todos os dados remotos.", "PASSWORD": "Palavra-passe"}, "D_INCOMPLETE_SYNC": {"BTN_CLOSE_APP": "<PERSON><PERSON>r <PERSON>ti<PERSON>", "BTN_DOWNLOAD_BACKUP": "Baixar Cópia local", "BTN_FORCE_UPLOAD": "Forçar upload local", "P1": "Os dados de sincronização remota são incoerentes!", "P2": "<PERSON><PERSON>:", "P3": "Você tem 2 opções:", "P4": "1. Vá para o seu outro dispositivo e tente fazer uma sincronização completa lá.", "P5": "2. Substitua os dados remotos pelos dados locais. <PERSON><PERSON> as alterações remotas serão perdidas!", "P6": "Recomenda-se criar um backup dos dados que você substitui!!", "T1": "A última atualização foi incompleta!", "T2": "Seus dados de arquivo não foram carregados corretamente durante a última sincronização:", "T3": "Você tem 2 opções:", "T4": "1. Vá para o outro dispositivo e conclua a sincronização lá.", "T5": "2. Ou você pode sobrescrever os dados remotos com os locais. <PERSON>das as alterações remotas serão\nesteja perdido!", "T6": "É recomendável criar um backup dos dados que você sobrescreveu!!!"}, "D_INITIAL_CFG": {"SAVE_AND_ENABLE": "Salvar & Ativar Sincronização", "TITLE": "Configurar <PERSON>"}, "D_PERMISSION": {"DISABLE_SYNC": "Desabilitar sincronização", "PERM_FILE": "<PERSON>", "TEXT": "<p>Sua permissão de arquivo para sincronização local foi revogada.</p>", "TITLE": "Sincronização: Permissão de arquivo local negada"}, "FORM": {"DROPBOX": {"L_ACCESS_TOKEN": "Token de acesso (gerado a partir do código de autenticação)"}, "GOOGLE": {"L_SYNC_FILE_NAME": "Nome do arquivo de sincronização"}, "L_ENABLE_COMPRESSION": "Habilitar compressão de dados (envio mais rá<PERSON>)", "L_ENABLE_ENCRYPTION": "Habilitar criptografia ponta-a-ponta (experimental) - Torna seus dados inelegíveis ao provedor sincronizado", "L_ENABLE_SYNCING": "Habilitar Sincronização", "L_ENCRYPTION_NOTES": "OBSERVAÇÕES IMPORTANTES: Você precisará definir a mesma senha em seus outros dispositivos <strong>ANTES</strong> da próxima sincronização para que tudo funcione. Selecione uma senha segura. Observe também que <strong>você NÃO poderá acessar seus dados se esquecer essa senha. NÃO há recuperação possível</strong> , pois somente você tem a chave. A criptografia provavelmente será boa o suficiente para frustrar a maioria dos invasores, mas <strong>não há garantia.</strong>", "L_ENCRYPTION_PASSWORD": "Senha de criptografia (NÃO ESQUEÇA)", "L_SYNC_INTERVAL": "Intervalo de sincronização", "L_SYNC_PROVIDER": "Provedor de sincronização", "LOCAL_FILE": {"L_SYNC_FILE_PATH_PERMISSION_VALIDATION": "Necess<PERSON><PERSON> de acesso ao arquivo", "L_SYNC_FOLDER_PATH": "Sincronizar caminho de pasta"}, "TITLE": "Sincronizar", "WEB_DAV": {"CORS_INFO": "<strong>Experimental !!</strong> Para fazer isso funcionar, você precisa desabilitar ou limitar o CORS para sua instância Nextcloud, o que pode ter implicações de segurança negativas! Por favor, <a href='https://github.com/nextcloud/server/issues/3131'>consulte esta discussão</a> para obter mais informações. Use por sua conta e risco!", "L_BASE_URL": "Url Base", "L_PASSWORD": "<PERSON><PERSON>", "L_SYNC_FOLDER_PATH": "Caminho da pasta de sincronização", "L_USER_NAME": "Nome de usuário"}}, "S": {"ALREADY_IN_SYNC": "<PERSON><PERSON> sincron<PERSON>", "ALREADY_IN_SYNC_NO_LOCAL_CHANGES": "Nenhuma alteração local – Já em sincronia", "BTN_CONFIGURE": "Configurar", "BTN_FORCE_OVERWRITE": "Forçar substituição", "ERROR_DATA_IS_CURRENTLY_WRITTEN": "Dados remotos estão sendo gravados no momento", "ERROR_FALLBACK_TO_BACKUP": "Algo deu errado ao importar os dados. Retornando ao backup local.", "ERROR_INVALID_DATA": "Erro ao sincronizar. Dados inválidos", "ERROR_NO_REV": "Nenhuma revisão válida para arquivo remoto", "ERROR_UNABLE_TO_READ_REMOTE_DATA": "Erro ao sincronizar. Não é possível ler dados remotos. Talvez você tenha habilitado a criptografia e sua senha local não corresponde à usada para criptografar os dados remotos?", "IMPORTING": "Importando dados", "INCOMPLETE_CFG": "A autenticação para sincronização falhou. Por favor, verifique o seu Config!", "INITIAL_SYNC_ERROR": "A sincronização inicial falhou", "SUCCESS_DOWNLOAD": "Dados sincronizados do controle remoto", "SUCCESS_IMPORT": "Dados importados", "SUCCESS_VIA_BUTTON": "Dados sincronizados com sucesso", "UNKNOWN_ERROR": "Erro desconhecido durante a sincronização. Verifique o console.", "UPLOAD_ERROR": "Erro de upload desconhecido (configurações corretas?): {{err}}"}}, "TAG": {"D_CREATE": {"CREATE": "Criar tag", "EDIT": "Editar etiqueta"}, "D_DELETE": {"CONFIRM_MSG": "Deseja realmente excluir a tag \"{{tagName}}\"? Será removido de todas as tarefas. Isto não pode ser desfeito."}, "D_EDIT": {"ADD": "Adicione tags para \"{{title}}\"", "EDIT": "Editar tags para \"{{title}}\"", "LABEL": "Tag"}, "FORM_BASIC": {"L_COLOR": "Cor (se a cor principal indefinida do tema for usada)", "L_ICON": "Ícone", "L_TITLE": "Nome da tag", "TITLE": "Configurações básicas"}, "S": {"UPDATED": "As configurações de tag foram atualizadas"}, "TTL": {"ADD_NEW_TAG": "Adicionar nova tag"}}, "TASK": {"ADD_TASK_BAR": {"ADD_EXISTING_TASK": "Adicionar tarefa existente \"{{taskTitle}}\"", "ADD_ISSUE_TASK": "Adicione a edição nº{{issueNr}} de {{issueType}}", "ADD_TASK_TO_BOTTOM_OF_BACKLOG": "Adicionar tarefa ao final da lista de pendências", "ADD_TASK_TO_BOTTOM_OF_TODAY": "Adicionar tarefa ao final da lista", "ADD_TASK_TO_TOP_OF_BACKLOG": "Adicionar tarefa ao topo da lista de pendências", "ADD_TASK_TO_TOP_OF_TODAY": "Adicionar tarefa ao topo da lista", "CREATE_TASK": "Criar nova tarefa", "EXAMPLE": "Exemplo: \"Algum título da tarefa + nome do projeto # alguma tag # alguma outra tag 10m / 3h\"", "START": "Pressione enter mais uma vez para iniciar", "TOGGLE_ADD_TO_BACKLOG_TODAY": "Alternar adição de tarefa à lista de pendências/lista de hoje'", "TOGGLE_ADD_TOP_OR_BOTTOM": "Alternar adição de tarefa ao topo e ao final da lista"}, "ADDITIONAL_INFO": {"ADD_ATTACHMENT": "Juntar anexo", "ADD_SUB_TASK": "Adicionar subtarefa", "ATTACHMENTS": "Anexos {{nr}}", "DUE": "Programado para", "FROM_PARENT": "(do pai)", "LOCAL_ATTACHMENTS": "Anexos locais", "NOTES": "Notas", "PARENT": "<PERSON><PERSON>", "REMINDER": "Le<PERSON><PERSON>", "REPEAT": "<PERSON><PERSON>r", "SCHEDULE_TASK": "Agendar <PERSON>", "SUB_TASKS": "Subtarefas ({{nr}})", "TIME": "Tempo", "TITLE_PLACEHOLDER": "Insira um título"}, "B": {"ADD_HALF_HOUR": "Adicione meia hora", "ESTIMATE_EXCEEDED": "Estimativa de tempo excedida para \"{{title}}\""}, "CMP": {"ADD_SUB_TASK": "Adicionar subtarefa", "ADD_TO_MY_DAY": "Adicionar ao meu dia", "ADD_TO_PROJECT": "Adicionar a um projeto", "CONVERT_TO_PARENT_TASK": "Converter em tarefa pai", "DELETE": "Excluir tarefa", "DELETE_REPEAT_INSTANCE": "Excluir instância de tarefa repetida", "DROP_ATTACHMENT": "Solte aqui para anexar a \"{{title}}\"", "EDIT_SCHEDULED": "<PERSON><PERSON>", "EDIT_TAGS": "Editar etiquetas", "EDIT_TASK_TITLE": "<PERSON><PERSON>", "FOCUS_SESSION": "Iniciar se<PERSON><PERSON> de foco", "MARK_DONE": "Marcar como concluído", "MARK_UNDONE": "Marcar como desfeita", "MOVE_TO_BACKLOG": "Mover para backlog", "MOVE_TO_OTHER_PROJECT": "Mover para outro projeto", "MOVE_TO_REGULAR": "Mover para a lista de hoje", "MOVE_TO_TOP": "Mover para o topo da lista", "OPEN_ATTACH": "Anexar arquivo ou link", "OPEN_ISSUE": "Abrir issue em uma nova guia do navegador", "OPEN_TIME": "Tempo estimado / adicionar tempo gasto", "REMOVE_FROM_MY_DAY": "Remover do meu dia", "REPEAT_EDIT": "Editar configuração da tarefa de repetição", "SCHEDULE": "Agendar tarefa", "SHOW_UPDATES": "Mostrar atualizações", "TOGGLE_ATTACHMENTS": "Mostrar/ocultar anexos", "TOGGLE_DETAIL_PANEL": "Mostrar/ocultar informações adicionais", "TOGGLE_DONE": "Marcar como concluído/desfeito", "TOGGLE_SUB_TASK_VISIBILITY": "Alternar visibilidade da subtarefa", "TOGGLE_TAGS": "Alternar tags", "TRACK_TIME": "Comece a rastrear o tempo", "TRACK_TIME_STOP": "Pausar o tempo de rastreamento", "UNSCHEDULE_TASK": "Cancelar agendamento de tarefa", "UPDATE_ISSUE_DATA": "<PERSON><PERSON><PERSON><PERSON> dados da <PERSON>"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAG": {"MSG": "Você deseja criar a nova tag {{tagsTxt}}?", "OK": "Criar Tag"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAGS": {"MSG": "Você deseja criar as novas tags {{tagsTxt}}?", "OK": "Criar Tags"}, "D_REMINDER_VIEW": {"ADD_ALL_TO_TODAY": "<PERSON><PERSON><PERSON><PERSON> tudo hoje", "ADD_TO_TODAY": "Adicione hoje", "DONE": "<PERSON><PERSON>", "DUE_TASK": "<PERSON><PERSON><PERSON> de<PERSON>", "DUE_TASKS": "<PERSON><PERSON><PERSON><PERSON>", "FOR_CURRENT": "A tarefa está vencida. Deseja começar a trabalhar nela?", "FOR_OTHER": "A tarefa está vencida. Deseja começar a trabalhar nela?", "FROM_PROJECT": "Do projeto", "FROM_TAG": "Da tag: \"{{title}}\"", "RESCHEDULE_EDIT": "<PERSON><PERSON> (reagendar)", "RESCHEDULE_UNTIL_TOMORROW": "<PERSON><PERSON> am<PERSON>", "SNOOZE": "Soneca", "SNOOZE_ALL": "Suspender tudo", "START": "Iniciar <PERSON>", "SWITCH_CONTEXT_START": "Alternar contexto e iniciar", "UNSCHEDULE": "Cancelar agendamento", "UNSCHEDULE_ALL": "Can<PERSON>ar o agendamento de tudo"}, "D_SCHEDULE_TASK": {"MOVE_TO_BACKLOG": "Mova a tarefa para backlog até que seja agendada", "QA_NEXT_MONTH": "Agenda do próximo mês", "QA_NEXT_WEEK": "Agenda da próxima semana", "QA_REMOVE_TODAY": "Remover tarefa de hoje", "QA_TODAY": "Agendar hoje", "QA_TOMORROW": "<PERSON><PERSON><PERSON><PERSON>", "REMIND_AT": "Lembre-se em", "RO_1H": "1 hora antes de começar", "RO_5M": "5 minutos antes de começa<PERSON>", "RO_10M": "10 minutos antes de começa<PERSON>", "RO_15M": "15 minutos antes de começa<PERSON>", "RO_30M": "30 minutos antes de começa<PERSON>", "RO_NEVER": "Nunca", "RO_START": "quando come<PERSON>", "SCHEDULE": "Agendar", "UNSCHEDULE": "Cancelar agendamento"}, "D_TIME": {"ADD_FOR_OTHER_DAY": "Adicione tempo gasto para outro dia", "DELETE_FOR": "Excluir entrada do dia", "ESTIMATE": "Estimativa", "TIME_SPENT": "Tempo gasto", "TIME_SPENT_ON": "Tempo gasto {{date}}", "TITLE": "Tempo gasto / estimativas"}, "D_TIME_FOR_DAY": {"ADD_ENTRY_FOR": "Adicione uma nova entrada para {{date}}", "DATE": "Data para nova entrada", "HELP": "Exemplos:<br> 30m => 30 minutos<br> 2h => 2 horas<br> 2h 30m => 2 horas e 30 minutos", "TINE_SPENT": "Tempo gasto", "TITLE": "Adicionar para o dia"}, "N": {"ESTIMATE_EXCEEDED": "Estimativa de tempo excedida!", "ESTIMATE_EXCEEDED_BODY": "Você excedeu o tempo estimado para \"{{title}}\"."}, "S": {"CANNOT_ASSIGN_PROJECT_FOR_REPEATABLE_TASK": "Não é possível atribuir projeto via sintaxe curta para tarefas repetíveis!", "CREATED_FOR_PROJECT": "Movida a tarefa \"{{taskTitle}}\" para o projeto \"{{projectTitle}}\"", "CREATED_FOR_PROJECT_ACTION": "Ir para o Projeto", "DELETED": "<PERSON><PERSON><PERSON> excluída \"{{title}}\"", "FOUND_MOVE_FROM_BACKLOG": "Tarefa <strong>{{title}}</strong> movida do backlog para a lista de tarefas de hoje", "FOUND_MOVE_FROM_OTHER_LIST": "<PERSON><PERSON><PERSON> adicionada <strong>{{title}}</strong> de <strong>{{contextTitle}}</strong> para a lista atual", "FOUND_RESTORE_FROM_ARCHIVE": "<PERSON><PERSON><PERSON> <strong>{{title}}</strong> relacionada ao problema restaurada do arquivo", "LAST_TAG_DELETION_WARNING": "Você está tentando remover a última tag de uma tarefa que não é do projeto. Isso não é permitido!", "MOVED_TO_ARCHIVE": "Tarefas {{nr}} movidas para o arquivamento", "MOVED_TO_PROJECT": "Movida a tarefa \"{{taskTitle}}\" para o projeto \"{{projectTitle}}\"", "MOVED_TO_PROJECT_ACTION": "Ir para o Projeto", "REMINDER_ADDED": "<PERSON><PERSON><PERSON><PERSON> \"{{title}}\"", "REMINDER_DELETED": "Lembrete excluído da tarefa", "REMINDER_UPDATED": "Lembrete atualizado para a tarefa \"{{title}}\"", "TASK_CREATED": "<PERSON><PERSON><PERSON> c<PERSON> \"{{title}}\""}, "SELECT_OR_CREATE": "Selecione ou crie tarefa", "SUMMARY_TABLE": {"ESTIMATE": "Estimativa", "SPENT_TODAY": "<PERSON><PERSON> hoje", "SPENT_TOTAL": "Total gasto", "TASK": "<PERSON><PERSON><PERSON>", "TOGGLE_DONE": "Desmarcar/Marcar como concluído"}}, "TASK_REPEAT": {"ADD_INFO_PANEL": {"CUSTOM": "Configurar repetição personalizada", "CUSTOM_AND_TIME": "Personalizada, {{timeStr}}", "CUSTOM_WEEKLY": "{{daysStr}}", "CUSTOM_WEEKLY_AND_TIME": "{{daysStr}}, {{timeStr}}", "DAILY": "Todos os dias", "DAILY_AND_TIME": "Todos os días, {{timeStr}}", "EVERY_X_DAILY": "Cada {{x}} dia(s)", "EVERY_X_DAILY_AND_TIME": "Cada {{x}} dia(s), {{timeStr}}", "EVERY_X_MONTHLY": "<PERSON><PERSON> {{x}} mês", "EVERY_X_MONTHLY_AND_TIME": "<PERSON>ada {{x}} mês, {{timeStr}}", "EVERY_X_YEARLY": "Cada {{x}} ano(s)", "EVERY_X_YEARLY_AND_TIME": "Cada {{x}} ano(s), {{timeStr}}", "MONDAY_TO_FRIDAY": "Seg. a Sex", "MONDAY_TO_FRIDAY_AND_TIME": "Seg. a <PERSON>, {{timeStr}}", "MONTHLY_CURRENT_DATE": "Mensalmente no dia {{dateDayStr}}", "MONTHLY_CURRENT_DATE_AND_TIME": "Mensalmente no dia {{dateDayStr}}, {{timeStr}}", "WEEKLY_CURRENT_WEEKDAY": "Semanalmente {{weekdayStr}", "WEEKLY_CURRENT_WEEKDAY_AND_TIME": "Semanalmente em {{weekdayStr}}, {{timeStr}}", "YEARLY_CURRENT_DATE": "Anualmente em {{dayAndMonthStr}}", "YEARLY_CURRENT_DATE_AND_TIME": "Anualmente no {{dayAndMonthStr}}, {{timeStr}}"}, "D_CONFIRM_MOVE_TO_PROJECT": {"MSG": "Existem {{tasksNr}} instâncias criadas para esta tarefa repetível. Pretende movê-las todas para o projeto \"{{projectName}}\"?", "OK": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as instâncias"}, "D_CONFIRM_REMOVE": {"MSG": "A remoção da configuração de repetição converterá todas as instâncias anteriores desta tarefa em apenas tarefas regulares. Tem certeza de que deseja continuar?", "OK": "Remover completamente"}, "D_CONFIRM_UPDATE_INSTANCES": {"CANCEL": "<PERSON>nte tarefas futuras", "MSG": "Há {{tasksNr}} instâncias criadas para esta tarefa repetível. Você quer atualizar todas elas com os novos padrões ou apenas tarefas futuras?", "OK": "<PERSON><PERSON><PERSON><PERSON> todas as instancias"}, "D_EDIT": {"ADD": "Adicionar configuração de repetição de tarefas", "ADVANCED_CFG": "Configuração avançada", "EDIT": "Editr configuração de repetição de tarefas", "HELP1": "Tarefas repetidas são destinadas a tarefas diárias, por exemplo: \"Organização\", \"Reunião Diária\", \"Revisão de Código\", \"Verificando e-mails\" ou tarefas semelhantes que provavelmente ocorrerão novamente e de novo.", "HELP2": "Uma vez configurada, uma tarefa repetida será recriada todos os dias selecionados abaixo assim que você abrir o seu projeto e será automaticamente marcada como concluída no final do dia. Elas serão tratadas como instâncias diferentes. Assim, você pode adicione livremente subtarefas etc.", "HELP3": "As tarefas importadas do Jira ou Issues do Git não podem ser repetidas. Todos os lembretes também serão excluídos em uma tarefa repetida.", "HELP4": "Uma observação sobre o campo do pedido: Referindo-se às tarefas repetíveis do pedido de criação. Só tem efeito para tarefas repetíveis criadas ao mesmo tempo. Um valor mais baixo significa que uma tarefa estará no topo da lista, um número menor que estará mais abaixo. Um valor maior que 0 significa que os itens são criados na parte inferior das tarefas normais.", "TAG_LABEL": "Tags para adicionar"}, "F": {"C_DAY": "<PERSON>a", "C_MONTH": "<PERSON><PERSON><PERSON>", "C_WEEK": "Se<PERSON>", "C_YEAR": "<PERSON><PERSON>", "DEFAULT_ESTIMATE": "Estimativa padrão", "FRIDAY": "Sexta-feira", "IS_ADD_TO_BOTTOM": "Mover a tarefa para o final da lista", "MONDAY": "Segunda-feira", "NOTES": "<PERSON><PERSON>", "ORDER": "Ordem", "ORDER_DESCRIPTION": "Ordem de criação de tarefas repetíveis. Afeta apenas tarefas repetíveis criadas ao mesmo tempo. Um valor mais baixo significa que uma tarefa será criada mais acima na lista, um número menor que estará mais abaixo. Um valor maior que 0 significa que os itens são criados na parte inferior das tarefas normais.", "Q_CUSTOM": "Configuração de repetição personalizada", "Q_DAILY": "Todos os dias", "Q_MONDAY_TO_FRIDAY": "De segunda a sexta", "Q_MONTHLY_CURRENT_DATE": "Todos os meses, no dia {{dateDayStr}}", "Q_WEEKLY_CURRENT_WEEKDAY": "Toda semana em {{weekdayStr}}", "Q_YEARLY_CURRENT_DATE": "Todos os anos em {{dayAndMonthStr}}", "QUICK_SETTING": "Repita a configuração", "REMIND_AT": "Lembre-se em", "REMIND_AT_PLACEHOLDER": "Sele<PERSON>e quando lembrar", "REPEAT_CYCLE": "<PERSON><PERSON><PERSON> c<PERSON>lo", "REPEAT_EVERY": "Repetir cada", "SATURDAY": "Sábado", "START_DATE": "Data de inicio", "START_TIME": "<PERSON><PERSON><PERSON><PERSON> de início <PERSON>", "START_TIME_DESCRIPTION": "Por exemplo. 15:00. De<PERSON>e em branco para uma tarefa de dia inteiro", "SUNDAY": "Domingo", "THURSDAY": "Quin<PERSON>-f<PERSON>", "TITLE": "<PERSON><PERSON><PERSON><PERSON>", "TUESDAY": "Terça-feira", "WEDNESDAY": "Quarta-feira"}}, "TASK_VIEW": {"CUSTOMIZER": {"ENTER_PROJECT": "Insira o projeto", "ENTER_TAG": "Insira a tag", "ESTIMATED_TIME": "Tempo Estimado", "FILTER_BY": "Filtrar por", "FILTER_DEFAULT": "<PERSON><PERSON>", "FILTER_ESTIMATED_TIME": "Tempo Estimado", "FILTER_PROJECT": "Projeto", "FILTER_SCHEDULED_DATE": "Data Agendada", "FILTER_TAG": "Tag", "FILTER_TIME_SPENT": "Tempo Gasto", "GROUP_BY": "Agrupar por", "GROUP_DEFAULT": "Sem Grupo", "GROUP_PROJECT": "Projeto", "GROUP_SCHEDULED_DATE": "Data Agendada", "GROUP_TAG": "Tag", "RESET_ALL": "<PERSON><PERSON><PERSON><PERSON>", "SCHEDULED_DEFAULT": "Qualquer Data", "SCHEDULED_NEXT_MONTH": "<PERSON>ró<PERSON><PERSON>", "SCHEDULED_NEXT_WEEK": "Próxima <PERSON>", "SCHEDULED_THIS_MONTH": "<PERSON><PERSON>", "SCHEDULED_THIS_WEEK": "<PERSON><PERSON>", "SCHEDULED_TODAY": "Hoje", "SCHEDULED_TOMORROW": "Amanhã", "SORT_BY": "Ordenar por", "SORT_CREATION_DATE": "Data de Criação", "SORT_DEFAULT": "Padrão", "SORT_NAME": "Nome", "SORT_SCHEDULED_DATE": "Data Agendada", "TIME_1HOUR": "> 1 Hora", "TIME_2HOUR": "> 2 Horas", "TIME_10MIN": "> 10 Minutos", "TIME_30MIN": "> 30 Minutos", "TIME_DEFAULT": "<PERSON><PERSON><PERSON>", "TIME_SPENT": "Tempo Gasto", "TITLE": "Personalizar Visualização de Tarefas"}}, "TIME_TRACKING": {"B": {"ALREADY_DID": "<PERSON>u já fiz", "SNOOZE": "Soneca {{time}}"}, "B_TTR": {"ADD_TO_TASK": "Adicionar <PERSON>fa", "MSG": "Você não está monitorando o tempo de {{time}}"}, "D_IDLE": {"ADD_ENTRY": "Adicionar entrada para rastreamento", "BREAK": "Pausa", "CREATE_AND_TRACK": "<em><PERSON><PERSON>r</em> e acompanhar:", "IDLE_FOR": "Você esteve ocioso por:", "RESET_BREAK_REMINDER_TIMER": "Reiniciar o temporizador de lembrete de intervalo", "SIMPLE_CONFIRM_COUNTER_CANCEL": "<PERSON><PERSON>", "SIMPLE_CONFIRM_COUNTER_OK": "Track", "SIMPLE_COUNTER_CONFIRM_TXT": "Você selecion<PERSON> igno<PERSON>, mas ativou {{nr}} botão (s) de contador simples. Você deseja rastrear o tempo ocioso deles?", "SIMPLE_COUNTER_TOOLTIP": "Clique para rastrear até {{title}}", "SIMPLE_COUNTER_TOOLTIP_DISABLE": "Clique para NÃO rastrear para {{title}}", "SKIP": "<PERSON><PERSON>", "SPLIT_TIME": "Divida o tempo em múltiplas tarefas e intervalos", "TASK": "<PERSON><PERSON><PERSON>", "TRACK_TO": "<PERSON><PERSON><PERSON> para:"}, "D_TRACKING_REMINDER": {"CREATE_AND_TRACK": "<em><PERSON><PERSON></em> e rastreie para", "IDLE_FOR": "Você está ocioso por:", "NOTIFICATION_TITLE": "Acompanhe seu tempo!", "TASK": "<PERSON><PERSON><PERSON>", "TRACK_TO": "<PERSON><PERSON><PERSON><PERSON> para:", "UNTRACKED_TIME": "Tempo não rastreado:"}}, "WORKLOG": {"CMP": {"DAYS_WORKED": "<PERSON><PERSON>, trabalhado:", "MONTH_WORKED": "<PERSON><PERSON><PERSON> t<PERSON>:", "REPEATING_TASK": "Tarefa repetida", "RESTORE_TASK_FROM_ARCHIVE": "Restaurar tarefa do arquivo", "TASKS": "<PERSON><PERSON><PERSON><PERSON>", "TOTAL_TIME": "Tempo gasto total:", "WEEK_NR": "Semana {{nr}}", "WORKED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "D_CONFIRM_RESTORE": "Tem certeza de que deseja mover a tarefa <strong> \"{{title}}\" </strong> para a sua lista de tarefas de hoje?", "D_EXPORT_TITLE": "Exportação de log de trabalho {{start}}–{{end}}", "D_EXPORT_TITLE_SINGLE": "Exportação de log de trabalho {{day}}", "EXPORT": {"ADD_COL": "<PERSON><PERSON><PERSON><PERSON> coluna", "COPY_TO_CLIPBOARD": "Copiar para área de transferência", "DONT_ROUND": "<PERSON>ão arredondar", "EDIT_COL": "<PERSON><PERSON> coluna", "GROUP_BY": "Agrupar por", "O": {"DATE": "Data", "ENDED_WORKING": "<PERSON><PERSON><PERSON><PERSON> encerrado", "ESTIMATE_AS_CLOCK": "Estimativa como relógio (ex.: 5:23)", "ESTIMATE_AS_MILLISECONDS": "Estimativa em milissegundos", "ESTIMATE_AS_STRING": "Estimativa como texto (ex.: 5h 23m)", "FULL_HALF_HOURS": "meia-horas completa", "FULL_HOURS": "horas completas", "FULL_QUARTERS": "quartos completos", "NOTES": "Descrições de Tarefas", "PARENT_TASK": "<PERSON><PERSON><PERSON> pai", "PARENT_TASK_TITLES_ONLY": "<PERSON><PERSON> títulos da tarefa pai", "PROJECTS": "Nomes de Projetos", "STARTED_WORKING": "Começou a trabalhar", "TAGS": "Tag", "TASK_SUBTASK": "Tarefa/Subtarefa", "TIME_AS_CLOCK": "Hora como relógio (ex.: 5:23)", "TIME_AS_MILLISECONDS": "Tempo em milissegundos", "TIME_AS_STRING": "Tempo como texto (ex.: 5h 23m)", "TITLES_AND_SUB_TASK_TITLES": "Títulos e Títulos de subtarefa", "WORKLOG": "Log de trabalho"}, "OPTIONS": "Opções", "ROUND_END_TIME_TO": "Arredondar hora final para", "ROUND_START_TIME_TO": "Arredondar hora de início para", "ROUND_TIME_WORKED_TO": "Arredondar tempo trabalhado para", "SAVE_TO_FILE": "Salvar em arquivo", "SEPARATE_TASKS_BY": "Separe as tare<PERSON>s por", "SHOW_AS_TEXT": "Mostrar como texto"}, "WEEK": {"EXPORT": "Exportar dados da semana", "NO_DATA": "Ainda não há tarefas nesta semana.", "TITLE": "<PERSON><PERSON><PERSON><PERSON>"}}}, "FILE_IMEX": {"DIALOG_CONFIRM_URL_IMPORT": {"INITIATED_MSG": "Uma importação automática de dados foi iniciada.", "SOURCE_URL_DOMAIN": "<PERSON><PERSON><PERSON> origem", "TITLE": "Confirmar importação de dados do URL", "WARNING_MSG": "Prosseguir irá sobrescrever seus dados e configurações atuais do aplicativo com o conteúdo do URL especificado. Esta ação não pode ser desfeita.", "WARNING_TITLE": "Aviso"}, "EXPORT_DATA": "Exportar dados", "IMPORT_FROM_FILE": "Importar do arquivo", "IMPORT_FROM_URL": "Importar de URL", "IMPORT_FROM_URL_DIALOG_DESCRIPTION": "Por favor, insira o URL completo do arquivo de backup JSON do Super Productivity que deseja importar.", "IMPORT_FROM_URL_DIALOG_TITLE": "Importar de URL", "OPEN_IMPORT_FROM_URL_DIALOG": "Importar de URL", "PRIVACY_EXPORT": "Exportar dados anonimizados (para <NAME_EMAIL> para depuração)", "S_BACKUP_DOWNLOADED": "Backup baixado para a pasta de documentos do Android", "S_ERR_IMPORT_FAILED": "Falha na importação de dados", "S_ERR_INVALID_DATA": "Falha na importação: JSON inválido", "S_ERR_INVALID_URL": "Falha na importação: URL fornecido inválido", "S_ERR_NETWORK": "Falha na importação: Erro de rede ao buscar dados do URL", "S_IMPORT_FROM_URL_ERR_DECODE": "Erro: Não foi possível decodificar o parâmetro URL para importação. Certifique-se de que está corretamente formatado.", "URL_PLACEHOLDER": "Insira o URL para importar"}, "G": {"ADD": "<PERSON><PERSON><PERSON><PERSON>", "ADVANCED_CFG": "Configuração Avançada", "CANCEL": "<PERSON><PERSON><PERSON>", "CLOSE": "<PERSON><PERSON><PERSON>", "CONFIRM": "Confirmar", "DELETE": "Excluir", "DISMISS": "Dispensar", "DO_IT": "Fazer isto!", "DURATION_DESCRIPTION": "por exemplo \"5h 23m\" que resulta em 5 horas em 23 minutos", "EDIT": "<PERSON><PERSON>", "ENABLED": "<PERSON><PERSON>do", "EXAMPLE_VAL": "Clique para editar", "EXTENSION_INFO": "Por favor <a target=\"_blank\" href=\"https://chrome.google.com/webstore/detail/super-productivity/ljkbjodfmekklcoibdnhahlaalhihmlb\"> faça o download da extensão chrome</a> para permitir a comunicação com o Jira Api e o Idle Time Handling. Observe que isso não funciona para dispositivos móveis.", "HIDE": "Esconder", "ICON_INP_DESCRIPTION": "Todos os emojis utf-8 também são suportados!", "INBOX_PROJECT_TITLE": "Caixa de entrada", "LOGIN": "<PERSON><PERSON>", "LOGOUT": "<PERSON><PERSON>", "MINUTES": "{{m}} minutos", "MOVE_BACKWARD": "Mover para Trás", "MOVE_FORWARD": "Mover para <PERSON>ente", "NEXT": "Próximo", "NO_CON": "Você está atualmente offline. Reconecte-se à Internet.", "NONE": "<PERSON><PERSON><PERSON>", "OK": "Ok", "OVERDUE": "<PERSON><PERSON><PERSON>", "PREVIOUS": "Anterior", "REMOVE": "Remover", "RESET": "<PERSON><PERSON><PERSON><PERSON>", "SAVE": "<PERSON><PERSON>", "SUBMIT": "Enviar", "TITLE": "<PERSON><PERSON><PERSON><PERSON>", "TODAY_TAG_TITLE": "Hoje", "TRACKING_INTERVAL_DESCRIPTION": "Acompanhe o tempo utilizando este intervalo em milissegundos. Você pode querer mudar isso para reduzir as gravações em disco. Veja a edição #2355.", "UNDO": "<PERSON><PERSON><PERSON>", "UPDATE": "<PERSON><PERSON><PERSON><PERSON>", "WITHOUT_PROJECT": "Sem Projeto", "YESTERDAY": "Ontem"}, "GCF": {"AUTO_BACKUPS": {"HELP": "Salve automaticamente todos os dados na pasta do aplicativo para prepará-los, caso algo dê errado.", "LABEL_IS_ENABLED": "Ativar backups automáticos", "LOCATION_INFO": "Os backups s<PERSON> salvos em:", "TITLE": "Backups automáticos"}, "CALENDARS": {"BROWSER_WARNING": "<b>Provavelmente isso NÃO funcionará com a versão para navegador do Super Productivity.<br /> <a href=\"https://super-productivity.com/download/\">Baixe a versão para desktop</a> para usar este recurso!</b>", "CAL_PATH": "URL da fonte iCal", "CAL_PROVIDERS": "Provedores de calendário (experimental e opcional)", "CHECK_UPDATES": "Verifique se há atualizações remotas a cada X", "DEFAULT_PROJECT": "Projeto padrão para tarefas de calendário adicionadas", "HELP": "Você pode integrar calendários para ser lembrado e adicioná-los como tarefas dentro do Super Productivity. A integração funciona usando o formato iCal. Para que isso funcione, seus calendários precisam estar acessíveis pela internet ou pelo sistema de arquivos.", "SHOW_BANNER_THRESHOLD": "Mostrar uma notificação X antes do evento (em branco para desabilitado)"}, "EVALUATION": {"IS_HIDE_EVALUATION_SHEET": "Ocultar folha de avaliação no resumo diário", "TITLE": "Avaliação e Métricas"}, "FOCUS_MODE": {"HELP": "O Modo Foco abre uma tela sem distrações para ajudar você a se concentrar na sua tarefa atual.", "L_ALWAYS_OPEN_FOCUS_MODE": "Sempre abra o modo de foco ao rastrear", "L_SKIP_PREPARATION_SCREEN": "Pular tela de preparação (alongamento etc.)", "TITLE": "Modo de Foco"}, "IDLE": {"HELP": "<div><p>Quando a manipulação de tempo ocioso estiver ativada, uma caixa de diálogo será aberta após um período especificado para verificar se e em qual tarefa você deseja acompanhar seu tempo, quando você estiver ocioso.</p></div>", "IS_ENABLE_IDLE_TIME_TRACKING": "Ativar manipulação de tempo ocioso", "IS_ONLY_OPEN_IDLE_WHEN_CURRENT_TASK": "Só aciona o diálogo de tempo ocioso quando uma tarefa atual é selecionada", "MIN_IDLE_TIME": "<PERSON><PERSON> o<PERSON>so a<PERSON>", "TITLE": "Manuseio inativo"}, "IMEX": {"HELP": "<p><PERSON><PERSON>, você pode exportar todos os seus dados como um <strong>JSON</strong> para backups, mas também para usá-los em um contexto diferente (por exemplo, convém exportar seus projetos no navegador e importá-los para a versão desktop).</p> <p>A importação espera que o JSON válido seja copiado na área de texto. <strong>NOTA: Depois de pressionar o botão de importação, todas as suas configurações e dados atuais serão substituídos!</strong></p>", "TITLE": "Importar/Exportar"}, "KEYBOARD": {"ADD_NEW_NOTE": "Adicionar nova nota", "ADD_NEW_TASK": "Adicionar nova tarefa", "APP_WIDE_SHORTCUTS": "Atalhos globais (em todo o aplicativo)", "COLLAPSE_SUB_TASKS": "Recolher subtarefas", "EXPAND_SUB_TASKS": "Expanda Subtarefas", "GLOBAL_ADD_NOTE": "Adicionar nova nota", "GLOBAL_ADD_TASK": "Adicionar nova tarefa", "GLOBAL_SHOW_HIDE": "Mostrar/Esconder Super Productivity", "GLOBAL_TOGGLE_TASK_START": "Alternar rastreamento de tempo para a última tarefa ativa", "GO_TO_DAILY_AGENDA": "<PERSON><PERSON> <PERSON>", "GO_TO_FOCUS_MODE": "Ir para modo de foco", "GO_TO_SCHEDULE": "Vá para a linha do tempo", "GO_TO_SCHEDULED_VIEW": "Vá para Tarefas agendadas", "GO_TO_SETTINGS": "<PERSON><PERSON> <PERSON> as configura<PERSON><PERSON><PERSON>", "GO_TO_WORK_VIEW": "Ir para modo de trabalho", "HELP": "<p><PERSON><PERSON> você pode configurar todos os atalhos do teclado.</p> <p>Clique na entrada de texto e insira a combinação de teclado desejada. Pressione Enter para salvar e Esc para abortar.</p> <p>Existem três tipos de atalhos:</p> <ul> <li> <strong>Atalhos globais:</strong> Quando o aplicativo está sendo executado, ele acionará a ação de todos os outros aplicativos. </li> <li> <strong>Atalhos no nível do aplicativo:</strong> Será acionado em todas as telas do aplicativo, mas não se você estiver editando um campo de texto. </li> <li> <strong>Atalhos no nível da tarefa:</strong> Eles serão acionados apenas se você tiver selecionado uma tarefa via mouse ou teclado e, normalmente, acionarão uma ação especificamente relacionada a essa tarefa. </li> </ul>", "MOVE_TASK_DOWN": "Mover tarefa para baixo na lista", "MOVE_TASK_TO_BOTTOM": "Mover tarefa para o final da lista", "MOVE_TASK_TO_TOP": "Mover tarefa para o topo da lista", "MOVE_TASK_UP": "Mover tarefa para cima na lista", "MOVE_TO_BACKLOG": "Mover tarefa para o backlog de tarefas", "MOVE_TO_REGULARS_TASKS": "Mover tarefa para a lista de tarefas de hoje", "OPEN_PROJECT_NOTES": "Mostrar / ocultar notas do projeto", "SAVE_NOTE": "<PERSON><PERSON> nota", "SELECT_NEXT_TASK": "Selecione a próxima tarefa", "SELECT_PREVIOUS_TASK": "Selecionar tarefa anterior", "SHOW_SEARCH_BAR": "Mostrar barra de pesquisa", "SYSTEM_SHORTCUTS": "Atal<PERSON> globais (todo o sistema)", "TASK_ADD_ATTACHMENT": "Anexar arquivo ou link", "TASK_ADD_SUB_TASK": "Adicionar subtarefa", "TASK_DELETE": "Excluir tarefa", "TASK_EDIT_TAGS": "Editar etiquetas", "TASK_EDIT_TITLE": "<PERSON><PERSON>", "TASK_MOVE_TO_PROJECT": "Abrir tarefa de mover para o menu do projeto", "TASK_OPEN_CONTEXT_MENU": "Abra o menu de contexto de tarefas", "TASK_OPEN_ESTIMATION_DIALOG": "Editar estimativa / tempo gasto", "TASK_PLAN_FORDAY": "Plano para o dia", "TASK_SCHEDULE": "Agendar tarefa", "TASK_SHORTCUTS": "<PERSON><PERSON><PERSON><PERSON>", "TASK_SHORTCUTS_INFO": "<PERSON><PERSON> se<PERSON>tes atalhos se aplicam à tarefa atualmente selecionada (selecionada via guia ou mouse).", "TASK_TOGGLE_DETAIL_PANEL_OPEN": "Mostrar/ocultar informações adicionais da tarefa", "TASK_TOGGLE_DONE": "<PERSON><PERSON><PERSON>", "TITLE": "Atalhos do teclado", "TOGGLE_BACKLOG": "Mostrar/ocultar tarefas do backlog", "TOGGLE_BOOKMARKS": "Mostrar/ocultar barra de favoritos", "TOGGLE_ISSUE_PANEL": "Mostrar/ocultar painel de problemas", "TOGGLE_PLAY": "Começar/Parar <PERSON>", "TOGGLE_SIDE_NAV": "Mostrar e focar / ocultar o Sidenav", "TOGGLE_TASK_VIEW_CUSTOMIZER_PANEL": "Alternar Painel de Filtro/Grupo/Ordenar", "TRIGGER_SYNC": "Botão de sincronização (se configurado)", "ZOOM_DEFAULT": "Zoom pad<PERSON> (somente para computador)", "ZOOM_IN": "<PERSON><PERSON> zoom (somente para computador)", "ZOOM_OUT": "Di<PERSON><PERSON><PERSON> o zoom (somente para computador)"}, "LANG": {"AR": "عرب<PERSON>", "CZ": "Checo", "DE": "Alemão", "EN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ES": "Espanhol", "FA": "فار<PERSON>ی", "FR": "franc<PERSON>s", "HR": "Hungria", "ID": "Indonésio", "IT": "Italiano", "JA": "日本語", "KO": "한국어", "LABEL": "Selecione um idioma", "NB": "Norsk Bokmål", "NL": "<PERSON><PERSON><PERSON>", "PL": "Polaco", "PT": "Português", "RU": "<PERSON>", "SK": "Eslovaco", "TITLE": "Língua", "TR": "Türkçe", "UK": "Українська", "ZH": "中文(简体)", "ZH_TW": "中文(繁體)"}, "MISC": {"DEFAULT_PROJECT": "Projeto padrão a ser usado para tarefas se nenhum for especificado", "FIRST_DAY_OF_WEEK": "Primeiro dia da semana", "HELP": "<p><strong><PERSON><PERSON> está vendo as notificações da área de trabalho?</strong> Para janelas, convém verificar Sistema> Notificações e ações, e verificar se as notificações necessárias foram ativadas.</p>", "IS_AUTO_ADD_WORKED_ON_TO_TODAY": "Adicionar automaticamente a tag hoje às tarefas trabalhadas", "IS_AUTO_MARK_PARENT_AS_DONE": "Marque a tarefa pai como concluída, quando todas as subtarefas estiverem concluídas", "IS_CONFIRM_BEFORE_EXIT": "Confirme antes de sair do aplicativo", "IS_CONFIRM_BEFORE_EXIT_WITHOUT_FINISH_DAY": "Confirme antes de sair do aplicativo sem terminar o dia primeiro", "IS_DARK_MODE": "<PERSON><PERSON> es<PERSON>ro", "IS_DISABLE_ANIMATIONS": "<PERSON><PERSON><PERSON> todas as animações", "IS_HIDE_NAV": "Ocultar a navegação até passar o mouse no cabeçalho principal (somente na área de trabalho)", "IS_MINIMIZE_TO_TRAY": "Minimize para a bandeja (somente desktop)", "IS_SHOW_TIP_LONGER": "Mostrar dica de produtividade no início do aplicativo um pouco mais", "IS_TRAY_SHOW_CURRENT_COUNTDOWN": "Mostrar contagem regressiva atual na bandeja / menu de status (apenas desktop mac)", "IS_TRAY_SHOW_CURRENT_TASK": "Mostra a tarefa atual na bandeja / menu de status (somente desktop)", "IS_TURN_OFF_MARKDOWN": "Desativar a análise de markdown para anotações", "IS_USE_MINIMAL_SIDE_NAV": "Use a barra de navegação mínima (mostrar apenas ícones)", "START_OF_NEXT_DAY": "Hora de início do dia seguinte", "START_OF_NEXT_DAY_HINT": "a partir de quando (em horas) você deseja contar o dia seguinte começou. o padrão é meia-noite, que é 0.", "TASK_NOTES_TPL": "Modelo de descrição de tarefa", "TITLE": "Configurações diversas"}, "POMODORO": {"BREAK_DURATION": "Duração de pausas curtas", "CYCLES_BEFORE_LONGER_BREAK": "Iniciar pausa mais longa após X sessões de trabalho", "DURATION": "Duração das sessões de trabalho", "HELP": "<p>O temporizador pomodoro pode ser configurado através de algumas configurações. A duração de cada sessão de trabalho, a duração das quebras normais, o número de sessões de trabalho a serem executadas antes do início de uma pausa mais longa e a duração dessa pausa mais longa.</p> <p>Você também pode definir se deseja exibir suas distrações durante seus intervalos de pomodoro.</p> <p>Configurar \"Pausar o rastreamento do tempo no intervalo do pomodoro\" também acompanhará seus intervalos como o tempo de trabalho gasto em uma tarefa. </p> <p>Habilitando \"Pausar a sessão pomodoro quando nenhuma tarefa ativa\" também pausará a sessão pomodoro, quando você pausar uma tarefa.</p>", "IS_ENABLED": "Ativar temporizador pomodoro", "IS_MANUAL_CONTINUE": "Confirmar manualmente o início da próxima sessão de pomodoro", "IS_MANUAL_CONTINUE_BREAK": "Confirme manualmente o início do próximo intervalo", "IS_PLAY_SOUND": "<PERSON><PERSON> som quando a sessão terminar", "IS_PLAY_SOUND_AFTER_BREAK": "Tocar som quando o intervalo estiver concluído", "IS_PLAY_TICK": "<PERSON><PERSON> som de tick a cada segundo", "IS_STOP_TRACKING_ON_BREAK": "Parar o rastreamento do tempo para tarefas em pausa", "LONGER_BREAK_DURATION": "Duração de pausas mais longas", "TITLE": "Configurações do Pomodoro"}, "REMINDER": {"COUNTDOWN_DURATION": "Mostrar o banner X antes do lembrete real", "IS_COUNTDOWN_BANNER_ENABLED": "Mostrar uma faixa de contagem decrescente antes da data de vencimento dos lembretes", "TITLE": "<PERSON><PERSON><PERSON>"}, "SCHEDULE": {"HELP": "O recurso de linha do tempo deve fornecer uma visão geral rápida sobre como suas tarefas planejadas se desenrolam ao longo do tempo. Você pode encontrá-lo no menu à esquerda em <a href='#/schedule'>'Linha do tempo'</a>.", "L_IS_LUNCH_BREAK_ENABLED": "Ativar o intervalo de almoço", "L_IS_WORK_START_END_ENABLED": "Limite o fluxo de tarefas não programadas a horários de trabalho específicos", "L_LUNCH_BREAK_END": "Fim do intervalo de almoço", "L_LUNCH_BREAK_START": "Inicio do intervalo de almoço", "L_WORK_END": "Fim do dia de trabalho", "L_WORK_START": "Início do dia de trabalho", "LUNCH_BREAK_START_END_DESCRIPTION": "ex: 13:00", "TITLE": "Schedule", "WORK_START_END_DESCRIPTION": "por exemplo. 17:00"}, "SHORT_SYNTAX": {"HELP": "<p><PERSON><PERSON> pode controlar as opções de sintaxe curta ao criar uma tarefa</p>", "IS_ENABLE_DUE": "Ativar a sintaxe curta devida (@<Due time>)", "IS_ENABLE_PROJECT": "Ativar a sintaxe curta do projeto (+<Project name>)", "IS_ENABLE_TAG": "Ativar a sintaxe curta da etiqueta (#<Tag>)", "TITLE": "Sintaxe curta"}, "SOUND": {"BREAK_REMINDER_SOUND": "Faça um lembrete sonoro de pausa", "DONE_SOUND": "Som de tarefa concluída", "IS_INCREASE_DONE_PITCH": "Aumente o tom para cada tarefa realizada", "TITLE": "Som", "TRACK_TIME_SOUND": "Som de lembrete de tempo de rastreamento", "VOLUME": "Volume"}, "TAKE_A_BREAK": {"ADD_NEW_IMG": "Adicione uma imagem motivacional", "FULL_SCREEN_BLOCKER_DURATION": "Duração para exibir a janela em tela cheia (somente desktop)", "HELP": "<div> <p>Permite configurar um lembrete recorrente quando você trabalha por um período de tempo especificado sem fazer uma pausa.</p> <p>Você pode modificar a mensagem exibida. ${duration} será substituído pelo tempo gasto sem interrupção.</p> </div>", "IS_ENABLED": "Ativar lembrete de pausa", "IS_FOCUS_WINDOW": "Focar a janela do aplicativo quando o lembrete estiver ativo (somente desktop)", "IS_FULL_SCREEN_BLOCKER": "Aparece uma mensagem em tela cheia na tela (apenas para computador)", "IS_LOCK_SCREEN": "Bloquear a tela quando iniciar um intervalo (somente desktop)", "MESSAGE": "Mensagem de pausa", "MIN_WORKING_TIME": "Acionar notificação de pausa após trabalhar X sem uma", "MOTIVATIONAL_IMGS": "Imagem motivacional (URL da Web)", "NOTIFICATION_TITLE": "Dar um tempo!", "SNOOZE_TIME": "Tempo de soneca quando solicitado para fazer uma pausa", "TITLE": "Le<PERSON><PERSON> de <PERSON>usa"}, "TIME_TRACKING": {"HELP": "O lembrete de controlo de tempo é uma faixa que aparece no caso de se ter esquecido de iniciar o controlo de tempo.", "L_DEFAULT_ESTIMATE": "Estimativa de tempo predefinida para novas tarefas", "L_DEFAULT_ESTIMATE_SUB_TASKS": "Estimativa de tempo predefinida para novas subtarefas", "L_IS_AUTO_START_NEXT_TASK": "Iniciar o acompanhamento da tarefa seguinte ao marcar a atual como concluída", "L_IS_NOTIFY_WHEN_TIME_ESTIMATE_EXCEEDED": "Notificar quando o tempo estimado foi excedido", "L_IS_TRACKING_REMINDER_ENABLED": "Lembrete de rastreio ativado", "L_IS_TRACKING_REMINDER_FOCUS_WINDOW": "Janela do app Foco quando o lembrete está ativo (somente desktop)", "L_IS_TRACKING_REMINDER_NOTIFY": "Notificar quando o lembrete de controle de tempo for exibido", "L_IS_TRACKING_REMINDER_SHOW_ON_MOBILE": "Mostrar lembrete de seguimento na aplicação móvel", "L_TRACKING_INTERVAL": "Intervalo de controlo do tempo (EXPERIMENTAL)", "L_TRACKING_REMINDER_MIN_TIME": "Tempo de espera antes de mostrar o lembrete de seguimento Banner", "TITLE": "Controlo de tempo"}}, "GLOBAL_RELATIVE_TIME": {"FUTURE": {"A_DAY": "em um dia", "A_MINUTE": "daqui a um minuto", "A_MONTH": "daqui a um mês", "A_YEAR": "daqui a um ano", "AN_HOUR": "daqui a uma hora", "DAYS": "daqui a {{count}} dias", "FEW_SECONDS": "da<PERSON> a poucos segundos", "HOURS": "daqui a {{count}} horas", "MINUTES": "daqui a {{count}} minutos", "MONTHS": "daqui a {{count}} meses", "YEARS": "daqui a {{count}} anos"}, "PAST": {"A_DAY": "Há um dia", "A_MINUTE": "há um minuto", "A_MONTH": "<PERSON><PERSON> um mês", "A_YEAR": "há um ano", "AN_HOUR": "há uma hora", "DAYS": "há {{count}} dias", "FEW_SECONDS": "há poucos segundos", "HOURS": "há {{count}} horas", "MINUTES": "há {{count}} minutos", "MONTHS": "há {{count}} meses", "YEARS": "há {{count}} anos"}}, "GLOBAL_SNACK": {"COPY_TO_CLIPPBOARD": "Copiado para a área de transferência", "ERR_COMPRESSION": "Erro para interface de compactação", "FILE_DOWNLOADED": "{{fileName}} baixado", "FILE_DOWNLOADED_BTN": "Pasta aberta", "NAVIGATE_TO_TASK_ERR": "Não foi possível focar a tarefa. Apagou-a?", "PERSISTENCE_DISALLOWED": "Os dados não serão mantidos permanentemente. Esteja ciente de que isso pode levar à perda de dados!", "PERSISTENCE_ERROR": "Erro ao solicitar a persistência de dados: {{err}}", "RUNNING_X": "<PERSON><PERSON><PERSON> \"{{str}}\".", "SHORTCUT_WARN_OPEN_BOOKMARKS_FROM_TAG": "{{keyCombo}} pressionado, mas o atalho para abrir favoritos só está disponível no contexto do projeto."}, "GPB": {"ASSETS": "Carregando recursos...", "DBX_DOWNLOAD": "Dropbox: Baixar arquivo ...", "DBX_GEN_TOKEN": "Dropbox: gerar token ...", "DBX_META": "Dropbox: Obter meta ...", "DBX_UPLOAD": "Dropbox: Carregar arquivo ...", "GITHUB_LOAD_ISSUE": "GitHub: Carregar dados do problema ...", "JIRA_LOAD_ISSUE": "Jira: Carregar dados do problema ...", "SYNC": "Sincronizando dados...", "UNKNOWN": "Carregando dados remotos", "WEB_DAV_DOWNLOAD": "WebDAV: Baixando dados ...", "WEB_DAV_UPLOAD": "WebDAV: Carregando dados ..."}, "MH": {"ADD_NEW_TASK": "Adicionar nova tarefa", "ALL_PLANNED_LIST": "Repetir/Programado", "BOARDS": "Placas", "CREATE_PROJECT": "<PERSON><PERSON>r <PERSON>", "CREATE_TAG": "Criar tag", "DELETE_PROJECT": "Eliminar projeto", "DELETE_TAG": "Excluir tag", "ENTER_FOCUS_MODE": "Entrar no Modo Foco", "GO_TO_TASK_LIST": "Ir para a lista de tarefas", "HELP": "<PERSON><PERSON><PERSON>", "HM": {"CALENDARS": "Como fazer: Ligar calendários", "CONTRIBUTE": "Contribuir", "GET_HELP_ONLINE": "Buscar ajuda online", "KEYBOARD": "Como fazer: <PERSON><PERSON><PERSON><PERSON>", "REDDIT_COMMUNITY": "Comunidade no Reddit", "REPORT_A_PROBLEM": "Reportar um problema", "START_WELCOME": "Iniciar a visita de boas-vindas", "SYNC": "Como fazer: Configurar a sincronização"}, "METRICS": "Métricas", "NO_PROJECT_INFO": "Não há projetos disponíveis. Você pode criar um novo projeto clicando no botão \"Criar projeto\".", "NO_TAG_INFO": "Atualmente não existem etiquetas. Pode adicionar etiquetas introduzindo `#yourTagName` ao adicionar ou editar tarefas.", "NOTES": "Notas", "NOTES_PANEL_INFO": "As notas só podem ser mostradas a partir da agenda e das vistas normais da lista de tarefas.", "PLANNER": "Plan<PERSON>or", "PROCRASTINATE": "Procrastinar", "PROJECT_MENU": "<PERSON><PERSON>", "PROJECT_SETTINGS": "Configurações do projeto", "PROJECTS": "Projetos", "QUICK_HISTORY": "<PERSON><PERSON><PERSON><PERSON>", "SCHEDULE": "<PERSON><PERSON><PERSON><PERSON>", "SEARCH": "<PERSON><PERSON><PERSON><PERSON>", "SETTINGS": "Configurações", "SHOW_SEARCH_BAR": "Mostrar barra de pesquisa", "TAGS": "Tag", "TASK_LIST": "Lista de tarefas", "TASKS": "<PERSON><PERSON><PERSON><PERSON>", "TOGGLE_SHOW_BOOKMARKS": "Mostrar/ocultar favoritos", "TOGGLE_SHOW_ISSUE_PANEL": "Mostrar/ocultar painel de problemas", "TOGGLE_SHOW_NOTES": "Mostrar/ocultar notas do projeto", "TOGGLE_TRACK_TIME": "Iniciar/parar rastreamento de tempo ", "TRIGGER_SYNC": "Acionar a sincronização manualmente", "WORKLOG": "Log de trabalho"}, "MIGRATE": {"C_DOWNLOAD_BACKUP": "Deseja baixar um backup de seus dados legados (pode ser usado com versões mais antigas do Super Productivity)?", "DETECTED_LEGACY": "Dados legados detectados. Vamos migrá-lo para você!", "E_MIGRATION_FAILED": "Falha na migração! com erro:", "E_RESTART_FAILED": "Falha na reinicialização automática. Reinicie o aplicativo manualmente!", "SUCCESS": "Migração concluída! Reiniciando o aplicativo agora..."}, "PDS": {"ADD_TASKS_FROM_TODAY": "Adicionar tare<PERSON>s a partir de hoje", "BACK": "<PERSON><PERSON><PERSON>, eu esqueci uma coisa!", "BREAK_LABEL": "Pausa (nr / time)", "CELEBRATE": "Tome um momento para <i>comemorar!</i>", "CLEAR_ALL_CONTINUE": "Limpar tudos os concluídos e continuar", "D_CONFIRM_APP_CLOSE": {"CANCEL": "<PERSON><PERSON>, apenas limpe as tare<PERSON>s", "MSG": "Seu trabalho está feito. Hora de ir para casa!", "OK": "Sim, sim! <PERSON><PERSON><PERSON>!"}, "ESTIMATE_TOTAL": "Estimativa total:", "EVALUATE_DAY": "Avaliar", "EXPORT_TASK_LIST": "Exportar lista de tarefas", "NO_TASKS": "Não há tarefas para este dia", "PLAN_TOMORROW": "Plano", "REVIEW_TASKS": "<PERSON><PERSON><PERSON>", "ROUND_5M": "<PERSON><PERSON><PERSON><PERSON> as tare<PERSON>s para 5 minutos", "ROUND_15M": "<PERSON><PERSON><PERSON><PERSON> as tare<PERSON>s para 15 minutos", "ROUND_30M": "<PERSON><PERSON><PERSON><PERSON> as tare<PERSON>s para 30 minutos", "ROUND_TIME_SPENT": "Arredonde Tempo Gasto", "ROUND_TIME_SPENT_TITLE": "Arredonde tempo gasto em todas as tarefas. Seja cuidadoso! Você não pode desfazer isso!", "ROUND_TIME_WARNING": "!!! <PERSON><PERSON><PERSON>, isso não pode ser desfeito!!!", "ROUND_UP_5M": "<PERSON><PERSON><PERSON><PERSON> as tare<PERSON>s para 5 minutos", "ROUND_UP_15M": "<PERSON><PERSON><PERSON><PERSON> as tare<PERSON>s para 15 minutos", "ROUND_UP_30M": "<PERSON><PERSON><PERSON><PERSON> as tare<PERSON>s para 30 minutos", "SAVE_AND_GO_HOME": "Salve e vá para inicio", "SAVE_AND_GO_HOME_TOOLTIP": "<PERSON><PERSON> todas as tare<PERSON>s concluídas para o arquivo (registo de trabalho) e, opcionalmente, sincronize todos os dados e feche a aplicação.", "START_END": "Começar – Fim", "SUMMARY_FOR": "<PERSON><PERSON><PERSON> para {{dayStr}}", "TASKS_COMPLETED": "<PERSON><PERSON><PERSON><PERSON> con<PERSON>", "TIME_SPENT_AND_ESTIMATE_LABEL": "Tempo gasto / Estimado", "TIME_SPENT_ESTIMATE_TITLE": "Tempo gasto: Tempo total gasto hoje. Tarefas arquivadas não incluídas. – Tempo estimado: Tempo estimado para tarefas trabalhadas hoje menos o tempo já gasto com elas em outros dias.", "TIME_SPENT_TODAY_BY_TAG": "Tempo gasto hoje por etiqueta", "WEEK": "Se<PERSON>"}, "PM": {"TITLE": "Métricas do projeto"}, "PS": {"GLOBAL_SETTINGS": "Configurações globais", "ISSUE_INTEGRATION": "Integração com gerenciadores de tarefas", "PRIVACY_POLICY": "Política Privada", "PRODUCTIVITY_HELPER": "Auxiliar de produtividade", "PROJECT_SETTINGS": "Configurações específicas do projeto", "PROVIDE_FEEDBACK": "Dar uma resposta", "SYNC_EXPORT": "Sincronizar e exportar", "TAG_SETTINGS": "Configurações específicas de tags", "TOGGLE_DARK_MODE": "Alternar modo escuro"}, "SCHEDULE": {"NO_REPEATABLE_TASKS": "No momento, não há tarefas recorrentes. Você pode agendar uma tarefa escolhendo \"Repetir Tarefa\" no menu de contexto da tarefa. Para abrí-lo clique nos 3 pontinhos à direita de uma tarefa.", "NO_SCHEDULED": "No momento, não há tarefas agendadas. Você pode agendar uma tarefa escolhendo \"Agendar Tarefa\" no menu de contexto da tarefa. Para abrí-lo, clique nos 3 pontinhos à direita de uma tarefa.", "NO_SCHEDULED_TITLE": "Tarefas programadas para o dia", "REPEATED_TASKS": "Tarefas Recorrentes", "SCHEDULED_TASKS": "<PERSON><PERSON><PERSON><PERSON>", "SCHEDULED_TASKS_WITH_TIME": "Tarefas programadas com lembrete", "START_TASK": "Iniciar tarefa agora e remover lembrete"}, "THEMES": {"amber": "Âmbar", "blue": "Azul", "blue-grey": "<PERSON><PERSON><PERSON>", "cyan": "<PERSON><PERSON>", "deep-orange": "Laranja profundo", "deep-purple": "Roxo profundo", "green": "Verde", "indigo": "Índigo", "light-blue": "<PERSON><PERSON>l claro", "light-green": "Verde claro", "lime": "Lima", "pink": "<PERSON>", "purple": "<PERSON><PERSON><PERSON>", "SELECT_THEME": "Selecionar Tema", "teal": "<PERSON><PERSON><PERSON>", "yellow": "<PERSON><PERSON>"}, "V": {"E_1TO10": "Digite um valor entre 1 e 10", "E_DATETIME": "O valor inserido não é um datetime!", "E_DURATION": "Insira uma duração válida (por exemplo, 1h)", "E_MAX": "<PERSON><PERSON> deve ser maior que {{val}}", "E_MAX_LENGTH": "Deve ter no max. {{val}} caracteres", "E_MIN": "Deve ser menor que {{val}}", "E_MIN_LENGTH": "<PERSON>e ter pelo menos {{val}} caracteres", "E_PATTERN": "Entrada inválida", "E_REQUIRED": "Este campo é obrigatório"}, "WW": {"ADD_MORE": "<PERSON><PERSON><PERSON> mais", "ADD_SCHEDULED_FOR_TOMORROW": "Adicionar tare<PERSON>s planejadas para amanhã ({{nr}})", "ADD_SOME_TASKS": "Adicione algumas tarefas para planejar o seu dia!", "DONE_TASKS": "<PERSON><PERSON><PERSON><PERSON>", "DONE_TASKS_IN_ARCHIVE": "Atualmente não há tarefas concluídas aqui, mas já existem algumas arquivadas.", "ESTIMATE_REMAINING": "Estimativa restante:", "FINISH_DAY": "Encerrar o dia", "FINISH_DAY_FOR_PROJECT": "Dia de conclusão deste projeto", "FINISH_DAY_FOR_TAG": "Dia de conclusão para esta etiqueta", "FINISH_DAY_TOOLTIP": "<PERSON>lie o seu dia, mova todas as tare<PERSON>s concluídas para o arquivo (opcionalmente) e/ou planeie o seu dia seguinte.", "HELP_PROCRASTINATION": "Ajuda! Eu estou procrastinando!", "MOVE_DONE_TO_ARCHIVE": "Mover concluído para arquivar", "NO_DONE_TASKS": "No momento, não há tarefas concluídas", "NO_PLANNED_TASK_ALL_DONE": "<PERSON><PERSON> as tare<PERSON><PERSON> conclu<PERSON>", "NO_PLANNED_TASKS": "<PERSON><PERSON><PERSON><PERSON>", "READY_TO_WORK": "Pronto para trabalhar!", "RESET_BREAK_TIMER": "Reiniciar sem temporizador de pause", "TIME_ESTIMATED": "Tempo estimado:", "TODAY_REMAINING": "Hoje, resta:", "WITHOUT_BREAK": "Sem pausa:", "WORKING_TODAY": "Trabalhando hoje:", "WORKING_TODAY_ARCHIVED": "Tempo trabalhado hoje em tarefas arquivadas"}}