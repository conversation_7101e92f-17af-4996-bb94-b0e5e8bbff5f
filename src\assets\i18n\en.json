{"ANDROID": {"NOTIFICATIONS": {"SYNC_CONFLICT_MSG": "Please check and decide what to do.", "SYNC_CONFLICT_TITLE": "A sync conflict occurred"}, "PERMANENT_NOTIFICATION_MSGS": {"INITIAL": "App running in background to allow for syncing if enabled", "NO_ACTIVE_TASKS": "No active tasks", "SYNCING": "Syncing"}}, "APP": {"B_INSTALL": {"IGNORE": "Ignore", "INSTALL": "Install", "MSG": "Do you want to install Super Productivity as a PWA?"}, "B_OFFLINE": "You are disconnected from the internet. Syncing and requesting issue provider data will not work.", "UPDATE_MAIN_MODEL": "Super Productivity has gotten a major update! Some migrations for your data are required. Please note that this renders your data incompatible with older versions of the app.", "UPDATE_MAIN_MODEL_NO_UPDATE": "No model update chosen. Please note that you either have to downgrade to the last version, if you do not want to perform the model upgrade.", "UPDATE_WEB_APP": "New version available. Load New Version?"}, "BL": {"NO_TASKS": "There are currently no tasks in your backlog"}, "CONFIRM": {"AUTO_FIX": "Your data seems to be damaged (\"{{validityError}}\"). Do you want to try to automatically fix it? This might result in partial data loss.", "RELOAD_AFTER_IDB_ERROR": "Cannot access database :( Possible causes are an app update to the app in the background or low disk space. If you installed the app on linux as snap you also want to enable refresh awareness 'snap set core experimental.refresh-app-awareness=true' until they fix this issue on their side. Press OK to reload the app (might require manual restarting the app on some platforms).", "RESTORE_FILE_BACKUP": "There seems to be NO DATA, but there are backups available at \"{{dir}}\". Do you want to restore the latest backup from {{from}}?", "RESTORE_FILE_BACKUP_ANDROID": "There seems to be NO DATA, but there is a backup available. Do you want to load it?", "RESTORE_STRAY_BACKUP": "During last sync there might have been some error. Do you want to restore the last backup?"}, "DATETIME_SCHEDULE": {"LATER_TODAY": "Later Today", "NEXT_WEEK": "Next Week", "PLACEHOLDER": "Please select a date", "PRESS_ENTER_AGAIN": "Press enter again to save", "TOMORROW": "Tomorrow"}, "F": {"ATTACHMENT": {"DIALOG_EDIT": {"ADD_ATTACHMENT": "Add Attachment", "EDIT_ATTACHMENT": "Edit Attachment", "LABELS": {"FILE": "File Path", "IMG": "Image", "LINK": "Url"}, "SELECT_TYPE": "Select a type", "TYPES": {"FILE": "File (opened by default system app)", "IMG": "Image (shown as thumbnail)", "LINK": "Link (opens in browser)"}}}, "BOARDS": {"DEFAULT": {"DONE": "Done", "EISENHAUER_MATRIX": "<PERSON>", "IMPORTANT": "Important", "IN_PROGRESS": "In Progress", "KANBAN": "Ka<PERSON><PERSON>", "NOT_URGENT_IMPORTANT": "Not Urgent & Important", "NOT_URGENT_NOT_IMPORTANT": "Not Urgent & Not Important", "TO_DO": "To Do", "URGENT": "<PERSON><PERSON>", "URGENT_IMPORTANT": "Urgent & Important", "URGENT_NOT_IMPORTANT": "Urgent & Not Important"}, "FORM": {"ADD_NEW_PANEL": "Add new Panel", "BACKLOG_TASK_FILTER_ALL": "All", "BACKLOG_TASK_FILTER_NO_BACKLOG": "Exclude", "BACKLOG_TASK_FILTER_ONLY_BACKLOG": "Backlog only", "BACKLOG_TASK_FILTER_TYPE": "Backlog tasks", "COLUMNS": "Columns", "TAGS_EXCLUDED": "Excluded Tags", "TAGS_REQUIRED": "Required Tags", "TASK_DONE_STATE": "Task Done State", "TASK_DONE_STATE_ALL": "All", "TASK_DONE_STATE_DONE": "Done", "TASK_DONE_STATE_UNDONE": "Undone"}, "V": {"ADD_NEW_BOARD": "Add new board", "CONFIRM_DELETE": "Do you really want to delete this board?", "CREATE_NEW_TAG_BTN": "Create Tag", "CREATE_NEW_TAG_MSG": "1 new tag needs to be created for this board to work", "CREATE_NEW_TAGS_BTN": "Create Tags", "CREATE_NEW_TAGS_MSG": "{{nr}} new tags needs to be created for this board to work", "EDIT_BOARD": "Edit board", "NO_PANELS_BTN": "Configure Board", "NO_PANELS_MSG": "This board has no panels configured. Add some panels to it."}}, "CALDAV": {"DIALOG_INITIAL": {"TITLE": "Setup CalDav for Project"}, "FORM": {"CALDAV_CATEGORY_FILTER": "Category to filter issues for (leave empty for none)", "CALDAV_PASSWORD": "Your CalDav password", "CALDAV_RESOURCE": "The name of the CalDav resource (the calendar)", "CALDAV_URL": "CalDav URL (the base URL)", "CALDAV_USER": "Your CalDav username", "IS_TRANSITION_ISSUES_ENABLED": "Automatically complete CalDav todos on task completion"}, "FORM_SECTION": {"HELP": "<p>Here you can configure SuperProductivity to list uncompleted CalDav todos for a specific project in the task creation panel in the daily planning view. They will be listed as suggestions and will provide a link to the todo as well as more information about it.</p> <p>In addition you can automatically add and sync all uncompleted todos to your task backlog.</p><p>To make it work for nextcloud in the webapp, you might need to whitelist \"https://app.super-productivity.com\" via the nextcloud app <a href='https://apps.nextcloud.com/apps/webapppassword'>webapppassword<a>.</p>", "TITLE": "CalDav"}, "ISSUE_CONTENT": {"ASSIGNEE": "Assignee", "AT": "at", "ATTACHMENTS": "Attachments", "CHANGED": "changed", "COMMENTS": "Comments", "COMPONENTS": "Components", "DESCRIPTION": "Description", "LABELS": "Categories", "LIST_OF_CHANGES": "List of changes", "MARK_AS_CHECKED": "Mark updates as checked", "ON": "on", "RELATED": "Related", "STATUS": "Status", "STORY_POINTS": "Story Points", "SUB_TASKS": "Subtasks", "SUMMARY": "Summary", "WORKLOG": "Worklog", "WRITE_A_COMMENT": "Write a comment"}, "S": {"CALENDAR_NOT_FOUND": "CalDav: Calendar \"{{calendarName}}\" not found", "CALENDAR_READ_ONLY": "CalDav: Calendar \"{{calendarName}}\" is readonly", "ISSUE_NOT_FOUND": "CalDav: Todo \"{{issueId}}\" seems to be deleted on server."}}, "CALENDARS": {"BANNER": {"ADD_AS_TASK": "Add as Task", "FOCUS_TASK": "Focus Task", "TXT": "<strong>{{title}}</strong> starts at <strong>{{start}}</strong>!", "TXT_MULTIPLE": "<strong>{{title}}</strong> starts at <strong>{{start}}</strong>!<br> (and {{nrOfOtherBanners}} other events are due)", "TXT_PAST": "<strong>{{title}}</strong> started at <strong>{{start}}</strong>!", "TXT_PAST_MULTIPLE": "<strong>{{title}}</strong> started at <strong>{{start}}</strong>!<br> (and {{nrOfOtherBanners}} other events are due)"}, "S": {"CAL_PROVIDER_ERROR": "Calendar Provider Error: {{errTxt}}"}}, "CONFIG": {"S": {"UPDATE_SECTION": "Updated settings for <strong>{{sectionKey}}</strong>"}}, "D_RATE": {"A_HOW": "How and where to rate", "BTN_DONT_BOTHER": "Don't bother me again", "TITLE": "🙈 Please forgive us, but...", "TXT": "You would help the project immensely by <strong>giving it a good rating, if you like it!</strong>"}, "DOMINA_MODE": {"FORM": {"HELP": "Repeats the configured phrase every X when tracking time to a task.", "L_INTERVAL": "Interval to repeat phrase", "L_TEXT": "Text", "L_TEXT_DESCRIPTION": "E.g.: \"Work on ${currentTaskTitle}!\"", "L_VOICE": "Select a Voice", "L_VOICE_DESCRIPTION": "Choose a voice", "TITLE": "Domina Mode"}}, "DROPBOX": {"S": {"ACCESS_TOKEN_ERROR": "Dropbox: Unable to generate Access Token from Auth Code", "ACCESS_TOKEN_GENERATED": "Dropbox: Access Token generated from Auth Code", "AUTH_ERROR": "Dropbox: Invalid access token provided", "AUTH_ERROR_ACTION": "Change Token", "OFFLINE": "Dropbox: Unable to sync, because offline", "SYNC_ERROR": "Dropbox: Error while syncing", "UNABLE_TO_GENERATE_PKCE_CHALLENGE": "Dropbox: Unable to generate PKCE challenge."}}, "FINISH_DAY_BEFORE_EXIT": {"C": {"FINISH_DAY_BEFORE_EXIT": "There are {{nr}} done tasks in your today list not yet moved to the archive. Do you really want to quit without finishing your day?"}}, "FOCUS_MODE": {"B": {"SESSION_RUNNING": "Focus Session is running", "TO_FOCUS_OVERLAY": "To Focus Overlay"}, "BACK_TO_PLANNING": "Back to Planning", "CONGRATS": "Congrats for completing this session!", "CONTINUE_FOCUS_SESSION": "Continue Focus Session", "COUNTDOWN": "Countdown", "FINISH_TASK_AND_SELECT_NEXT": "Finish task and select next", "FLOWTIME": "Flowtime", "FOR_TASK": "for task", "GET_READY": "Get ready for your focus session!", "GO_TO_PROCRASTINATION": "Get help, when procrastinating", "GOGOGO": "Go, go, go!!!", "NEXT": "Next", "ON": "on", "OPEN_ISSUE_IN_BROWSER": "Open issue in Browser", "POMODORO_BACK": "Back", "POMODORO_DISABLE": "Disable Pomodoro", "POMODORO_INFO": "Focus sessions cannot be used together with the pomodoro timer enabled.", "PREP_GET_MENTALLY_READY": "Get mentally ready to be focused and productive", "PREP_SIT_UPRIGHT": "Sit (or stand) upright", "PREP_STRETCH": "Do some mild stretching", "SELECT_ANOTHER_TASK": "Select another Task", "SELECT_TASK": "Select Task to focus on", "SESSION_COMPLETED": "Focus Session Completed!", "SET_FOCUS_SESSION_DURATION": "Set Focus Session Duration", "SHOW_HIDE_NOTES_AND_ATTACHMENTS": "Show/hide task notes and attachments", "START_FOCUS_SESSION": "Start Focus Session", "START_NEXT_FOCUS_SESSION": "Start next Focus Session", "WORKED_FOR": "You worked for"}, "GITEA": {"DIALOG_INITIAL": {"TITLE": "Setup Gitea for Project"}, "FORM": {"FILTER_USER": "Username (e.g. to filter out changes by yourself)", "HOST": "Host (e.g.: https://try.gitea.io)", "REPO_FULL_NAME": "Username or Organization name/project", "REPO_FULL_NAME_DESCRIPTION": "Can be found as part of the url, when viewing the project in the Browser.", "SCOPE": "<PERSON><PERSON>", "SCOPE_ALL": "All", "SCOPE_ASSIGNED": "Assigned to me", "SCOPE_CREATED": "Created by me", "TOKEN": "Access Token"}, "FORM_SECTION": {"HELP": "<p>Here you can configure SuperProductivity to list open Gitea issues for a specific repository in the task creation panel in the daily planning view. They will be listed as suggestions and will provide a link to the issue as well as more information about it.</p> <p>In addition you can automatically add and import all open issues.</p><p>To get by usage limits and to access you can provide a an access token.", "TITLE": "<PERSON><PERSON><PERSON>"}, "ISSUE_CONTENT": {"ASSIGNEE": "Assignee", "AT": "at", "DESCRIPTION": "Description", "LABELS": "Labels", "MARK_AS_CHECKED": "Mark updates as checked", "PROJECT": "Project", "STATUS": "Status", "SUMMARY": "Summary", "WRITE_A_COMMENT": "Write a comment"}, "S": {"ERR_UNKNOWN": "Gitea: Unknown error {{statusCode}} {{errorMsg}}. Api Rate limit exceeded?"}}, "GITHUB": {"DIALOG_INITIAL": {"TITLE": "Setup GitHub for Project"}, "FORM": {"FILTER_USER": "Username (e.g. to filter out changes by yourself)", "INVALID_TOKEN_MESSAGE": "Not a valid GitHub token. It must start with \"ghp_\".", "IS_ASSIGNEE_FILTER": "Only import issues assigned to me to backlog", "REPO": "username/repositoryName", "TOKEN": "Access Token"}, "FORM_SECTION": {"HELP": "<p>Here you can configure SuperProductivity to list open GitHub issues for a specific repository in the task creation panel in the daily planning view. They will be listed as suggestions and will provide a link to the issue as well as more information about it.</p> <p>In addition you can automatically import all open issues.</p><p>To get by usage limits and to access you can provide a an access token. <a href='https://docs.github.com/en/free-pro-team@latest/developers/apps/scopes-for-oauth-apps'>More info about its scopes can be found here</a>.", "TITLE": "GitHub"}, "ISSUE_CONTENT": {"ASSIGNEE": "Assignee", "AT": "at", "DESCRIPTION": "Description", "LABELS": "Labels", "LAST_COMMENT": "Last comment", "LOAD_ALL_COMMENTS": "Load all {{nr}} comments", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "Load description & all comments", "MARK_AS_CHECKED": "Mark updates as checked", "STATUS": "Status", "SUMMARY": "Summary", "WRITE_A_COMMENT": "Write a comment"}, "S": {"CONFIG_ERROR": "GitHub: Error while mapping data. Is your repository name correct?", "ERR_UNKNOWN": "GitHub: Unknown error {{statusCode}} {{errorMsg}}. Api Rate limit exceeded?"}}, "GITLAB": {"DIALOG_INITIAL": {"TITLE": "Setup GitLab for Project"}, "*********************": {"PAST_DAY_INFO": "The prefilled duration contains untracked data from past days.", "T_ALREADY_TRACKED": "Already Tracked", "T_TITLE": "Title", "T_TO_BE_SUBMITTED": "To be submitted", "TITLE": "Submit Time Spent on Issues to GitLab", "TOTAL_MSG": "You will submit <em>{{totalTimeToSubmit}}</em> work time for today in total to <em>{{nrOfTasksToSubmit}}</em> different issues."}, "FORM": {"FILTER": "Custom Filter", "FILTER_DESCRIPTION": "See https://docs.gitlab.com/ee/api/issues.html#list-issues. Multiple can be combined by &", "FILTER_USER": "Filter username", "GITLAB_BASE_URL": "Custom GitLab base URL (optional)", "PROJECT": "user name/project", "PROJECT_HINT": "e.g. johannesjo/super-productivity", "SCOPE": "<PERSON><PERSON>", "SCOPE_ALL": "All", "SCOPE_ASSIGNED": "Assigned to me", "SCOPE_CREATED": "Created by me", "SOURCE": "Source", "SOURCE_GLOBAL": "All", "SOURCE_GROUP": "Group", "SOURCE_PROJECT": "Project", "SUBMIT_TIMELOGS": "Submit timelogs to Gitlab", "SUBMIT_TIMELOGS_DESCRIPTION": "Show Time Tracking Dialog after clicking on finish day", "TOKEN": "Access Token"}, "FORM_SECTION": {"HELP": "<p>Here you can configure SuperProductivity to list open GitLab (either its the online version or a self-hosted instance) issues for a specific project in the task creation panel in the daily planning view. They will be listed as suggestions and will provide a link to the issue as well as more information about it.</p> <p>In addition you can automatically import all open issues.</p>", "TITLE": "GitLab"}, "ISSUE_CONTENT": {"ASSIGNEE": "Assignee", "AT": "at", "DESCRIPTION": "Description", "LABELS": "Labels", "MARK_AS_CHECKED": "Mark updates as checked", "PROJECT": "Project", "STATUS": "Status", "SUMMARY": "Summary", "WRITE_A_COMMENT": "Write a comment"}, "S": {"ERR_UNKNOWN": "GitLab: Unknown error {{statusCode}} {{errorMsg}}"}}, "ISSUE": {"CROSS_ORIGIN_BROWSER_WARNING": "This integration will likely not work in your browser. Please download the Desktop or Android version of Super Productivity!", "DEFAULT": {"ISSUE_STR": "issue", "ISSUES_STR": "issues"}, "DEFAULT_PROJECT_DESCRIPTION": "Project assigned to tasks created from issues.", "DEFAULT_PROJECT_LABEL": "Default Super Productivity Project", "HOW_TO_GET_A_TOKEN": "How to get a token?", "ISSUE_CONTENT": {"ASSIGNEE": "Assignee", "AT": "at", "ATTACHMENTS": "Attachments", "AUTHOR": "Author", "CATEGORY": "Category", "CHANGED": "changed", "COMMENTS": "Comments", "COMPONENTS": "Components", "DESCRIPTION": "Description", "DONE_RATIO": "<PERSON>", "DUE_DATE": "Due Date", "LABELS": "Labels", "LAST_COMMENT": "Last comment", "LIST_OF_CHANGES": "List of changes", "LOAD_ALL_COMMENTS": "Load all {{nr}} comments", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "Load description & all comments", "LOCATION": "Location", "MARK_AS_CHECKED": "Mark updates as checked", "ON": "on", "PRIORITY": "Priority", "RELATED": "Related", "START": "Start", "STATUS": "Status", "STORY_POINTS": "Story Points", "SUB_TASKS": "Subtasks", "SUMMARY": "Summary", "TIME_SPENT": "Time Spent", "TYPE": "Type", "VERSION": "Version", "WORKLOG": "Worklog", "WRITE_A_COMMENT": "Write a comment"}, "S": {"ERR_GENERIC": "{{issue<PERSON><PERSON><PERSON><PERSON>ame}} Error: {{errTxt}}", "ERR_NETWORK": "{{issue<PERSON><PERSON><PERSON>Name}}: Request failed because of a client side network error", "ERR_NOT_CONFIGURED": "{{issue<PERSON><PERSON><PERSON><PERSON>ame}}: Not properly configured", "IMPORTED_MULTIPLE_ISSUES": "{{issueProviderName}}: Imported {{nr}} new {{issuesStr}}", "IMPORTED_SINGLE_ISSUE": "{{issueProviderName}}: Imported {{issueStr}} \"{{issueTitle}}\"", "ISSUE_DELETED_OR_CLOSED": "{{issueProviderName}}: {{issueStrC}} \"{{issueTitle}}\" seems to be deleted or closed", "ISSUE_NO_UPDATE_REQUIRED": "{{issue<PERSON><PERSON>iderName}}: No update required", "ISSUE_UPDATE_MULTIPLE": "{{issueProviderName}}: Updated data for {{nr}} {{issuesStr}}", "ISSUE_UPDATE_SINGLE": "{{issueProviderName}}: Updated data for \"{{issueTitle}}\"", "MANUAL_UPDATE_ISSUE_SUCCESS": "{{issueProviderName}}: Updated data for \"{{issueTitle}}\"", "MISSING_ISSUE_DATA": "{{issueProviderName}}: Tasks with missing {{issueStr}} data found. Reloading.", "NEW_COMMENT": "{{issueProviderName}}: New comment for \"{{issueTitle}}\"", "POLLING_BACKLOG": "{{issueProviderName}}: Polling for new {{issuesStr}}", "POLLING_CHANGES": "{{issueProviderName}}: Polling Changes for {{issuesStr}}"}}, "JIRA": {"BANNER": {"BLOCK_ACCESS_MSG": "Jira: To prevent shut out from api, access has been blocked by Super Productivity. You probably should check your jira settings!", "BLOCK_ACCESS_UNBLOCK": "Unblock"}, "CFG_CMP": {"ALWAYS_ASK": "Always open dialog", "DO_NOT": "Don't transition", "DONE": "Status for completing task", "ENABLE": "Enable Jira integration", "ENABLE_TRANSITIONS": "Enable Transition Handling", "IN_PROGRESS": "Status for starting task", "LOAD_SUGGESTIONS": "Load Suggestions", "MAP_CUSTOM_FIELDS": "Load Story Points", "MAP_CUSTOM_FIELDS_INFO": "Unfortunately some of Jira's data is saved under custom fields which are different for every installation. If you want to include this data you need to select the proper custom field for it. Currently there is only the story points field that needs to be mapped.", "OPEN": "Status for pausing task", "SELECT_ISSUE_FOR_TRANSITIONS": "Select issue to load available transitions", "STORY_POINTS": "Story Points Field Name", "TRANSITION": "Transition Handling"}, "DIALOG_CONFIRM_ASSIGNMENT": {"MSG": "<strong>{{summary}}</strong> is currently assigned to <strong>{{assignee}}</strong>. Do you want to assign it to yourself?", "OK": "Do it!"}, "DIALOG_INITIAL": {"TITLE": "<PERSON>up <PERSON> for Project"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "Choose status to assign", "CURRENT_ASSIGNEE": "Current Assignee:", "CURRENT_STATUS": "Current Status:", "TASK_NAME": "Task name:", "TITLE": "Jira: Update Status", "UPDATE_STATUS": "Update Status"}, "DIALOG_WORKLOG": {"CHECKBOXES": {"ALL_TIME": "Always use all time spent on task as default", "ALL_TIME_MINUS_LOGGED": "Always use only time spent minus time logged as default", "TIME_SPENT_TODAY": "Always use only time spent today as default", "TIME_SPENT_YESTERDAY": "Always use only time spent yesterday as default"}, "CURRENTLY_LOGGED": "Currently logged time: ", "INVALID_DATE": "The entered value is not a date!", "SAVE_WORKLOG": "Save Worklog", "STARTED": "Started", "SUBMIT_WORKLOG_FOR": "Submit a worklog to Jira for", "TIME_SPENT": "Time Spent", "TIME_SPENT_TOOLTIP": "Add different times", "TITLE": "Jira: Submit Worklog"}, "FORM_ADV": {"AUTO_ADD_BACKLOG_JQL_QUERY": "JQL used for importing issues to Super Productivity automatically", "IS_ADD_WORKLOG_ON_SUB_TASK_DONE": "Open dialog to submit worklog to Jira when sub task is done", "IS_CHECK_TO_RE_ASSIGN_TICKET_ON_TASK_START": "Check if the currently worked on issue is assigned to current user", "IS_WORKLOG_ENABLED": "Open dialog to submit worklog to <PERSON><PERSON> when task is done", "SEARCH_JQL_QUERY": "JQL Query for limiting searching tasks", "WORKLOG_DEFAULT_ALL_TIME": "Fill in all time spent on task", "WORKLOG_DEFAULT_ALL_TIME_MINUS_LOGGED": "Fill in all time spent minus time logged", "WORKLOG_DEFAULT_TIME_MODE": "Default time value for dialog", "WORKLOG_DEFAULT_TODAY": "Fill only time spent today", "WORKLOG_DEFAULT_YESTERDAY": "Fill only time spent yesterday"}, "FORM_CRED": {"ALLOW_SELF_SIGNED": "Allow self signed certificate", "HOST": "Host (e.g.: http://my-host.de:1234)", "PASSWORD": "Token / Password", "USE_PAT": "Use Personal Access Token instead (LEGACY)", "USER_NAME": "Email / Username", "WONKY_COOKIE_MODE": "Wonky Cookie Fallback Authentication (desktop app only)"}, "FORM_SECTION": {"ADV_CFG": "Advanced Config", "HELP_ARR": {"H1": "Basic configuration", "H2": "Worklog settings", "H3": "Default transitions", "P1_1": "Please provide a login name (can be found on your profile page) and an <a target=\"_blank\" href=\"https://confluence.atlassian.com/cloud/api-tokens-938839638.html\" target=\"_blank\">API token</a> or password if you can't generate one for some reason. Please not that newer versions of Jira sometimes only work with the token. ", "P1_2": "You also need to specify a JQL query which is used for the suggestions to add tasks from <PERSON><PERSON>. If you need help check out this link <a target=\"_blank\" href=\"https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html\" target=\"_blank\">https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html</a>.", "P1_3": "You can also configure, if you want to automatically (e.g. every time you visit the planning view), to import all new issues specified by a custom JQL query.", "P1_4": "Another option is \"Check if current ticket is assigned to current user\". If enabled and you're starting, a check will be made if you're currently assigned to that ticket on <PERSON><PERSON>, if not an Dialog appears in which you can chose to assign the ticket to yourself.", "P2_1": "There are several options to determine when and how you want to submit a worklog. Enabling <em>'Open worklog dialog for adding a worklog to Jira when task is done'</em> opens a dialog to add an worklog every time you mark a Jira Task as done. So keep in mind that worklogs will be added on top of everything tracked so far. So if you mark a task as done for a second time, you might not want to submit the complete worked time for the task again.", "P2_2": "<em>'Open worklog dialog when sub task is done and not for tasks with sub tasks themselves'</em> opens a worklog dialog every time when you mark a sub task of a Jira issue as done. Because you already track your time via the sub tasks, no dialog is opened once you mark the Jira task itself as done.", "P2_3": "<em>'Send updates to worklog automatically without dialog'</em> does what it says. Because marking a task as done several times leads to the whole worked time being tracked twice, this is not recommended.", "P3_1": "Here you can reconfigure your default transitions. Jira enables a wide configuration of transitions usually coming into action as different columns on your Jira agile board we can't make assumptions about where and when to transition your tasks and you need to set it manually."}}, "ISSUE_CONTENT": {"ASSIGNEE": "Assignee", "AT": "at", "ATTACHMENTS": "Attachments", "CHANGED": "changed", "COMMENTS": "Comments", "COMPONENTS": "Components", "DESCRIPTION": "Description", "LIST_OF_CHANGES": "List of changes", "MARK_AS_CHECKED": "Mark updates as checked", "ON": "on", "RELATED": "Related", "STATUS": "Status", "STORY_POINTS": "Story Points", "SUB_TASKS": "Subtasks", "SUMMARY": "Summary", "WORKLOG": "Worklog", "WRITE_A_COMMENT": "Write a comment"}, "S": {"ADDED_WORKLOG_FOR": "Jira: Added worklog for {{issueKey}}", "EXTENSION_NOT_LOADED": "Super Productivity Extension not loaded. Reloading the page might help", "INSUFFICIENT_SETTINGS": "Insufficient Settings provided for <PERSON><PERSON>", "INVALID_RESPONSE": "Jira: Response contained invalid data", "ISSUE_NO_UPDATE_REQUIRED": "<PERSON><PERSON>: \"{{issueText}}\" already up to date", "MANUAL_UPDATE_ISSUE_SUCCESS": "Jira: Updated data for \"{{issueText}}\"", "MISSING_ISSUE_DATA": "Jira: Tasks with missing issue data found. Reloading.", "NO_AUTO_IMPORT_JQL": "Jira: No search query defined for auto import", "NO_VALID_TRANSITION": "Jira: No valid transition configured", "TIMED_OUT": "Jira: Request timed out", "TRANSITION": "<PERSON><PERSON>: Set issue \"{{issueKey}}\" to \"{{name}}\"", "TRANSITION_SUCCESS": "<PERSON><PERSON>: Set issue {{issue<PERSON><PERSON>}} to <strong>{{chosenTransition}}</strong>", "TRANSITIONS_LOADED": "Jira: Transitions loaded. Use the selects below to assign them", "UNABLE_TO_REASSIGN": "<PERSON><PERSON>: Unable to reassign ticket to yourself, because you didn't specify a username. Please visit the settings."}, "STEPPER": {"CREDENTIALS": "Credentials", "DONE": "You are now done.", "LOGIN_SUCCESS": "Login successful!", "TEST_CREDENTIALS": "Test Credentials", "WELCOME_USER": "Welcome {{user}}!"}}, "MARKDOWN_PASTE": {"CONFIRM_ADD_TO_SUB_TASK_NOTES": "Add the pasted markdown list to the notes of sub-task \"{{parentTaskTitle}}\"?", "CONFIRM_PARENT_TASKS": "Create <strong>{{tasksCount}} new tasks</strong> from the pasted markdown list?", "CONFIRM_PARENT_TASKS_WITH_SUBS": "Create <strong>{{tasksCount}} new tasks and {{subTasksCount}} sub-tasks</strong> from the pasted markdown list?", "CONFIRM_SUB_TASKS": "Create {{tasksCount}} new sub-tasks from the pasted markdown list?", "CONFIRM_SUB_TASKS_WITH_PARENT": "Create <strong>{{tasksCount}} new sub-tasks under \"{{parentTaskTitle}}\"</strong> from the pasted markdown list?", "DIALOG_TITLE": "Pasted Markdown List detected!"}, "METRIC": {"BANNER": {"CHECK": "I did it!"}, "CMP": {"AVG_BREAKS_PER_DAY": "Avg. breaks per day", "AVG_TASKS_PER_DAY_WORKED": "Avg. tasks per day worked", "AVG_TIME_SPENT_ON_BREAKS": "Avg. time spent on breaks", "AVG_TIME_SPENT_PER_DAY": "Avg. time spent per day", "AVG_TIME_SPENT_PER_TASK": "Avg. time spent per task", "COUNTING_SUBTASKS": "(counting subtasks)", "DAYS_WORKED": "Days worked", "GLOBAL_METRICS": "Global Metrics", "IMPROVEMENT_SELECTION_COUNT": "Number of times an improvement factor was selected", "MOOD_PRODUCTIVITY_OVER_TIME": "Mood and productivity over time", "NO_ADDITIONAL_DATA_YET": "No additional data collected yet. Use the form on the daily summary \"Evaluation\" panel to do so.", "OBSTRUCTION_SELECTION_COUNT": "Number of times an obstructing factor was selected", "SIMPLE_CLICK_COUNTERS_OVER_TIME": "Click Counters over time", "SIMPLE_COUNTERS": "Simple Counters & Habit Tracking", "SIMPLE_STOPWATCH_COUNTERS_OVER_TIME": "Stopwatch Counters over time", "TASKS_DONE_CREATED": "Tasks (done/created)", "TIME_ESTIMATED": "Time Estimated", "TIME_SPENT": "Time Spent"}, "EVAL_FORM": {"ADD_NOTE_FOR_TOMORROW": "Add Note for tomorrow", "DISABLE_REPEAT_EVERY_DAY": "Disable repeat every day", "ENABLE_REPEAT_EVERY_DAY": "Repeat every day", "HELP_H1": "Why should I care?", "HELP_LINK_TXT": "Go to metrics section", "HELP_P1": "Time for a little self evaluation! Your answers here are saved and provide you with a little bit of statistics on how you work in the metrics section. Furthermore the suggestions for tomorrow will appear above your task list the next day.", "HELP_P2": "This is intended to be less about calculating exact metrics or becoming machine like efficient in all you do than it is about improving how you feel about your work. It can be helpful to evaluate pain points in your daily routine, as well as it is to find factors that help you out. Being just a little bit systematic about it hopefully helps to get a better grip on these and to improve what you can.", "IMPROVEMENTS": "What improved your productivity?", "IMPROVEMENTS_TOMORROW": "What could you do to improve tomorrow?", "MOOD": "How do you feel?", "MOOD_HINT": "1: <PERSON>w<PERSON> – 10: <PERSON><PERSON>ndi<PERSON>", "NOTES": "Notes for tomorrow", "OBSTRUCTIONS": "What hindered your productivity?", "PRODUCTIVITY": "How efficient did you work?", "PRODUCTIVITY_HINT": "1: Haven't even started – 10: Enormously efficient"}, "S": {"SAVE_METRIC": "<PERSON><PERSON> successfully saved"}}, "NOTE": {"D_ADD": {"NOTE_LABEL": "Enter some text to save as note (you can use markdown)..."}, "D_FULLSCREEN": {"VIEW_PARSED": "View as parsed (non editable) markdown", "VIEW_SPLIT": "View parsed and unparsed markdown in split view", "VIEW_TEXT_ONLY": "View as unparsed text"}, "NOTE_CMP": {"DISABLE_PARSE": "Disable markdown parsing for preview", "ENABLE_PARSE": "Enable markdown parse"}, "NOTES_CMP": {"ADD_BTN": "Add new Note", "DROP_TO_ADD": "Drop here to add new note", "NO_NOTES": "There are currently no notes"}, "S": {"NOTE_ADDED": "Note saved"}}, "OPEN_PROJECT": {"CFG_CMP": {"ALWAYS_ASK": "Always open dialog", "DO_NOT": "Don't transition", "DONE": "Status for completing task", "ENABLE": "Enable Openproject integration", "ENABLE_TRANSITIONS": "Enable Transition Handling", "IN_PROGRESS": "Status for starting task", "OPEN": "Status for pausing task", "PROGRESS_ON_SAVE": "Default progress on save", "SELECT_ISSUE_FOR_TRANSITIONS": "Select issue to load available transitions", "TRANSITION": "Transition Handling"}, "DIALOG_INITIAL": {"TITLE": "Setup OpenProject for Project"}, "DIALOG_TRACK_TIME": {"ACTIVITY": "Activity", "CURRENTLY_LOGGED": "Currently logged time: ", "INVALID_DATE": "The entered value is not a date!", "POST_TIME": "Post Time", "STARTED": "Started", "SUBMIT_TIME_FOR": "Submit time to OpenProject for", "TIME_SPENT": "Time Spent", "TITLE": "OpenProject: Submit Worklog"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "Choose status to assign", "CURRENT_ASSIGNEE": "Current Assignee:", "CURRENT_STATUS": "Current Status:", "PERCENTAGE_DONE": "Progress:", "TASK_NAME": "Task name:", "TITLE": "OpenProject: Update Status", "UPDATE_STATUS": "Update Status"}, "FORM": {"FILTER_USER": "Filter username", "HOST": "Host (e.g.: https://www.openproject.org/)", "IS_SHOW_TIME_TRACKING_DIALOG": "Show time tracking dialog to report to OpenProject", "IS_SHOW_TIME_TRACKING_DIALOG_DESCRIPTION": "Requires time tracking module to be enabled for OpenProject project", "IS_SHOW_TIME_TRACKING_DIALOG_FOR_EACH_SUB_TASK": "Show time tracking dialog when sub tasks are completed", "PROJECT_ID": "Project ID", "PROJECT_ID_DESCRIPTION": "Can be found as part of the url, when viewing the project in the Browser.", "SCOPE": "<PERSON><PERSON>", "SCOPE_ALL": "All", "SCOPE_ASSIGNED": "Assigned to me", "SCOPE_CREATED": "Created by me", "TOKEN": "Access Token"}, "FORM_SECTION": {"HELP": "<p>Here you can configure SuperProductivity to list open OpenProject work packages. Please Note that for this to work in the browser you need to probably configure CORS for your OpenProject server, to allow access from app.super-productivity.com</p>", "TITLE": "OpenProject"}, "ISSUE_CONTENT": {"ASSIGNEE": "Assignee", "ATTACHMENTS": "Attachments", "DESCRIPTION": "Description", "MARK_AS_CHECKED": "Mark updates as checked", "STATUS": "Status", "SUMMARY": "Summary", "TYPE": "Type", "UPLOAD_ATTACHMENT": "Upload to Task"}, "ISSUE_STRINGS": {"ISSUE_STR": "work package", "ISSUES_STR": "work packages"}, "S": {"ERR_NO_FILE": "No file selected", "ERR_UNKNOWN": "OpenProject: Unknown error {{statusCode}} {{errorMsg}}. Is CORS properly configured for the server?", "POST_TIME_SUCCESS": "OpenProject: Successfully created time entry for {{issueTitle}}", "TRANSITION": "OpenProject: Set issue \"{{issueKey}}\" to \"{{name}}\"", "TRANSITION_SUCCESS": "OpenProject: Set issue {{issueK<PERSON>}} to <strong>{{chosenTransition}}</strong>", "TRANSITIONS_LOADED": "Transitions loaded. Use the selects below to assign them"}}, "PLANNER": {"D": {"ADD_PLANNED": {"ADD_TO_TODAY": "Add to Today", "RE_PLAN_ALL": "Reschedule all", "TITLE": "Add planned tasks to Today"}}, "EDIT_REPEATED_TASK": "Edit repeated task '{{taskName}}'", "NO_TASKS": "No tasks", "PLAN_VIEW": {"NO_SCHEDULED_ITEMS": "No scheduled items"}, "S": {"REMOVED_PLAN_DATE": "Removed plan date for task '{{taskTitle}}'", "TASK_ALREADY_PLANNED": "Task is already planned for {{date}}", "TASK_PLANNED_FOR": "Task planned for <strong>{{date}}</strong>{{extra}}"}, "TASK_DRAWER": "Task Drawer"}, "POMODORO": {"BACK_TO_WORK": "Back to work!", "BREAK_IS_DONE": "Your break is done!", "ENJOY_YOURSELF": "Enjoy yourself, get yourself moving, come back in:", "FINISH_SESSION_X": "You successfully finished session <strong>{{nr}}</strong>!", "NOTIFICATION": {"BREAK_TIME": "Pomodoro: Time for break {{nr}}!", "BREAK_X_START": "Pomodoro: Break {{nr}} started!", "NO_TASKS": "You need to add tasks before the Pomodoro timer can start.", "SESSION_X_START": "Pomodoro: Session {{nr}} started!"}, "S": {"RESET": "Reset to first Pomodoro Session", "SESSION_SKIP": "Skip to end of current Pomodoro Session", "SESSION_X_START": "Pomodoro: Session {{nr}} started!"}, "SKIP_BREAK": "Skip break", "START_BREAK": "Start break"}, "PROCRASTINATION": {"BACK_TO_WORK": "Back to work!", "COMP": {"INTRO": "People with high procrastination levels usually have low self-compassion. So practice it! It improves your feeling of self-worth, fosters positive emotions and can help you overcome procrastination, of course. Try a little exercise:", "L1": "Sit down for bit and stretch yourself, if you like, calm down a little bit", "L2": "Try to listen to the thoughts and feelings that arise", "L3": "Are you responding to yourself in a way that you would respond to a friend?", "L4": "If the answer is no, imagine your friend in your situation. What you would say to them? What you would do for them?", "OUTRO": "More exercises <a target=\"_blank\" href=\"https://drsoph.com/blog/2018/9/17/3-exercises-in-self-compassion\" target=\"_blank\">can be found here</a> or on <a target=\"_blank\" href=\"https://www.google.com/search?q=self+compassion+exercises&oq=self+compassion+excers&aqs=chrome.1.69i57j0l5.4303j0j7&sourceid=chrome&ie=UTF-8\" target=\"_blank\">google</a>.", "TITLE": "Self Compassion"}, "CUR": {"INTRO": "Isn't procrastination interesting? It doesn't seem to make any sense to do it, since it is not in your long term interest at all. But still everybody does. It helps a lot explore und gain a better understanding on how it works for you personally! Some basic questions to ask yourself could be:", "L1": "What feelings are eliciting your temptation to procrastinate?", "L2": "Where do you feel them in your body?", "L3": "What do they remind you of?", "L4": "What happens to the thought of procrastinating as you observe it? Does it intensify? Dissipate? Cause other emotions to arise?", "L5": "How are the sensations in your body shifting as you continue to rest your awareness on them?", "PROCRASTINATION_TRIGGERS_TEXT": "Another very effective method is to record, what triggered your urge to procrastinate. For example I personally often have the urge to quickly jump to reddit or my favorite news site whenever my browser window comes into focus. Since I started writing down my triggers in a simple empty text document, I became aware of how ingrained this pattern was and it helped me to experiment with different counter measures.", "PROCRASTINATION_TRIGGERS_TITLE": "Writing down your procrastination triggers", "TITLE": "Curiosity"}, "H1": "Cut yourself some slack!", "INTRO": {"AVOIDING": "Avoiding the task", "FEAR": "Fear of failure", "STRESSED": "Stressed about not getting things done", "TITLE": "Intro"}, "P1": "First of all: Relax! Everybody does procrastinate once in a while. And if you're not doing what you should, you should at least enjoy it!", "P2": "Remember: <b>Procrastination is an emotion regulation problem, not a time management problem.</b> This means beating yourself up is not the solution, it is part of the problem. Let that sink in and then check the other tabs here for some further help.", "REFRAME": {"INTRO": "Think about what might be positive about the task despite its flaws.", "TITLE": "Reframing", "TL1": "What might be interesting about it?", "TL2": "What is to gain if you complete it?", "TL3": "How will you feel about it if you complete it?"}, "SPLIT_UP": {"INTRO": "Split up the task into as many small chunks as you can.", "OUTRO": "Done? Then think about it. What would be – strictly theoretical – the first thing you would do <i>if</i> you were to start working on the task? Just think about it...", "TITLE": "Split it up!"}}, "PROJECT": {"D_CREATE": {"CREATE": "Create Project", "EDIT": "Edit Project", "SETUP_CALDAV": "Setup Caldav Integration", "SETUP_GIT": "Setup GitHub Integration", "SETUP_GITEA_PROJECT": "Setup Gitea Integration", "SETUP_GITLAB": "Setup GitLab Integration", "SETUP_JIRA": "Setup Jira Integration", "SETUP_OPEN_PROJECT": "Setup OpenProject Integration", "SETUP_REDMINE_PROJECT": "Setup Redmine Integration"}, "D_DELETE": {"MSG": "Are you sure you want to delete the project \"{{title}}\"?"}, "FORM_BASIC": {"L_ENABLE_BACKLOG": "Enable Project Backlog", "L_IS_HIDDEN_FROM_MENU": "Hide project from menu", "L_TITLE": "Project Name", "TITLE": "Basic Settings"}, "FORM_THEME": {"D_IS_DARK_THEME": "Won`t be used if system supports global dark mode.", "HELP": "Theme settings for your project.", "L_BACKGROUND_IMAGE_DARK": "Background Image Url (Dark Theme)", "L_BACKGROUND_IMAGE_LIGHT": "Background Image Url (Light Theme)", "L_COLOR_ACCENT": "Accent Color", "L_COLOR_PRIMARY": "Primary Color", "L_COLOR_WARN": "Warn/Error Color", "L_HUE_ACCENT": "<PERSON><PERSON><PERSON><PERSON> for dark text on accent color background", "L_HUE_PRIMARY": "<PERSON><PERSON><PERSON><PERSON> for dark text on primary color background", "L_HUE_WARN": "T<PERSON><PERSON><PERSON> for dark text on warn color background", "L_IS_AUTO_CONTRAST": "Auto set text colors for best readability", "L_IS_DISABLE_BACKGROUND_GRADIENT": "Disable colored background gradient", "L_IS_REDUCED_THEME": "Use reduced UI (no boxes around tasks)", "L_THEME_COLOR": "Theme Color", "L_TITLE": "Title", "TITLE": "Theme"}, "S": {"ARCHIVED": "Archived Project", "CREATED": "Created project <strong>{{title}}</strong>. You can select it from the menu on the top left.", "DELETED": "Deleted Project", "E_EXISTS": "Project \"{{title}}\" already exists", "E_INVALID_FILE": "Invalid data for project file", "ISSUE_PROVIDER_UPDATED": "Updated project settings for <strong>{{issue<PERSON>roviderKey}}</strong>", "UNARCHIVED": "Unarchived project", "UPDATED": "Updated project settings"}}, "QUICK_HISTORY": {"NO_DATA": "No data for current year", "PAGE_TITLE": "Quick History", "WEEK_TITLE": "Week {{nr}} ({{timeSpent}})"}, "REDMINE": {"DIALOG_INITIAL": {"TITLE": "Setup Redmine for Project"}, "FORM": {"API_KEY": "API access key", "HOST": "Host (e.g.: https://redmine.org)", "PROJECT_ID": "Project Identifier", "PROJECT_ID_DESCRIPTION": "Can be found as part of the url, when viewing the project in the Browser.", "SCOPE": "<PERSON><PERSON>", "SCOPE_ALL": "All", "SCOPE_ASSIGNED": "Assigned to me", "SCOPE_CREATED": "Created by me"}, "FORM_SECTION": {"HELP": "<p>Here you can configure SuperProductivity to list open Redmine (either its the online version or a self-hosted instance) issues for a specific project in the task creation panel in the daily planning view. They will be listed as suggestions and will provide a link to the issue as well as more information about it.</p><p>In addition you can automatically import all open issues.</p>", "TITLE": "Redmine"}, "ISSUE_CONTENT": {"AUTHOR": "Author", "DESCRIPTION": "Description", "MARK_AS_CHECKED": "Mark updates as checked", "PRIORITY": "Priority", "STATUS": "Status"}, "S": {"ERR_UNKNOWN": "Redmine: Unknown error {{statusCode}} {{errorMsg}}."}}, "REMINDER": {"COUNTDOWN_BANNER": {"HIDE": "<PERSON>de", "START_NOW": "Start now", "TXT": "<strong>{{title}}</strong> starts at <strong>{{start}}</strong>!", "TXT_MULTIPLE": "<strong>{{title}}</strong> starts at <strong>{{start}}</strong>!<br> (and {{nrOfOtherBanners}} other tasks are due)"}, "S_ACTIVE_TASK_DUE": "The task you are currently working on is now due!<br/> ({{title}})", "S_REMINDER_ERR": "Error for reminder interface"}, "SCHEDULE": {"CONTINUED": "continued", "D_INITIAL": {"TEXT": "<p>The Schedule should provide you with a better picture of how one's planned tasks play out over time. It is automatically generated from your Tasks and requires only <strong>time estimates on them to work</strong>.</p><p>Two things are distinguished: <strong>Scheduled Tasks</strong>, which are shown at their planned time and <strong>Regular Tasks</strong>, which should flow around those fixed events.</p><p>If you provide a work start and end time (recommended) regular tasks will never show up outside of these boundaries.</p>", "TITLE": "Schedule"}, "END": "Work End", "LUNCH_BREAK": "Lunch Break", "MONTH": "Month", "NO_TASKS": "Currently there are no tasks. Please add some tasks via the + Button in the top bar.", "NOW": "Now", "PLAN_END_DAY": "Plan at end of day", "PLAN_START_DAY": "Plan at start of day", "START": "Work Start", "TASK_PROJECTION_INFO": "Future projection of a scheduled repeatable task", "WEEK": "Week"}, "SEARCH_BAR": {"INFO": "Start typing to search for tasks", "INFO_ARCHIVED": "Click on the archive icon to search for normal tasks", "NO_RESULTS": "No tasks found matching your search", "PLACEHOLDER": "Search for task or task description", "PLACEHOLDER_ARCHIVED": "Search for archived tasks", "TOO_MANY_RESULTS": "Too many results, please narrow your search"}, "SIMPLE_COUNTER": {"D_CONFIRM_REMOVE": {"MSG": "Deleting a simple counter, will also delete all past data tracked on it. Are you sure you want to proceed?", "OK": "Do it!"}, "D_EDIT": {"CURRENT_STREAK": "Current Streak", "DAILY_GOAL": "Daily Goal", "DAYS": "Days", "L_COUNTER": "Count for Today"}, "FORM": {"ADD_NEW": "Add simple counter/ habit", "HELP": "Here you can configure simple buttons which will appear at the top right. They can either be timers or just a simple counter, which is counted up, by clicking on it. By enabling 'Track Streaks' you can use these for habit tracking.", "L_COUNTDOWN_DURATION": "Countdown duration", "L_DAILY_GOAL": "Daily goal for successful streak", "L_ICON": "Icon", "L_ICON_ON": "Icon when toggled", "L_IS_ENABLED": "Enabled", "L_TITLE": "Title", "L_TRACK_STREAKS": "Track Streaks", "L_TYPE": "Type", "L_WEEKDAYS": "Weekdays to check for streak", "TITLE": "Simple Counters & Habit Tracking", "TYPE_CLICK_COUNTER": "Click Counter", "TYPE_REPEATED_COUNTDOWN": "Repeated Countdown", "TYPE_STOPWATCH": "Stopwatch"}, "S": {"GOAL_REACHED_1": "You reached your goal for today!", "GOAL_REACHED_2": "Current streak duration: "}}, "SYNC": {"A": {"ARCHIVE_ONLY_UPLOADED": "Your data was only partially uploaded. Please try again later! Otherwise you will not be able to sync your data to other devices.", "POSSIBLE_LEGACY_DATA": "Super Productivity has improved syncing by now using two separate files rather than a single one, allowing for much less data transfer. It is recommended to update all instances of Super Productivity and to first sync data from the app instance where the data is newest. If this is the data from your local device then please ignore this warning and just proceed to upload it by confirming the next dialog.", "REMOTE_MODEL_VERSION_NEWER": "The remote model version is newer than the local one. Please update your local app to the newest version!"}, "C": {"EMPTY_SYNC": "You're trying to sync an empty data object. If you are trying to setup syncing from a new app instance, just press OK to load the data from the server. Otherwise, please check your data.", "FORCE_UPLOAD": "Forcing an upload of your data could lead to data loss. Continue?", "FORCE_UPLOAD_AFTER_ERROR": "An Error occurred while uploading your local data. Try to force the update?", "MIGRATE_LEGACY": "Legacy data detected while importing, do you want to try to migrate it?", "NO_REMOTE_DATA": "No remote data found. Upload local to Remote?", "TRY_LOAD_REMOTE_AGAIN": "Try to re-load data from remote once again?", "UNABLE_TO_LOAD_REMOTE_DATA": "Unable to load data from remote. Do you want to try to overwrite the remote data with your local data? All remote data will be lost in the process."}, "D_AUTH_CODE": {"FOLLOW_LINK": "Please open the following link and copy the auth code provided there into the input field below.", "GET_AUTH_CODE": "Get Authorization Code", "L_AUTH_CODE": "Enter Auth Code", "TITLE": "Login: {{provider}}"}, "D_CONFLICT": {"COMPARISON_RESULT": "Comparison Result", "LAMPORT_CLOCK": "Revision", "LAST_CHANGE": "Last Change:", "LAST_SYNC": "Last Sync:", "LAST_SYNCED": "Last Synced", "LAST_WRITE": "Last Write", "LOCAL": "Local", "LOCAL_REMOTE": "Local -> Remote", "NEVER": "Never", "REMOTE": "Remote", "TEXT": "<p>Update from remote. Both local and remote data seem to be modified.</p>", "TIMESTAMP": "Timestamp", "TITLE": "Sync: Conflicting Data", "USE_LOCAL": "Keep local", "USE_REMOTE": "Keep remote", "ADDITIONAL_INFO": "Additional Info", "VECTOR_COMPARISON_CONCURRENT": "Concurrent (True Conflict)", "VECTOR_COMPARISON_EQUAL": "Equal", "VECTOR_COMPARISON_LOCAL_GREATER": "Local > Remote", "VECTOR_COMPARISON_LOCAL_LESS": "Local < Remote", "DATE": "Date", "TIME": "Time", "VECTOR_CLOCK": "Vector Clock", "RESULT": "Result"}, "D_DECRYPT_ERROR": {"BTN_OVER_WRITE_REMOTE": "Change & Overwrite Remote", "CHANGE_PW_AND_DECRYPT": "Change & Attempt Decrypt", "P1": "Your remote data is encrypted and decryption failed. Please enter the correct password!", "P2": "Or you can also change your password, which will overwrite all remote data.", "PASSWORD": "Password"}, "D_INCOMPLETE_SYNC": {"BTN_CLOSE_APP": "Close App", "BTN_DOWNLOAD_BACKUP": "Download Local Backup", "BTN_FORCE_UPLOAD": "Force Upload Local", "P1": "The remote sync data is incoherent!", "P2": "Affected model:", "P3": "You have 2 Options:", "P4": "1. Go to your other device and attempt do a complete sync there.", "P5": "2. Overwrite the remote data with your local data. All remote changes will be lost!", "P6": "Creating a backup of the data you overwrite is recommended!!!", "T1": "Last sync was incomplete!", "T2": "Your archive data was not properly uploaded during last sync:", "T3": "You have 2 Options:", "T4": "1. Go to your other device and complete the sync there.", "T5": "2. Or you can overwrite the remote data with your local one. All remote changes will\n    be lost!", "T6": "Creating a backup of the data you overwrite is recommended!!!"}, "D_INITIAL_CFG": {"SAVE_AND_ENABLE": "Save & Enable Sync", "TITLE": "Configure Sync"}, "D_PERMISSION": {"DISABLE_SYNC": "Disable sync", "PERM_FILE": "Give permission", "TEXT": "<p>Your file permission for local syncing has been revoked.</p>", "TITLE": "Sync: Local file permission denied"}, "FORM": {"DROPBOX": {"L_ACCESS_TOKEN": "Access Token (generated from Auth Code)"}, "GOOGLE": {"L_SYNC_FILE_NAME": "Sync File Name"}, "L_ENABLE_COMPRESSION": "Enable Compression (faster data transfer)", "L_ENABLE_ENCRYPTION": "Enable end-to-end encryption (experimental) – Make your data inaccessible for your sync provider", "L_ENABLE_SYNCING": "Enable Syncing", "L_ENCRYPTION_NOTES": "IMPORTANT NOTES: You will need to set the same password on your other devices <strong>BEFORE</strong> the next sync for everything to work. Please select a password that is secure. Please also note that <strong>you will NOT be able to access your data, if you forget this password. There is NO recovery possible</strong>, since only you have the key. The encryption will probably be good enough to thwart of most attackers, but <strong>there is no guarantee.</strong>", "L_ENCRYPTION_PASSWORD": "Encryption Password (DO NOT FORGET)", "L_SYNC_INTERVAL": "Sync Interval", "L_SYNC_PROVIDER": "Sync Provider", "LOCAL_FILE": {"L_SYNC_FILE_PATH_PERMISSION_VALIDATION": "Needs file access permission", "L_SYNC_FOLDER_PATH": "Sync folder path"}, "TITLE": "Sync", "WEB_DAV": {"CORS_INFO": "<strong>Making it work in the browser:</strong> To make this work in the browser you need to whitelist Super Productivity for CORS requests for your Nextcloud instance. This can have negative security implications! Please <a href='https://github.com/nextcloud/server/issues/3131'>refer to this thread for more information</a>. One approach to make this work on mobile is whitelisting \"https://app.super-productivity.com\" via the nextcloud app <a href='https://apps.nextcloud.com/apps/webapppassword'>webapppassword<a>. Use at your own risk!</p>", "L_BASE_URL": "Base Url", "L_PASSWORD": "Password", "L_SYNC_FOLDER_PATH": "Sync Folder Path", "L_USER_NAME": "Username"}}, "S": {"ALREADY_IN_SYNC": "Already in sync", "ALREADY_IN_SYNC_NO_LOCAL_CHANGES": "No local changes – Already in sync", "BTN_CONFIGURE": "Configure", "BTN_FORCE_OVERWRITE": "Force Overwrite", "ERROR_DATA_IS_CURRENTLY_WRITTEN": "Remote Data is currently being written", "ERROR_FALLBACK_TO_BACKUP": "Something went wrong while importing the data. Falling back to local backup.", "ERROR_INVALID_DATA": "Error while syncing. Invalid data", "ERROR_NO_REV": "No valid rev for remote file", "ERROR_UNABLE_TO_READ_REMOTE_DATA": "Error while syncing. Unable to read remote data. Maybe you enabled encryption and your local password does not match the one used to encrypt the remote data?", "IMPORTING": "Importing data", "INCOMPLETE_CFG": "Authentication for sync failed. Please check your Config!", "INITIAL_SYNC_ERROR": "Initial Sync failed", "SUCCESS_DOWNLOAD": "Synced data from remote", "SUCCESS_IMPORT": "Data imported", "SUCCESS_VIA_BUTTON": "Data successfully synced", "UNKNOWN_ERROR": "Unknown Sync Error: {{err}}", "UPLOAD_ERROR": "Unknown Upload Error (Settings correct?): {{err}}"}}, "TAG": {"D_CREATE": {"CREATE": "Create Tag", "EDIT": "Edit Tag"}, "D_DELETE": {"CONFIRM_MSG": "Do you really want to delete the tag \"{{tagName}}\"? It will be removed from all tasks. This cannot be undone."}, "D_EDIT": {"ADD": "Add tags for \"{{title}}\"", "EDIT": "Edit tags for \"{{title}}\"", "LABEL": "Tags"}, "FORM_BASIC": {"L_COLOR": "Color (if undefined primary theme color is used)", "L_ICON": "Icon", "L_TITLE": "Tag Name", "TITLE": "Basic Settings"}, "S": {"UPDATED": "Tag Settings were updated"}, "TTL": {"ADD_NEW_TAG": "Add new Tag"}}, "TASK": {"ADD_TASK_BAR": {"ADD_EXISTING_TASK": "Add existing task \"{{taskTitle}}\"", "ADD_ISSUE_TASK": "Add issue #{{issueNr}} from {{issueType}}", "ADD_TASK_TO_BOTTOM_OF_BACKLOG": "Add task to bottom of backlog", "ADD_TASK_TO_BOTTOM_OF_TODAY": "Add task to bottom of list", "ADD_TASK_TO_TOP_OF_BACKLOG": "Add task to top of backlog", "ADD_TASK_TO_TOP_OF_TODAY": "Add task to top of list", "CREATE_TASK": "Create new task", "EXAMPLE": "Example: \"Some task title @fri 4pm +projectName #some-tag #some-other-tag 10m/3h\"", "START": "Press enter one more time to start", "TOGGLE_ADD_TO_BACKLOG_TODAY": "Toggle adding task to backlog / today's list'", "TOGGLE_ADD_TOP_OR_BOTTOM": "Toggle adding task to top & bottom of list"}, "ADDITIONAL_INFO": {"ADD_ATTACHMENT": "Add attachment", "ADD_SUB_TASK": "Add Sub Task", "ATTACHMENTS": "Attachments ({{nr}})", "DUE": "Planned at", "FROM_PARENT": "(from Parent)", "LOCAL_ATTACHMENTS": "Local Attachments", "NOTES": "Description", "PARENT": "Parent", "REMINDER": "Reminder", "REPEAT": "Repeat", "SCHEDULE_TASK": "Schedule Task", "SUB_TASKS": "Sub Tasks ({{nr}})", "TIME": "Time", "TITLE_PLACEHOLDER": "Enter a title"}, "B": {"ADD_HALF_HOUR": "Add 1/2 hour", "ESTIMATE_EXCEEDED": "Time estimate exceeded for  \"{{title}}\""}, "CMP": {"ADD_SUB_TASK": "Add sub task", "ADD_TO_MY_DAY": "Add to Today", "ADD_TO_PROJECT": "Add to a Project", "CONVERT_TO_PARENT_TASK": "Convert to parent Task", "DELETE": "Delete Task", "DELETE_REPEAT_INSTANCE": "Delete repeated Task Instance", "DROP_ATTACHMENT": "Drop here to attach to \"{{title}}\"", "EDIT_SCHEDULED": "Reschedule", "EDIT_TAGS": "Edit tags", "EDIT_TASK_TITLE": "Edit title", "FOCUS_SESSION": "Start Focus Session", "MARK_DONE": "Mark as done", "MARK_UNDONE": "Mark as undone", "MOVE_TO_BACKLOG": "Move to backlog", "MOVE_TO_OTHER_PROJECT": "Move to Project", "MOVE_TO_REGULAR": "Move to regular list", "MOVE_TO_TOP": "Move to Top", "OPEN_ATTACH": "Attach file or link", "OPEN_ISSUE": "Open in browser", "OPEN_TIME": "Time Tracking", "REMOVE_FROM_MY_DAY": "Remove from Today", "REPEAT_EDIT": "Edit repeat task config", "SCHEDULE": "Schedule task", "SHOW_UPDATES": "Show updates", "TOGGLE_ATTACHMENTS": "Show/Hide attachments", "TOGGLE_DETAIL_PANEL": "Show/Hide additional info", "TOGGLE_DONE": "Mark as done/undone", "TOGGLE_SUB_TASK_VISIBILITY": "Toggle sub task visibility", "TOGGLE_TAGS": "Toggle Tags", "TRACK_TIME": "Start tracking time", "TRACK_TIME_STOP": "Pause tracking time", "UNSCHEDULE_TASK": "Unschedule task", "UPDATE_ISSUE_DATA": "Update issue data"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAG": {"MSG": "Do you want to create the new tag {{tagsTxt}}?", "OK": "Create tag"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAGS": {"MSG": "Do you want to create the new tags {{tagsTxt}}?", "OK": "Create tags"}, "D_REMINDER_VIEW": {"ADD_ALL_TO_TODAY": "Add all to today", "ADD_TO_TODAY": "Add to today", "DONE": "Done", "DUE_TASK": "Reminder for planned task", "DUE_TASKS": "Reminder for planned tasks", "FOR_CURRENT": "Task is due. Do you want to start working on it?", "FOR_OTHER": "Task is due. Do you want to start working on it?", "FROM_PROJECT": "From Project: \"{{title}}\"", "FROM_TAG": "From Tag: \"{{title}}\"", "RESCHEDULE_EDIT": "Edit (Re-schedule)", "RESCHEDULE_UNTIL_TOMORROW": "Re-schedule for tomorrow", "SNOOZE": "Snooze", "SNOOZE_ALL": "Snooze all", "START": "Start", "SWITCH_CONTEXT_START": "Switch Context & Start", "UNSCHEDULE": "Unschedule", "UNSCHEDULE_ALL": "Unschedule all", "DISMISS_REMINDER_KEEP_TODAY": "<PERSON><PERSON><PERSON> (Keep in Today)", "DISMISS_ALL_REMINDERS_KEEP_TODAY": "Dismiss All Reminders (Keep in Today)"}, "D_SCHEDULE_TASK": {"MOVE_TO_BACKLOG": "Move task to backlog until scheduled", "QA_NEXT_MONTH": "Schedule next Month", "QA_NEXT_WEEK": "Schedule next Week", "QA_REMOVE_TODAY": "Remove task from today", "QA_TODAY": "Schedule Today", "QA_TOMORROW": "Schedule Tomorrow", "REMIND_AT": "Remind at", "RO_1H": "1 hour before it starts", "RO_5M": "5 minutes before it starts", "RO_10M": "10 minutes before it starts", "RO_15M": "15 minutes before it starts", "RO_30M": "30 minutes before it starts", "RO_NEVER": "Never", "RO_START": "when it starts", "SCHEDULE": "Schedule", "UNSCHEDULE": "Remove"}, "D_TIME": {"ADD_FOR_OTHER_DAY": "Add time spent for other day", "DELETE_FOR": "Delete entry for day", "ESTIMATE": "Estimate", "TIME_SPENT": "Time Spent", "TIME_SPENT_ON": "Time Spent {{date}}", "TITLE": "Duration"}, "D_TIME_FOR_DAY": {"ADD_ENTRY_FOR": "Add new entry for {{date}}", "DATE": "Date for new entry", "HELP": "Examples:<br> 30m => 30 minutes<br> 2h => 2 hours<br> 2h 30m => 2 hours and 30 minutes", "TINE_SPENT": "Time Spent", "TITLE": "Add for Day"}, "N": {"ESTIMATE_EXCEEDED": "Time estimate exceeded!", "ESTIMATE_EXCEEDED_BODY": "You exceeded your estimated time for \"{{title}}\"."}, "S": {"CANNOT_ASSIGN_PROJECT_FOR_REPEATABLE_TASK": "Cannot assign project via short syntax for repeatable tasks!", "CREATED_FOR_PROJECT": "Created task \"{{taskTitle}}\" for project \"{{projectTitle}}\"", "CREATED_FOR_PROJECT_ACTION": "Go to Project", "DELETED": "Deleted task \"{{title}}\"", "FOUND_MOVE_FROM_BACKLOG": "Moved task <strong>{{title}}</strong> from backlog to todays task list", "FOUND_MOVE_FROM_OTHER_LIST": "Added task <strong>{{title}}</strong> from <strong>{{contextTitle}}</strong> to current list", "FOUND_RESTORE_FROM_ARCHIVE": "Restored task <strong>{{title}}</strong> related to issue from archive", "LAST_TAG_DELETION_WARNING": "You're trying to remove the last tag of a non project task. This is not allowed!", "MOVED_TO_ARCHIVE": "Moved {{nr}} tasks to archive", "MOVED_TO_PROJECT": "Moved task \"{{taskTitle}}\" to project \"{{projectTitle}}\"", "MOVED_TO_PROJECT_ACTION": "Go to Project", "REMINDER_ADDED": "Scheduled <strong>{{title}}</strong> at <strong>{{date}}</strong>", "REMINDER_DELETED": "Deleted reminder for task", "REMINDER_UPDATED": "Updated reminder for task \"{{title}}\"", "TASK_CREATED": "Created task \"{{title}}\""}, "SELECT_OR_CREATE": "Select or create task", "SUMMARY_TABLE": {"ESTIMATE": "Estimate", "SPENT_TODAY": "Spent Today", "SPENT_TOTAL": "Spent Total", "TASK": "Task", "TOGGLE_DONE": "un-/mark as done"}}, "TASK_REPEAT": {"ADD_INFO_PANEL": {"CUSTOM": "Custom repeat config", "CUSTOM_AND_TIME": "Custom, {{timeStr}}", "CUSTOM_WEEKLY": "{{daysStr}}", "CUSTOM_WEEKLY_AND_TIME": "{{daysStr}}, {{timeStr}}", "DAILY": "Every day", "DAILY_AND_TIME": "Every day, {{timeStr}}", "EVERY_X_DAILY": "Every {{x}} days", "EVERY_X_DAILY_AND_TIME": "Every {{x}} days, {{timeStr}}", "EVERY_X_MONTHLY": "Every {{x}} month", "EVERY_X_MONTHLY_AND_TIME": "Every {{x}} months, {{timeStr}}", "EVERY_X_YEARLY": "Every {{x}} years", "EVERY_X_YEARLY_AND_TIME": "Every {{x}} years, {{timeStr}}", "MONDAY_TO_FRIDAY": "<PERSON><PERSON><PERSON><PERSON>", "MONDAY_TO_FRIDAY_AND_TIME": "<PERSON><PERSON><PERSON><PERSON>, {{timeStr}}", "MONTHLY_CURRENT_DATE": "Monthly on {{dateDayStr}}th", "MONTHLY_CURRENT_DATE_AND_TIME": "Monthly on {{dateDayStr}}th, {{timeStr}}", "WEEKLY_CURRENT_WEEKDAY": "Weekly on {{weekdayStr}}", "WEEKLY_CURRENT_WEEKDAY_AND_TIME": "Weekly on {{weekdayStr}}, {{timeStr}}", "YEARLY_CURRENT_DATE": "Yearly on {{dayAndMonthStr}}", "YEARLY_CURRENT_DATE_AND_TIME": "Yearly {{dayAndMonthStr}}, {{timeStr}}"}, "D_CONFIRM_MOVE_TO_PROJECT": {"MSG": "There are {{tasksNr}} instances created for this repeatable task. Do you want to move all of them to the project \"{{projectName}}\"?", "OK": "Update all instances"}, "D_CONFIRM_REMOVE": {"MSG": "Removing the repeat config will convert all previous instances of this task to just regular tasks. Are you sure you want to proceed", "OK": "Remove completely"}, "D_CONFIRM_UPDATE_INSTANCES": {"CANCEL": "Only future tasks", "MSG": "There are {{tasksNr}} instances created for this repeatable task. Do you want to update all of them with the new defaults or just future tasks?", "OK": "Update all instances"}, "D_EDIT": {"ADD": "Add Repeat Task Config", "ADVANCED_CFG": "Advanced configuration", "EDIT": "Edit Repeat Task Config", "HELP1": "Repeating tasks are meant for daily chores, e.g.: \"Organization\", \"Daily Meeting\", \"Code Review\", \"Checking emails\" or similar tasks which are likely to occur again and again.", "HELP2": "Once configured a repeating task will be recreated on every day selected below as soon as you open your project and will be automatically marked as completed at the end of the day. They will be handled as different instances. So you can freely add sub tasks etc.", "HELP3": "Tasks imported from Jira or Git Issues cannot be repeated.", "HELP4": "A note about the order field: Referring to the creation order repeatable tasks. Only in effect for repeatable tasks that are created at the same time. A lower value means a task will be higher up the list, a higher number that it will be further down. A value greater than 0 means items are created at the bottom of normal tasks.", "TAG_LABEL": "Tags to add"}, "F": {"C_DAY": "Day", "C_MONTH": "Month", "C_WEEK": "Week", "C_YEAR": "Year", "DEFAULT_ESTIMATE": "Default Estimate", "FRIDAY": "Friday", "IS_ADD_TO_BOTTOM": "Move task to bottom of list", "MONDAY": "Monday", "NOTES": "Default notes", "ORDER": "Order", "ORDER_DESCRIPTION": "Creation order of repeatable tasks. Only affects repeatable tasks created at the same time. A lower value means a task will be created higher up the list, a higher number that it will be further down. A value greater than 0 means items are created at the bottom of normal tasks.", "Q_CUSTOM": "Custom repeat config", "Q_DAILY": "Every day", "Q_MONDAY_TO_FRIDAY": "Every monday to friday", "Q_MONTHLY_CURRENT_DATE": "Every month on the {{dateDayStr}}th", "Q_WEEKLY_CURRENT_WEEKDAY": "Every week on {{weekdayStr}}", "Q_YEARLY_CURRENT_DATE": "Every year on the {{dayAndMonthStr}}", "QUICK_SETTING": "Repeat Config", "REMIND_AT": "Remind at", "REMIND_AT_PLACEHOLDER": "Select when to remind", "REPEAT_CYCLE": "Repeat cycle", "REPEAT_EVERY": "Repeat every", "SATURDAY": "Saturday", "START_DATE": "Start date", "START_TIME": "Scheduled start time", "START_TIME_DESCRIPTION": "E.g. 15:00. Leave blank for an all day task", "SUNDAY": "Sunday", "THURSDAY": "Thursday", "TITLE": "Title for task", "TUESDAY": "Tuesday", "WEDNESDAY": "Wednesday"}}, "TASK_VIEW": {"CUSTOMIZER": {"ENTER_PROJECT": "Enter project", "ENTER_TAG": "Enter tag", "ESTIMATED_TIME": "Estimated Time", "FILTER_BY": "Filter <PERSON>", "FILTER_DEFAULT": "No Filter", "FILTER_ESTIMATED_TIME": "Estimated Time", "FILTER_PROJECT": "Project", "FILTER_SCHEDULED_DATE": "Scheduled Date", "FILTER_TAG": "Tag", "FILTER_TIME_SPENT": "Time Spent", "GROUP_BY": "Group By", "GROUP_DEFAULT": "No Group", "GROUP_PROJECT": "Project", "GROUP_SCHEDULED_DATE": "Scheduled Date", "GROUP_TAG": "Tag", "RESET_ALL": "Reset All", "SCHEDULED_DEFAULT": "Any Date", "SCHEDULED_NEXT_MONTH": "Next Month", "SCHEDULED_NEXT_WEEK": "Next Week", "SCHEDULED_THIS_MONTH": "This Month", "SCHEDULED_THIS_WEEK": "This Week", "SCHEDULED_TODAY": "Today", "SCHEDULED_TOMORROW": "Tomorrow", "SORT_BY": "Sort By", "SORT_CREATION_DATE": "Creation Date", "SORT_DEFAULT": "<PERSON><PERSON><PERSON>", "SORT_NAME": "Name", "SORT_SCHEDULED_DATE": "Scheduled Date", "TIME_1HOUR": "> 1 Hour", "TIME_2HOUR": "> 2 Hours", "TIME_10MIN": "> 10 Minutes", "TIME_30MIN": "> 30 Minutes", "TIME_DEFAULT": "Any Duration", "TIME_SPENT": "Time Spent", "TITLE": "Customize Task View"}}, "TIME_TRACKING": {"B": {"ALREADY_DID": "I already did", "SNOOZE": "Snooze {{time}}"}, "B_TTR": {"ADD_TO_TASK": "Add to Task", "MSG": "You have not been tracking time for {{time}}"}, "D_IDLE": {"ADD_ENTRY": "Add entry for tracking", "BREAK": "Break", "CREATE_AND_TRACK": "<em>Create</em> and track to", "IDLE_FOR": "You have been idle for:", "RESET_BREAK_REMINDER_TIMER": "Reset break reminder timer", "SIMPLE_CONFIRM_COUNTER_CANCEL": "<PERSON><PERSON>", "SIMPLE_CONFIRM_COUNTER_OK": "Track", "SIMPLE_COUNTER_CONFIRM_TXT": "You selected skip, but activated {{nr}} simple counter button(s). Do you want to track the idle time to them?", "SIMPLE_COUNTER_TOOLTIP": "Click to track to {{title}}", "SIMPLE_COUNTER_TOOLTIP_DISABLE": "Click to NOT track to {{title}}", "SKIP": "<PERSON><PERSON>", "SPLIT_TIME": "Split time into multiples tasks and breaks", "TASK": "Task", "TRACK_TO": "Track to"}, "D_TRACKING_REMINDER": {"CREATE_AND_TRACK": "<em>Create</em> and track to", "IDLE_FOR": "You have been idle for:", "NOTIFICATION_TITLE": "Track your time!", "TASK": "Task", "TRACK_TO": "Track to:", "UNTRACKED_TIME": "Untracked time:"}}, "WORKLOG": {"CMP": {"DAYS_WORKED": "Days, worked:", "MONTH_WORKED": "Month worked:", "REPEATING_TASK": "Repeating task", "RESTORE_TASK_FROM_ARCHIVE": "Restore Task From Archive", "TASKS": "Tasks", "TOTAL_TIME": "Time spent total:", "WEEK_NR": "Week {{nr}}", "WORKED": "Worked"}, "D_CONFIRM_RESTORE": "Are you sure you want to move the task <strong>\"{{title}}\"</strong> into your todays task list?", "D_EXPORT_TITLE": "Worklog Export {{start}}–{{end}}", "D_EXPORT_TITLE_SINGLE": "Worklog Export {{day}}", "EXPORT": {"ADD_COL": "Add column", "COPY_TO_CLIPBOARD": "Copy to clipboard", "DONT_ROUND": "don't round", "EDIT_COL": "Edit column", "GROUP_BY": "Group by", "O": {"DATE": "Date", "ENDED_WORKING": "Ended Working", "ESTIMATE_AS_CLOCK": "Estimate as clock (e.g. 5:23)", "ESTIMATE_AS_MILLISECONDS": "Estimate as milliseconds", "ESTIMATE_AS_STRING": "Estimate as string (e.g. 5h 23m)", "FULL_HALF_HOURS": "full half hours", "FULL_HOURS": "full hours", "FULL_QUARTERS": "full quarters", "NOTES": "Task Descriptions", "PARENT_TASK": "Parent Task", "PARENT_TASK_TITLES_ONLY": "Parent Task Titles only", "PROJECTS": "Project Names", "STARTED_WORKING": "Started Working", "TAGS": "Tags", "TASK_SUBTASK": "Task/Subtask", "TIME_AS_CLOCK": "Time as clock (e.g. 5:23)", "TIME_AS_MILLISECONDS": "Time as milliseconds", "TIME_AS_STRING": "Time as string (e.g. 5h 23m)", "TITLES_AND_SUB_TASK_TITLES": "Titles and Sub Task Titles", "WORKLOG": "Worklog"}, "OPTIONS": "Options", "ROUND_END_TIME_TO": "Round end time to", "ROUND_START_TIME_TO": "Round start time to", "ROUND_TIME_WORKED_TO": "Round time worked to", "SAVE_TO_FILE": "Save to file", "SEPARATE_TASKS_BY": "Separate tasks by", "SHOW_AS_TEXT": "Show as text"}, "WEEK": {"EXPORT": "Export Week Data", "NO_DATA": "No tasks this week yet.", "TITLE": "Title"}}}, "FILE_IMEX": {"DIALOG_CONFIRM_URL_IMPORT": {"INITIATED_MSG": "An automatic data import has been initiated.", "SOURCE_URL_DOMAIN": "Source Domain", "TITLE": "Confirm Data Import from URL", "WARNING_MSG": "Proceeding will overwrite your current application data and configuration with the content from the specified URL. This action cannot be undone.", "WARNING_TITLE": "Warning"}, "EXPORT_DATA": "Export Data", "IMPORT_FROM_FILE": "Import from file", "IMPORT_FROM_URL": "Import from URL", "IMPORT_FROM_URL_DIALOG_DESCRIPTION": "Please enter the full URL of the Super Productivity backup JSON file you want to import.", "IMPORT_FROM_URL_DIALOG_TITLE": "Import from URL", "OPEN_IMPORT_FROM_URL_DIALOG": "Import from URL", "PRIVACY_EXPORT": "Export anonymized data (to <NAME_EMAIL> for debugging)", "S_BACKUP_DOWNLOADED": "Backup downloaded to android documents folder", "S_ERR_IMPORT_FAILED": "Importing data failed", "S_ERR_INVALID_DATA": "Import failed: Invalid JSON", "S_ERR_INVALID_URL": "Import failed: Invalid URL provided", "S_ERR_NETWORK": "Import failed: Network error while fetching data from URL", "S_IMPORT_FROM_URL_ERR_DECODE": "Error: Could not decode the URL parameter for import. Please ensure it is correctly formatted.", "URL_PLACEHOLDER": "Enter URL to import from"}, "G": {"ADD": "Add", "ADVANCED_CFG": "Advanced Config", "CANCEL": "Cancel", "CLOSE": "Close", "CONFIRM": "Confirm", "DELETE": "Delete", "DISMISS": "<PERSON><PERSON><PERSON>", "DO_IT": "Do it!", "DURATION_DESCRIPTION": "e.g. \"5h 23m\" which results in 5 hours in 23 minutes", "EDIT": "Edit", "ENABLED": "Enabled", "EXAMPLE_VAL": "e.g. 32m", "EXTENSION_INFO": "Please <a target=\"_blank\" href=\"https://chrome.google.com/webstore/detail/super-productivity/ljkbjodfmekklcoibdnhahlaalhihmlb\"> download the chrome extension</a> in order to allow communication with the Jira Api and Idle Time Handling. Note that this doesn't work for mobile. <strong>Without the extension the features will not work!!</strong>", "HIDE": "<PERSON>de", "ICON_INP_DESCRIPTION": "All utf-8 emojis are also supported!", "INBOX_PROJECT_TITLE": "Inbox", "LOGIN": "<PERSON><PERSON>", "LOGOUT": "Logout", "MINUTES": "{{m}} minutes", "MOVE_BACKWARD": "Move Backward", "MOVE_FORWARD": "Move Forward", "NEXT": "Next", "NO_CON": "You are currently offline. Please reconnect to the internet.", "NONE": "None", "OK": "Ok", "OVERDUE": "Overdue", "PREVIOUS": "Previous", "REMOVE": "Remove", "RESET": "Reset", "SAVE": "Save", "SUBMIT": "Submit", "TITLE": "Title", "TODAY_TAG_TITLE": "Today", "TRACKING_INTERVAL_DESCRIPTION": "Track time using this interval in milliseconds. You might want to change this to reduce disk writes. See issue #2355.", "UNDO": "Undo", "UPDATE": "Update", "WITHOUT_PROJECT": "Without Project", "YESTERDAY": "Yesterday"}, "GCF": {"AUTO_BACKUPS": {"HELP": "Auto save all data to your app folder in order to have it ready in case something goes wrong.", "LABEL_IS_ENABLED": "Enable automatic backups", "LOCATION_INFO": "Backups are saved to:", "TITLE": "Automatic Backups"}, "CALENDARS": {"BROWSER_WARNING": "Due to cross origin restrictions <b>this will likely NOT work with the Browser version of Super Productivity.<br /> Please <a href=\"https://super-productivity.com/download/\">download the desktop version</a> to use this feature!</b>", "CAL_PATH": "Url of the iCal source", "CAL_PROVIDERS": "Calendar providers (experimental and optional)", "CHECK_UPDATES": "Check for remote updates every X", "DEFAULT_PROJECT": "Default Project for added calendar tasks", "HELP": "You can integrate calendars to be reminded and add them as tasks within Super Productivity. The integration works by using the iCal format. For this to work your calendars need to be accessible either over the internet or via file system.", "SHOW_BANNER_THRESHOLD": "Show a notification X before the event (blank for disabled)"}, "EVALUATION": {"IS_HIDE_EVALUATION_SHEET": "Hide evaluation sheet on daily summary", "TITLE": "Evaluation & Metrics"}, "FOCUS_MODE": {"HELP": "Focus Mode opens up a distraction free screen to help you focus on your current task.", "L_ALWAYS_OPEN_FOCUS_MODE": "Always open focus mode, when tracking", "L_SKIP_PREPARATION_SCREEN": "Skip preparation screen (stretching etc.)", "TITLE": "Focus Mode"}, "IDLE": {"HELP": "<div><p>When idle time handling is enabled a dialog will open after a specified amount of time to check if and on which task you want to track your time, when you have been idle.</p></div>", "IS_ENABLE_IDLE_TIME_TRACKING": "Enable idle time handling", "IS_ONLY_OPEN_IDLE_WHEN_CURRENT_TASK": "Only trigger idle time dialog when a current task is selected", "MIN_IDLE_TIME": "Trigger idle after X", "TITLE": "Idle Handling"}, "IMEX": {"HELP": "<p>Here you can export all your data as a <strong>JSON</strong> for backups, but also to use it in a different context (e.g. you might want to export your projects in the browser and import them into the desktop version). </p> <p>The import expects valid JSON to be copied into the text area. <strong>NOTE: Once you hit the import button all your current settings and data will be overwritten!</strong></p>", "TITLE": "Import/Export"}, "KEYBOARD": {"ADD_NEW_NOTE": "Add new note", "ADD_NEW_TASK": "Add New Task", "APP_WIDE_SHORTCUTS": "Global Shortcuts (application wide)", "COLLAPSE_SUB_TASKS": "Collapse Sub Tasks", "EXPAND_SUB_TASKS": "Expand Sub Tasks", "GLOBAL_ADD_NOTE": "Add new note", "GLOBAL_ADD_TASK": "Add new task", "GLOBAL_SHOW_HIDE": "Show/Hide Super Productivity", "GLOBAL_TOGGLE_TASK_START": "Toggle time tracking for last active task", "GO_TO_DAILY_AGENDA": "Go to Agenda", "GO_TO_FOCUS_MODE": "Enter Focus Mode", "GO_TO_SCHEDULE": "Go to Schedule", "GO_TO_SCHEDULED_VIEW": "Go to scheduled Tasks", "GO_TO_SETTINGS": "Go to Settings", "GO_TO_WORK_VIEW": "Go to Work View and focus first task", "HELP": "<p>Here you can configure all keyboard shortcuts.</p> <p>Click on the text input and enter the desired keyboard combination. Hit enter to save and Escape to abort.</p> <p>There are three types of shortcuts:</p> <ul> <li> <strong>Global shortcuts:</strong> When the app is running it will trigger the action from every other application. </li> <li> <strong>Application level shortcuts:</strong> Will trigger from every screen of the application, but not if you're currently editing a text field. </li> <li> <strong>Task level shortcuts:</strong> They will only trigger if you have selected a task via mouse or keyboard and usually trigger an action specifically related to that one task. </li> </ul><p>You can <strong>press Escape to remove a shortcut.</strong>", "MOVE_TASK_DOWN": "Move Task down in List", "MOVE_TASK_TO_BOTTOM": "Move Task to the bottom of the List", "MOVE_TASK_TO_TOP": "Move Task to the top of the List", "MOVE_TASK_UP": "Move Task up in List", "MOVE_TO_BACKLOG": "Move Task to Task Backlog", "MOVE_TO_REGULARS_TASKS": "Move Task to Today\"s Task List", "OPEN_PROJECT_NOTES": "Show/Hide Notes", "SAVE_NOTE": "Save note", "SELECT_NEXT_TASK": "Select next Task", "SELECT_PREVIOUS_TASK": "Select previous Task", "SHOW_SEARCH_BAR": "Show search bar", "SYSTEM_SHORTCUTS": "Global Shortcuts (system wide)", "TASK_ADD_ATTACHMENT": "Attach file or link", "TASK_ADD_SUB_TASK": "Add sub Task", "TASK_DELETE": "Delete Task", "TASK_EDIT_TAGS": "Edit Tags", "TASK_EDIT_TITLE": "Edit Title", "TASK_MOVE_TO_PROJECT": "Open move task to project menu", "TASK_OPEN_CONTEXT_MENU": "Open task context menu", "TASK_OPEN_ESTIMATION_DIALOG": "Edit estimation / time spent", "TASK_PLAN_FORDAY": "Plan for day", "TASK_SCHEDULE": "Schedule Task", "TASK_SHORTCUTS": "Tasks", "TASK_SHORTCUTS_INFO": "The following shortcuts apply for the currently selected task (selected via tab or mouse).", "TASK_TOGGLE_DETAIL_PANEL_OPEN": "Show/Hide additional task info", "TASK_TOGGLE_DONE": "Toggle Done", "TITLE": "Keyboard Shortcuts", "TOGGLE_BACKLOG": "Show/Hide Task Backlog", "TOGGLE_BOOKMARKS": "Show/Hide Bookmark Bar", "TOGGLE_ISSUE_PANEL": "Show/Hide Issue Panel", "TOGGLE_PLAY": "Start/Stop Task", "TOGGLE_SIDE_NAV": "Show & Focus/Hide Sidenav", "TOGGLE_TASK_VIEW_CUSTOMIZER_PANEL": "Toggle Filter/Group/Sort Panel", "TRIGGER_SYNC": "Trigger sync (if configured)", "ZOOM_DEFAULT": "Zoom default (Desktop only)", "ZOOM_IN": "Zoom in (Desktop only)", "ZOOM_OUT": "Zoom out (Desktop only)", "PLUGIN_SHORTCUTS": "Plugin Shortcuts"}, "LANG": {"AR": "عرب<PERSON>", "CZ": "Czech", "DE": "De<PERSON>ch", "EN": "English", "ES": "Español", "FA": "فار<PERSON>ی", "FR": "français", "HR": "Hrvatski", "ID": "Indonesian", "IT": "Italiano", "JA": "日本語", "KO": "한국어", "LABEL": "Please select a language", "NB": "Norsk Bokmål", "NL": "Nederlands", "PL": "Polish", "PT": "Português", "RU": "Russian", "SK": "Slovak", "TITLE": "Language", "TR": "Türkçe", "UK": "Українська", "ZH": "中文(简体)", "ZH_TW": "中文(繁體)"}, "MISC": {"DEFAULT_PROJECT": "Default project to use for tasks if none is specified", "FIRST_DAY_OF_WEEK": "First day of the week", "HELP": "<p><strong>Not seeing Desktop Notifications?</strong> For windows you might want to check System > Notifications & actions and check if the required notifications have been enabled.</p>", "IS_AUTO_ADD_WORKED_ON_TO_TODAY": "Automatically add today tag to worked on tasks", "IS_AUTO_MARK_PARENT_AS_DONE": "Mark parent task as done, when all sub tasks are done", "IS_CONFIRM_BEFORE_EXIT": "Confirm before exiting the app", "IS_CONFIRM_BEFORE_EXIT_WITHOUT_FINISH_DAY": "Confirm before exiting the app without finishing day first", "IS_DARK_MODE": "Dark Mode", "IS_DISABLE_ANIMATIONS": "Disable all animations", "IS_HIDE_NAV": "Hide navigation until main header is hovered (desktop only)", "IS_MINIMIZE_TO_TRAY": "Minimize to tray (desktop only)", "IS_SHOW_TIP_LONGER": "Show productivity tip on app start a little longer", "IS_TRAY_SHOW_CURRENT_COUNTDOWN": "Show current countdown in the tray / status menu (desktop mac only)", "IS_TRAY_SHOW_CURRENT_TASK": "Show current task in the tray / status menu (desktop mac/windows only)", "IS_OVERLAY_INDICATOR_ENABLED": "Enable overlay indicator window (desktop linux/gnome)", "IS_TURN_OFF_MARKDOWN": "Turn off markdown parsing for task notes", "IS_USE_MINIMAL_SIDE_NAV": "Use minimal navigation bar (show only icons)", "START_OF_NEXT_DAY": "Start time of the next day", "START_OF_NEXT_DAY_HINT": "from when (in hour) you want to count the next day has started. default is midnight which is 0.", "TASK_NOTES_TPL": "Task description template", "TITLE": "Misc Settings"}, "POMODORO": {"BREAK_DURATION": "Duration of short breaks", "CYCLES_BEFORE_LONGER_BREAK": "Start longer break after X work sessions", "DURATION": "Duration of work sessions", "HELP": "<p>The pomodoro timer can be configured via a couple of settings. The duration of every work session, the duration of normal breaks, the number of work sessions to run before a longer break is started and the duration of this longer break.</p> <p>You can also set if you want to display your distractions during your pomodoro breaks.</p> <p>Setting \"Pause time tracking on pomodoro break\" will also track your breaks as work time spent on a task. </p> <p>Enabling \"Pause pomodoro session when no active task\" will also pause the pomodoro session, when you pause a task.</p>", "IS_ENABLED": "Enable pomodoro timer", "IS_MANUAL_CONTINUE": "Manually confirm starting next pomodoro session", "IS_MANUAL_CONTINUE_BREAK": "Manually confirm starting next break", "IS_PLAY_SOUND": "Play sound when session is done", "IS_PLAY_SOUND_AFTER_BREAK": "Play sound when break is done", "IS_PLAY_TICK": "Play tick sound every second", "IS_STOP_TRACKING_ON_BREAK": "Stop time tracking for task on break", "LONGER_BREAK_DURATION": "Duration of longer breaks", "TITLE": "Pomodoro Timer"}, "REMINDER": {"COUNTDOWN_DURATION": "Show banner X before the actual reminder", "IS_COUNTDOWN_BANNER_ENABLED": "Show countdown banner before reminders are due", "TITLE": "Reminders"}, "SCHEDULE": {"HELP": "The schedule feature should provide you with a quick overview over how your planned tasks play out over time. You can find it in the left hand menu under <a href='#/schedule'>'Schedule'</a>. ", "L_IS_LUNCH_BREAK_ENABLED": "Enable lunch break", "L_IS_WORK_START_END_ENABLED": "Limit unscheduled task flow to specific work times", "L_LUNCH_BREAK_END": "Lunch break end", "L_LUNCH_BREAK_START": "Lunch break start", "L_WORK_END": "Work Day End", "L_WORK_START": "Work Day Start", "LUNCH_BREAK_START_END_DESCRIPTION": "e.g. 13:00", "TITLE": "Schedule", "WORK_START_END_DESCRIPTION": "e.g. 17:00", "WEEK": "Week", "MONTH": "Month"}, "SHORT_SYNTAX": {"HELP": "<p>Here you can control short syntax options when creating a task</p>", "IS_ENABLE_DUE": "Enable planned at short syntax (@<planned time>)", "IS_ENABLE_PROJECT": "Enable project short syntax (+<Project name>)", "IS_ENABLE_TAG": "Enable tag short syntax (#<Tag>)", "TITLE": "Short Syntax"}, "SOUND": {"BREAK_REMINDER_SOUND": "Take a break reminder sound", "DONE_SOUND": "Task marked done sound", "IS_INCREASE_DONE_PITCH": "Increase pitch for every task done", "TITLE": "Sound", "TRACK_TIME_SOUND": "Track time reminder sound", "VOLUME": "Volume"}, "TAKE_A_BREAK": {"ADD_NEW_IMG": "Add motivational image", "FULL_SCREEN_BLOCKER_DURATION": "Duration to display the fullscreen window (desktop only)", "HELP": "<div> <p>Allows you to configure a reoccurring reminder when you have worked for a specified amount of time without taking a break.</p> <p>You can modify the message displayed. ${duration} will be replaced with the time spent without a break.</p> </div>", "IS_ENABLED": "Enable take a break reminder", "IS_FOCUS_WINDOW": "Focus app window when reminder is active (desktop only)", "IS_FULL_SCREEN_BLOCKER": "Display message in fullscreen window (desktop only)", "IS_LOCK_SCREEN": "Lock screen when a break is due (desktop only)", "MESSAGE": "Take a break message", "MIN_WORKING_TIME": "<PERSON><PERSON> take a break notification after <PERSON> working without one", "MOTIVATIONAL_IMGS": "Motivational images (use web urls)", "NOTIFICATION_TITLE": "Take a break!", "SNOOZE_TIME": "Snooze time when prompted to take a break", "TITLE": "Break Reminder"}, "TIME_TRACKING": {"HELP": "The time tracking reminder is a banner to show up in case you forgot to start time tracking.", "L_DEFAULT_ESTIMATE": "Default time estimate for new tasks", "L_DEFAULT_ESTIMATE_SUB_TASKS": "Default time estimate for new sub tasks", "L_IS_AUTO_START_NEXT_TASK": "Start tracking next task when marking current as done", "L_IS_NOTIFY_WHEN_TIME_ESTIMATE_EXCEEDED": "Notify when time estimate was exceeded", "L_IS_TRACKING_REMINDER_ENABLED": "Tracking Reminder Enabled", "L_IS_TRACKING_REMINDER_FOCUS_WINDOW": "Focus app window when reminder is active (desktop only)", "L_IS_TRACKING_REMINDER_NOTIFY": "Notify when time tracking reminder is shown", "L_IS_TRACKING_REMINDER_SHOW_ON_MOBILE": "Show tracking reminder on mobile app", "L_TRACKING_INTERVAL": "Time tracking interval (EXPERIMENTAL)", "L_TRACKING_REMINDER_MIN_TIME": "Time to wait before showing tracking reminder Banner", "TITLE": "Time Tracking"}}, "GLOBAL_RELATIVE_TIME": {"FUTURE": {"A_DAY": "in a day", "A_MINUTE": "in a minute", "A_MONTH": "in a month", "A_YEAR": "in a year", "AN_HOUR": "in an hour", "DAYS": "in {{count}} days", "FEW_SECONDS": "in a few seconds", "HOURS": "in {{count}} hours", "MINUTES": "in {{count}} minutes", "MONTHS": "in {{count}} months", "YEARS": "in {{count}} years"}, "PAST": {"A_DAY": "a day ago", "A_MINUTE": "a minute ago", "A_MONTH": "a month ago", "A_YEAR": "a year ago", "AN_HOUR": "an hour ago", "DAYS": "{{count}} days ago", "FEW_SECONDS": "a few seconds ago", "HOURS": "{{count}} hours ago", "MINUTES": "{{count}} minutes ago", "MONTHS": "{{count}} months ago", "YEARS": "{{count}} years ago"}}, "GLOBAL_SNACK": {"COPY_TO_CLIPPBOARD": "Copied to clipboard", "ERR_COMPRESSION": "Error for compression interface", "FILE_DOWNLOADED": "{{fileName}} downloaded", "FILE_DOWNLOADED_BTN": "Open folder", "NAVIGATE_TO_TASK_ERR": "Could not focus to task. Did you delete it?", "PERSISTENCE_DISALLOWED": "Data will be not persisted permanently. Be aware that this can lead to data loss!!", "PERSISTENCE_ERROR": "Error when requesting to persist data: {{err}}", "RUNNING_X": "Running \"{{str}}\".", "SHORTCUT_WARN_OPEN_BOOKMARKS_FROM_TAG": "{{key<PERSON><PERSON><PERSON>}} pressed, but open bookmarks shortcut is only available when in project context."}, "GPB": {"ASSETS": "Loading assets...", "DBX_DOWNLOAD": "Dropbox: Download file...", "DBX_GEN_TOKEN": "Dropbox: Generate token...", "DBX_META": "Dropbox: Get file meta...", "DBX_UPLOAD": "Dropbox: Upload file...", "GITHUB_LOAD_ISSUE": "GitHub: Load issue data...", "JIRA_LOAD_ISSUE": "Jira: Load issue data...", "SYNC": "Syncing data...", "UNKNOWN": "Loading remote data", "WEB_DAV_DOWNLOAD": "WebDAV: Downloading data...", "WEB_DAV_UPLOAD": "WebDAV: Uploading data..."}, "MH": {"ADD_NEW_TASK": "Add new Task", "ALL_PLANNED_LIST": "Planned / Repeat", "BOARDS": "Boards", "CREATE_PROJECT": "Create Project", "CREATE_TAG": "Create Tag", "DELETE_PROJECT": "Delete Project", "DELETE_TAG": "Delete Tag", "ENTER_FOCUS_MODE": "Enter Focus Mode", "GO_TO_TASK_LIST": "Go to task list", "HELP": "Help", "HM": {"CALENDARS": "Howto: Connect Calendars", "CONTRIBUTE": "Contribute", "GET_HELP_ONLINE": "Get help online", "KEYBOARD": "Howto: Advanced Keyboard", "REDDIT_COMMUNITY": "Reddit Community", "REPORT_A_PROBLEM": "Report a problem", "START_WELCOME": "Start Welcome Tour", "SYNC": "Howto: Configure Sync"}, "METRICS": "Metrics", "NO_PROJECT_INFO": "No projects available. You can create a new project by clicking the \"Create Project\" button.", "NO_TAG_INFO": "There are currently no tags. You can add tags by entering `#yourTagName` when adding or editing tasks.", "NOTES": "Notes", "NOTES_PANEL_INFO": "Notes can only be shown from the schedule and the regular task list views.", "PLANNER": "Planner", "PROCRASTINATE": "Procrastinate", "PROJECT_MENU": "Project Menu", "PROJECT_SETTINGS": "Project Settings", "PROJECTS": "Projects", "QUICK_HISTORY": "Quick History", "SCHEDULE": "Schedule", "SEARCH": "Search", "SETTINGS": "Settings", "SHOW_SEARCH_BAR": "Show Search Bar", "TAGS": "Tags", "TASK_LIST": "Task List", "TASKS": "Tasks", "TOGGLE_SHOW_BOOKMARKS": "Show/Hide Bookmarks", "TOGGLE_SHOW_ISSUE_PANEL": "Show/Hide Issue Panel", "TOGGLE_SHOW_NOTES": "Show/Hide Project Notes", "TOGGLE_TRACK_TIME": "Start/Stop tracking time", "TRIGGER_SYNC": "Sync!", "WORKLOG": "Worklog", "SIDE_PANEL_MENU": "Side Panel Menu"}, "MIGRATE": {"C_DOWNLOAD_BACKUP": "Do you want to download a backup of your legacy data (can be used with older versions of Super Productivity)?", "DETECTED_LEGACY": "Detected legacy data. We will migrate it for you!", "E_MIGRATION_FAILED": "Migration failed! with <PERSON><PERSON><PERSON>:", "E_RESTART_FAILED": "Automatic restart failed. Please restart the app manually!", "SUCCESS": "Migration all done! Restarting app now..."}, "PDS": {"ADD_TASKS_FROM_TODAY": "Add tasks from today", "BACK": "Wait I forgot something!", "BREAK_LABEL": "Breaks (nr / time)", "CELEBRATE": "Take a moment to <i>celebrate!</i>", "CLEAR_ALL_CONTINUE": "Clear all done and continue", "D_CONFIRM_APP_CLOSE": {"CANCEL": "No, just clear the tasks", "MSG": "Your work is done. Time to go home!", "OK": "Aye aye! Shutdown!"}, "ESTIMATE_TOTAL": "Total estimate:", "EVALUATE_DAY": "Evaluate", "EXPORT_TASK_LIST": "Export Task List", "NO_TASKS": "There are no tasks for this day", "PLAN_TOMORROW": "Plan", "REVIEW_TASKS": "Review", "ROUND_5M": "Round all tasks to 5 minutes", "ROUND_15M": "Round all tasks to 15 minutes", "ROUND_30M": "Round all tasks to 30 minutes", "ROUND_TIME_SPENT": "Round Time Spent", "ROUND_TIME_SPENT_TITLE": "Round time spent on all tasks. Be careful! You cannot undo this!", "ROUND_TIME_WARNING": "!!! Be careful this cannot be undone !!!", "ROUND_UP_5M": "Round UP all tasks to 5 minutes", "ROUND_UP_15M": "Round UP all tasks to 15 minutes", "ROUND_UP_30M": "Round UP all tasks to 30 minutes", "SAVE_AND_GO_HOME": "Save and go home", "SAVE_AND_GO_HOME_TOOLTIP": "Move all done tasks to the archive (worklog) and optionally sync all data and close the app.", "START_END": "Start – End", "SUMMARY_FOR": "Daily Summary for {{dayStr}}", "TASKS_COMPLETED": "Tasks Completed", "TIME_SPENT_AND_ESTIMATE_LABEL": "Time Spent / Estimated", "TIME_SPENT_ESTIMATE_TITLE": "Time Spent: Total time spent today. Archived tasks not included. – Time Estimated: Time estimated for tasks worked on today minus the time already spent with them on other days.", "TIME_SPENT_TODAY_BY_TAG": "Time Spent today by Tag", "WEEK": "Week"}, "PM": {"TITLE": "Project Metrics"}, "PS": {"GLOBAL_SETTINGS": "Global Settings", "ISSUE_INTEGRATION": "Issue Integration", "NO_PLUGINS_INSTALLED": "No plugins are currently installed", "PLUGINS": "Plugins", "PRIVACY_POLICY": "Privacy Policy", "PRODUCTIVITY_HELPER": "Productivity Helper", "PROJECT_SETTINGS": "Project Specific Settings", "PROVIDE_FEEDBACK": "Provide <PERSON>", "RELOAD": "Reload", "SYNC_EXPORT": "Sync & Export", "TAG_SETTINGS": "Tag Specific Settings", "TOGGLE_DARK_MODE": "Toggle Dark Mode"}, "PLUGINS": {"INSTALL_PLUGIN": "Install Plugin", "UPLOAD_PLUGIN_INSTRUCTION": "Upload a plugin ZIP file to install it:", "CHOOSE_PLUGIN_FILE": "<PERSON><PERSON> Plugin File", "INSTALLING": "Installing...", "CLEAR_PLUGIN_CACHE": "<PERSON> Plugin <PERSON>", "REMOVE": "Remove", "HOOKS": "<PERSON>s", "PERMISSIONS": "Permissions", "ID": "ID:", "MIN_VERSION": "Min. Version:", "NODE_EXECUTION_REQUIRED": "This plugin requires Node.js execution which is only available in the desktop app.", "TOGGLE_PLUGIN": "Toggle {{pluginName}}", "EXPERIMENTAL_WARNING_TITLE": "Experimental Feature - Security Warning", "EXPERIMENTAL_WARNING": "The plugin system is in an experimental stage and should be used with extreme caution.", "SECURITY_WARNING": "Plugins have significant access to your data and system. Installing untrusted plugins poses serious security risks:", "RISK_DATA_ACCESS": "Plugins can read, modify, and delete ALL your tasks, projects, and personal data", "RISK_MALICIOUS_CODE": "Malicious plugins could steal sensitive information or corrupt your data", "RISK_SYSTEM_ACCESS": "Desktop plugins with Node.js permissions can execute system commands", "RISK_PERFORMANCE": "Poorly written plugins may cause performance issues or crashes", "RECOMMENDATION": "Only install plugins from trusted sources and review their code if possible. Always backup your data before installing new plugins.", "INSTALL_WARNING": "Before installing a plugin, ensure you trust its source and understand the permissions it requests.", "SYSTEM_ACCESS_REQUEST_TITLE": "System Access Request", "SYSTEM_ACCESS_REQUEST_DESC": "This plugin is requesting permission to execute Node.js code on your system. This will allow it to:", "GRANT_PERMISSION": "Grant Permission", "CANCEL": "Cancel", "REMEMBER_CHOICE": "Remember my choice for this plugin", "TRUST_WARNING": "Only grant permission if you trust this plugin", "CAPABILITIES": {"ACCESS_FILES": "Access and modify files on your system", "RUN_COMMANDS": "Execute system commands", "USE_NODE_APIS": "Use Node.js APIs and modules"}, "ERROR": "Error", "LOADING_PLUGIN": "Loading...", "ENABLED": "Enabled", "DISABLED": "Disabled", "PLEASE_SELECT_ZIP_FILE": "Please select a ZIP file", "FILE_TOO_LARGE": "File too large ({{fileSize}}MB). Maximum size is {{maxSize}}MB", "FAILED_TO_INSTALL": "Failed to install plugin", "FAILED_TO_CLEAR_CACHE": "Failed to clear plugin cache", "CONFIRM_REMOVE": "Are you sure you want to remove the plugin \"{{name}}\"?", "FAILED_TO_REMOVE": "Failed to remove plugin", "TYPE": "Type: {{type}}", "NO_ADDITIONAL_INFO": "No additional information available", "NO_PLUGIN_ID_PROVIDED_FOR_HTML": "No plugin ID provided for HTML content", "PROJECT_NOT_FOUND": "Project '{{contextId}}' not found", "TASKS_NOT_IN_PROJECT": "One or more tasks are not in project '{{contextId}}'", "PARENT_TASK_NOT_FOUND": "Parent task not found in context '{{contextId}}'", "TASKS_NOT_SUBTASKS": "One or more tasks are not subtasks of '{{contextId}}'", "NO_PLUGIN_CONTEXT_PERSISTENCE": "Plugin does not have permission to persist data", "UNABLE_TO_PERSIST_DATA": "Unable to persist data to plugin storage", "NO_PLUGIN_CONTEXT_LOADING": "Plugin does not have context for data loading", "NO_PLUGIN_CONTEXT_SYNC": "Plugin does not have permission to use sync", "NO_PLUGIN_CONTEXT_HEADER_BUTTON": "Plugin does not have permission to add header buttons", "NO_PLUGIN_CONTEXT_MENU_ENTRY": "Plugin does not have permission to add menu entries", "MENU_ENTRY_LABEL_REQUIRED": "Menu entry label is required", "MENU_ENTRY_ONCLICK_REQUIRED": "Menu entry onClick handler is required", "MENU_ENTRY_ICON_STRING": "Menu entry icon must be a string", "NO_PLUGIN_CONTEXT_SIDE_PANEL": "Plugin does not have permission to add side panel buttons", "SIDE_PANEL_LABEL_REQUIRED": "Side panel button label is required", "SIDE_PANEL_ONCLICK_REQUIRED": "Side panel button onClick handler is required", "NO_PLUGIN_CONTEXT_SHORTCUT": "Plugin does not have permission to register shortcuts", "PROJECT_DOES_NOT_EXIST": "Project does not exist", "TAGS_DO_NOT_EXIST": "One or more tags do not exist", "PARENT_TASK_DOES_NOT_EXIST": "Parent task does not exist", "VALIDATION_FAILED": "Validation failed", "NO_PLUGIN_CONTEXT_ACTION": "Plugin does not have permission to perform actions", "ACTION_TYPE_NOT_ALLOWED": "Action type '{{type}}' is not allowed", "NODE_ONLY_DESKTOP": "Node.js execution is only available in the desktop app", "NO_PLUGIN_CONTEXT_NODE": "Plugin does not have permission to execute Node.js code", "NO_PLUGIN_MANIFEST_NODE": "Plugin manifest does not include Node.js module", "ELECTRON_API_NOT_AVAILABLE": "Electron API is not available", "FAILED_TO_EXECUTE_SCRIPT": "Failed to execute Node.js script", "ALREADY_INITIALIZED": "Plugin is already initialized", "USER_DECLINED_NODE_PERMISSION": "User declined Node.js execution permission", "FAILED_TO_EXTRACT_ZIP": "Failed to extract plugin ZIP file", "MANIFEST_NOT_FOUND": "Plugin manifest (plugin.json) not found in ZIP file", "MANIFEST_TOO_LARGE": "Plugin manifest is too large (max {{maxSize}}KB)", "PLUGIN_JS_NOT_FOUND": "Plugin JavaScript file (plugin.js) not found in ZIP file", "CODE_TOO_LARGE": "Plugin code is too large (max {{maxSize}}MB)", "UNKNOWN_ERROR": "An unknown error occurred", "PLUGIN_DIALOG_TITLE": "Plugin Message", "NO_CONTENT_PROVIDED": "No content provided", "OK": "OK", "LOADING_INTERFACE": "Loading plugin interface...", "ERROR_LOADING_PLUGIN": "Error loading plugin", "GO_BACK": "Go Back", "FAILED_TO_LOAD": "Failed to load plugin", "PLUGIN_ID_NOT_PROVIDED": "Plugin ID not provided", "PLUGIN_SYSTEM_FAILED_INIT": "Plugin system failed to initialize", "PLUGIN_NOT_FOUND": "Plugin not found", "PLUGIN_DOES_NOT_SUPPORT_IFRAME": "This plugin does not support iframe interface", "INDEX_HTML_NOT_LOADED": "Plugin index.html not loaded"}, "SCHEDULE": {"NO_REPEATABLE_TASKS": "There are currently no repeated tasks. You can schedule a task by choosing \"Repeat Task\" in the task side panel. To open it click on the most right icon that appears when hovering a task (or just tap on the task on mobile).", "NO_SCHEDULED": "There are currently no scheduled tasks. You can schedule a task by choosing \"Schedule Task\" in the task context menu. To open it right click (long press on mobile) on a task.", "NO_SCHEDULED_TITLE": "Scheduled Tasks for Day", "REPEATED_TASKS": "Repeated Tasks", "SCHEDULED_TASKS": "Scheduled tasks", "SCHEDULED_TASKS_WITH_TIME": "Scheduled Tasks with Start Time", "START_TASK": "Start task and remove reminder"}, "THEMES": {"amber": "amber", "blue": "blue", "blue-grey": "blue-grey", "cyan": "cyan", "deep-orange": "deep-orange", "deep-purple": "deep-purple", "green": "green", "indigo": "indigo", "light-blue": "light-blue", "light-green": "light-green", "lime": "lime", "pink": "pink", "purple": "purple", "SELECT_THEME": "Select Theme", "teal": "teal", "yellow": "yellow"}, "V": {"E_1TO10": "Please enter a value between 1 and 10", "E_DATETIME": "The entered value is not a datetime!", "E_DURATION": "Please enter a valid duration (e.g. 1h)", "E_MAX": "Should not be bigger than {{val}}", "E_MAX_LENGTH": "Should be max {{val}} characters long", "E_MIN": "Should not be smaller than {{val}}", "E_MIN_LENGTH": "Should be at least {{val}} characters long", "E_PATTERN": "Invalid input", "E_REQUIRED": "This field is required"}, "WW": {"ADD_MORE": "Add more", "ADD_SCHEDULED_FOR_TOMORROW": "Add tasks planned for tomorrow ({{nr}})", "ADD_SOME_TASKS": "Add some tasks to plan your day!", "DONE_TASKS": "Done Tasks", "DONE_TASKS_IN_ARCHIVE": "There are currently no done tasks here, but there are some already archived.", "ESTIMATE_REMAINING": "Estimate remaining:", "LATER_TODAY": "Later Today", "FINISH_DAY": "Finish Day", "FINISH_DAY_FOR_PROJECT": "Finish Day for this Project", "FINISH_DAY_FOR_TAG": "Finish Day for this Tag", "FINISH_DAY_TOOLTIP": "Evaluate your day, move all done tasks to the archive (optionally) and/or plan your next day.", "HELP_PROCRASTINATION": "Help I'm procrastinating!", "MOVE_DONE_TO_ARCHIVE": "Move done to archive", "NO_DONE_TASKS": "There are currently no done tasks", "NO_PLANNED_TASK_ALL_DONE": "all tasks done", "NO_PLANNED_TASKS": "no tasks planned", "READY_TO_WORK": "Ready to work!", "RESET_BREAK_TIMER": "Reset without break timer", "TIME_ESTIMATED": "Time Estimated:", "TODAY_REMAINING": "Today remaining:", "WITHOUT_BREAK": "Without break:", "WORKING_TODAY": "Working today:", "WORKING_TODAY_ARCHIVED": "Time worked today on archived tasks"}}