name: Win Store File on Release
on:
  #  release:
  #    types: [published]
  push:
    branches: [test/git-actions]
    tags:
      - v*
  workflow_dispatch:
    inputs: {}

jobs:
  windows-store-artifact:
    runs-on: windows-latest

    if: '!github.event.release.prerelease'

    steps:
      - uses: actions/setup-node@v3
        with:
          node-version: 20
      # required because setting via env.TZ does not work on windows
      - name: Set timezone to Europe Standard Time
        uses: szenius/set-timezone@v2.0
        with:
          timezoneWindows: 'W. Europe Standard Time'

      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      # work around for npm installs from git+https://github.com/johannesjo/J2M.git
      - name: Reconfigure git to use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/

      - name: Load Electron Builder Windows Store Config
        run: echo $WIN_STORE_ELECTRON_BUILDER_YML | base64 --decode > electron-builder.yaml
        shell: bash
        env:
          WIN_STORE_ELECTRON_BUILDER_YML: ${{secrets.WIN_STORE_ELECTRON_BUILDER_YML}}

      - name: Get npm cache directory
        id: npm-cache-dir
        run: |
          echo "::set-output name=dir::$(npm config get cache)"
      - uses: actions/cache@v4
        id: npm-cache # use this to check for `cache-hit` ==> if: steps.npm-cache.outputs.cache-hit != 'true'
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Workaround for nx issue
        run: npm install @nx/nx-win32-x64-msvc

      - name: Install npm Packages
        #  if: steps.npm-cache.outputs.cache-hit != 'true'
        run: npm i

      - name: Lint
        run: npm run lint:ci

      - name: Test Unit
        run: npm run test

      - name: Build Frontend & Electron
        run: npm run build

      - name: Build/Release Electron app
        uses: johannesjo/action-electron-builder@v1
        with:
          build_script_name: empty
          release: false
          github_token: ${{ secrets.github_token }}

      - name: 'Upload Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: WinStoreRelease
          path: .tmp/app-builds/*.appx
#      - name: Upload to Microsoft Store
#        if: startsWith(github.ref, 'refs/tags/v')
#        uses: isaacrlevin/windows-store-action@1.0
#        with:
#          tenant-id: ${{ secrets.AZURE_AD_TENANT_ID }}
#          client-id: ${{ secrets.AZURE_AD_CLIENT_ID }}
#          client-secret: ${{ secrets.AZURE_AD_CLIENT_SECRET }}
#          app-id: ${{ secrets.MICROSOFT_STORE_APP_ID }}
#          package-path: '.tmp/app-builds/*.appx'
#          skip-polling: false
