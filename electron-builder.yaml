appId: superProductivity
productName: Super Productivity
artifactName: ${name}-${arch}.${ext}

files:
  - electron/**/*
  - '!electron/**/*.ts'
  - '!electron/**/*.js.map'
  - .tmp/angular-dist/**/*
  - 'embedded.provisionprofile'
#afterSign: ./tools/notarizeMacApp.js

directories:
  output: './.tmp/app-builds'

win:
  target:
    - target: nsis
      arch:
        - arm64
        - x64
    - target: portable
      arch:
        - arm64
        - x64
  publish:
    - github
nsis:
  artifactName: ${productName} Setup-${arch}.${ext}
portable:
  artifactName: ${name}-${arch}.${ext}
appx:
  applicationId: SuperProductivity
  displayName: Super Productivity
  publisherDisplayName: johannesjo
  publisher: CN=___-___
  identityName: ___johannesjo.SuperProductivity

linux:
  category: Office;ProjectManagement
  target:
    - AppImage
    - deb
    - snap
    - rpm
  publish:
    - github
# leads to Error: Please specify executable name as linux.executableName instead of linux.desktop.Exec
#  desktop:
#    Name: Super Productivity
#    Comment: A todo list and time tracking app
#    Exec: superProductivity
#    Terminal: false
#    Type: Application
#    Icon: build/icon.png
#    GenericName: ToDo List and Time Tracker

snap:
  grade: stable
  allowNativeWayland: true
  autoStart: true
  base: core22
  confinement: strict
  plugs:
    - default
    - password-manager-service
    - system-observe
    - login-session-observe

mac:
  appId: com.super-productivity.app
  type: distribution
  category: public.app-category.productivity
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  icon: build/icon-mac.icns
  hardenedRuntime: true
  gatekeeperAssess: false
  provisioningProfile: embedded.provisionprofile
  darkModeSupport: true
  notarize: true
  extendInfo:
    ITSAppUsesNonExemptEncryption: false
  publish:
    - github
  target:
    #    - zip
    - target: dmg
      arch:
        - arm64
        - x64
        - universal

dmg:
  sign: true
  publish:
    - github

pkg:
  publish: never
# HERE: electron-builder.mas.yaml
#mas:
#  type: distribution
#  category: public.app-category.productivity
#  entitlements: build/entitlements.mas.plist
#  entitlementsInherit: build/entitlements.mas.inherit.plist
#  provisioningProfile: prod.provisionprofile
#  icon: build/icon.icns
#  hardenedRuntime: true
#  publish: never
