// import { async, ComponentFixture, TestBed } from '@angular/core/testing';
//
// import { TaskAdditionalInfoItemComponent } from './task-additional-info-item.component';
//
// describe('TaskAdditionalInfoItemComponent', () => {
//   let component: TaskAdditionalInfoItemComponent;
//   let fixture: ComponentFixture<TaskAdditionalInfoItemComponent>;
//
//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [ TaskAdditionalInfoItemComponent ]
//     })
//     .compileComponents();
//   }));
//
//   beforeEach(() => {
//     fixture = TestBed.createComponent(TaskAdditionalInfoItemComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
