* {
  box-sizing: border-box;
}
body {
  margin: 0;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    sans-serif;
  background: transparent;
  color: var(--theme-text-color);
}
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}
.card {
  background: var(--theme-card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 4px #0000001a;
  padding: 1rem;
  text-align: left;
  border: 2px solid var(--theme-extra-border-color);
}
.card-clickable:hover {
  transition: transform 0.2s;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px #00000026;
  border-color: var(--c-primary);
  cursor: pointer;
}
.text-muted {
  color: var(--theme-text-color-muted);
}
.text-primary {
  color: var(--c-primary);
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header h1 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--c-primary);
}
.back-button {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid var(--c-primary);
  color: var(--c-primary);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-family: inherit;
}
.back-button:hover {
  background: var(--c-primary);
  color: #fff;
}
.main {
  flex: 1;
  position: relative;
  overflow: hidden;
}
.intro {
  text-align: center;
  margin-bottom: 2rem;
}
.intro h2 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}
.blocker-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-top: 2rem;
}
.blocker-card h3 {
  margin: 0 0 0.5rem;
  font-size: 1.1rem;
}
.blocker-card p {
  margin: 0;
  font-size: 0.9rem;
}
.strategy-container {
  max-width: 600px;
  margin: 0 auto;
}
.selected-type {
  padding: 1.5rem;
  text-align: center;
}
.selected-type h2 {
  margin: 0 0 0.5rem;
}
.emotion {
  margin: 0;
  font-style: italic;
}
.strategy-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}
.strategy-item {
  padding: 1rem;
}
.strategy-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}
.strategy-text {
  margin: 0;
  flex: 1;
}
.strategy-action-btn {
  padding: 6px 12px;
  border: 1px solid var(--c-primary);
  background: transparent;
  color: var(--c-primary);
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  margin-left: auto;
}
.strategy-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px #0000001a;
}
.page-fade {
  animation: fadeIn 0.3s ease;
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.info-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid var(--theme-extra-border-color);
  color: var(--theme-text-color-muted);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-family: inherit;
}
.info-button:hover {
  border-color: var(--c-primary);
  color: var(--c-primary);
}
.info-content {
  max-width: 700px;
  margin: 0 auto;
  padding: 1rem;
}
.info-content section {
  margin-bottom: 2rem;
}
.info-content h3 {
  color: var(--c-primary);
  margin-bottom: 0.75rem;
}
.info-content p {
  line-height: 1.6;
  margin-bottom: 1rem;
}
.info-content ul {
  line-height: 1.8;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}
.procrastination-graph {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin: 1.5rem 0;
  flex-wrap: wrap;
}
.graph-item {
  padding: 0.5rem 1rem;
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-extra-border-color);
  border-radius: 4px;
  text-align: center;
  font-size: 0.9rem;
}
.sync-icon {
  font-size: 1.2rem;
  color: var(--theme-text-color-muted);
}
.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}
.primary-button {
  padding: 0.75rem 2rem;
  background: var(--c-primary);
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.2s;
}
.primary-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px #00000026;
}
