{"$schema": "./node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/index.html", "/manifest.json", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/assets/**"]}}, {"name": "criticalAssets", "installMode": "prefetch", "updateMode": "prefetch", "resources": {"files": ["/assets/icons/*.svg", "/assets/icons/icon-*.png", "/assets/icons/favicon-*.png", "/assets/i18n/en.json", "/assets/fonts/MaterialIcons-Regular.ttf", "/*.(otf|ttf|woff|woff2)"]}}, {"name": "externalAssets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"urls": ["**/*.(png|jpg|jpeg|gif)"]}}], "dataGroups": [{"name": "api-freshness", "urls": ["https://api.github.com/**", "https://gitlab.com/api/**", "https://*.atlassian.net/**"], "cacheConfig": {"strategy": "freshness", "maxSize": 10, "maxAge": "1h", "timeout": "10s"}}]}