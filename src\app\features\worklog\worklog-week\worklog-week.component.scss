:host {
  display: block;
}

$this-border-radius: 8px;

.days {
  text-align: center;
  vertical-align: top;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.day {
  vertical-align: top;
  display: inline-block;
  width: 280px;
  margin: var(--s);

  .material-table {
    border-radius: $this-border-radius;
  }
}

// .scroll-wrapper {
//   max-height: 280px;
//   @include scrollY;
// }

.caption {
  border-top-left-radius: $this-border-radius;
  border-top-right-radius: $this-border-radius;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none; /* Safari */

  .spacer {
    width: calc(var(--s) * 1.5);
  }

  em,
  strong,
  span,
  mat-icon {
    vertical-align: middle;
    display: inline-block;
  }

  > div {
    padding: var(--s) 0;
    font-weight: bold;
    flex-basis: 20%;

    &:first-child {
      padding-left: var(--s2);
      text-align: left;
    }

    &:last-child {
      padding-right: var(--s2);
      text-align: right;
    }
  }

  .title {
    text-align: center;
    flex-basis: 60%;
    font-weight: normal;
    text-transform: uppercase;

    h3 {
      font-weight: normal;
      margin-bottom: 0;
    }
  }

  .center-box {
    flex-grow: 1;
  }
}

.task-summary-table {
  display: block;
  width: 100%;
  border: none;
  box-shadow: none;

  > table {
    width: 100%;
    box-shadow: none;
    text-align: left;
    border-collapse: collapse;

    > tbody {
      width: 100%;
    }
  }

  th,
  td {
    text-align: left;
    white-space: normal;
    line-height: 16px;
    padding: var(--s) calc(var(--s) * 1.5);
    vertical-align: middle;

    &.with-icon {
      text-align: center;
    }

    &.title {
      padding-left: var(--s2);
      padding-right: 0;
      width: 100%;
    }

    &.worked {
      white-space: nowrap;
      text-align: center;
      padding: 0;

      ::ng-deep .inline-input-wrapper {
        padding: var(--s) calc(var(--s) * 1.5);
      }
    }

    &.isSubTask {
      .task-title {
        font-style: italic;
        padding-left: var(--s2);
        position: relative;
        display: inline-block;

        &:before {
          content: '•';
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
        }
      }
    }
  }
}

.simple-counter-items {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  // NOTE: give them a bit of extra space
  // MAGIC NUMBER ALERT!!!
  margin-left: calc(-1 * var(--s4));
  margin-right: calc(-1 * var(--s4));
  //margin-top: var(--s);
}

.simple-counter-item {
  margin: 0 var(--s-half);
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;

  mat-icon {
    font-size: 18px;
    margin-right: 2px;
  }
}
