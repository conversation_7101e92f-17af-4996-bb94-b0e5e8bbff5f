@import '../../../../common';

:host-context([dir='rtl']) {
  direction: rtl;
}

:host {
  display: block;

  mat-form-field {
    display: block;
  }
}

.control-row {
  td,
  th {
    padding: 0;
    border: 0 !important;
  }

  td {
    ::ng-deep .mat-mdc-select-value {
      padding-left: calc(var(--s) * 2.25);
      padding-top: calc(var(--s) * 1.25);
      padding-bottom: calc(var(--s) * 1.25);
    }

    &:first-of-type ::ng-deep .mat-mdc-select-value {
      padding-left: var(--s3);
    }

    ::ng-deep .mat-mdc-select-arrow-wrapper {
      padding-right: calc(var(--s) * 1.25);
    }

    &:last-of-type ::ng-deep .mat-mdc-select-arrow-wrapper {
      padding-right: var(--s3);
    }
  }

  mat-select {
    &:hover {
      &:after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        z-index: -1;

        background: var(--theme-select-hover-bg);
      }
    }
  }
}

@media (min-width: #{$layout-xs}) {
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding-top: var(--s2);
  }
  .col {
    width: 32%;
  }
}

#task-textarea {
  width: 100%;
  @media (min-width: #{$layout-xs}) {
    min-width: 70vw;
  }
}

.wrapper-wrapper {
  position: relative;
  min-height: 60px;

  &.isNoCols {
    text-align: center;
  }
}

.add-col {
  z-index: 2;
  position: absolute;
  top: 0;
  right: -20px;
  transition: var(--transition-duration-l) var(--ani-standard-timing);

  .isNoCols & {
    position: absolute;
    top: 10px;
    right: 50%;
    opacity: 1;
  }

  @include mq(xs) {
    opacity: 0;
    .wrapper-wrapper:hover & {
      opacity: 1;
    }
  }
}

.table-wrapper {
  overflow: auto;
  position: relative;
  z-index: 1;
  margin: 0 -20px var(--s);

  ::ng-deep table {
    width: 100%;
  }

  ::ng-deep td {
    white-space: normal;

    &:last-child {
      white-space: normal;
    }
  }

  tr:first-of-type td {
    border: 1px solid var(--theme-extra-border-color);
  }

  th {
    //@include mat-css-color-and-contrast(500);
  }

  th,
  td {
    text-align: center;

    &.TITLES_INCLUDING_SUB,
    &.TITLES {
      text-align: start;
      min-width: 250px;
    }

    &.DATE {
      @include mq(xs) {
        white-space: nowrap;
      }
    }
  }
}

.options-collapsible {
  margin-top: var(--s2);
  text-align: center;
}

.options {
  text-align: left;
  position: relative;

  &:after {
    position: absolute;
    content: '';
    left: -20px;
    right: -20px;
    top: 0;
    border: 1px solid;

    border-color: var(--theme-options-border-color);
  }
}

collapsible::ng-deep .collapsible-header {
  text-transform: uppercase;
}
