@use '@angular/material' as mat;
@use 'angular-material-css-vars' as mat-css-vars;

//$custom-typography: mat.m2-define-typography-config(
//  $font-family: null,
//);
//@include mat-css-vars.init-material-css-vars($typography-config: $custom-typography);
@include mat-css-vars.init-material-css-vars();

.bg-card {
  background: var(--theme-card-bg);

  .isDarkTheme & {
    background: var(--theme-card-bg);
  }
}

.bg-primary {
  background-color: var(--c-primary);
}

.bgc-primary {
  @include mat-css-vars.mat-css-color-and-contrast-primary(500);
}

.bg-accent {
  background-color: var(--c-accent);
}

.bgc-accent {
  // todo fix
  @include mat-css-vars.mat-css-color-and-contrast-accent(500);
}

.bg-100 {
  background-color: var(--palette-primary-100);
}

.bg-200 {
  background-color: var(--palette-primary-200);
}

.bg-400 {
  background-color: var(--palette-primary-400);
}

.bgc-400 {
  @include mat-css-vars.mat-css-color-and-contrast-primary(400);
}

.bg-600 {
  background-color: var(--palette-primary-600);
}

.bgc-600 {
  @include mat-css-vars.mat-css-color-and-contrast-primary(600);
}

.cc-600 {
  color: var(--palette-primary-contrast-600);
}

.bg-600i {
  background-color: var(--palette-primary-600) !important;
}

.bgc-800 {
  @include mat-css-vars.mat-css-color-and-contrast-primary(800);
}

// for buttons
.isDarkTheme .mat-lighter,
.mat-lighter {
  @include mat-css-vars.mat-css-color-and-contrast-primary(300);
}

.isDarkTheme .mat-darker,
.mat-darker {
  @include mat-css-vars.mat-css-color-and-contrast-primary(600);
}

.color-contrast {
  color: var(--palette-primary-contrast-500);
}

.color-primary {
  color: var(--c-primary);
  stroke: var(--c-primary);
}

.color-warn-i {
  color: var(--c-warn) !important;
  stroke: var(--c-warn) !important;
}

.border-color-primary {
  border-color: var(--c-primary) !important;
}

// Traffic light colors for progress bars
.bg-success {
  background-color: var(--color-success);
}

.bg-warning {
  background-color: var(--color-warning);
}

.bg-danger {
  background-color: var(--color-danger);
}
