:host ::ng-deep .wrap-add-planned {
  margin-top: var(--s2);
}

.add-task-bar-wrapper {
  margin: calc(var(--s) * 1.5) auto calc(var(--s) * 1.5);
  max-width: 628px;
}

:host::ng-deep .hover-controls {
  .task-done-btn,
  .start-task-btn {
    display: none !important;
  }
}

.estimate-total {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: center;
  padding: 5px 0;
  user-select: none;
  -webkit-user-select: none; /* Safari */
  vertical-align: middle;

  .label {
    margin-right: var(--s);
  }

  .no-wrap {
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}

:host planner-day {
  max-width: 100%;
  width: auto;
  text-align: left;
}

:host ::ng-deep add-task-inline {
  display: none !important;
}
