// import { async, ComponentFixture, TestBed } from '@angular/core/testing';
//
// import { DialogTimeEstimateComponent } from './dialog-time-estimate.component';
//
// describe('DialogTimeEstimateComponent', () => {
//   let component: DialogTimeEstimateComponent;
//   let fixture: ComponentFixture<DialogTimeEstimateComponent>;
//
//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [DialogTimeEstimateComponent]
//     })
//       .compileComponents();
//   }));
//
//   beforeEach(() => {
//     fixture = TestBed.createComponent(DialogTimeEstimateComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should be created', () => {
//     expect(component).toBeTruthy();
//   });
// });
