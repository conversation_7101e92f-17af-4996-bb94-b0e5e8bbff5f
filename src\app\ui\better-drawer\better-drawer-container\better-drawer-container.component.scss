@import '../../../../common';

$z-this-content: calc(var(--z-side-panel-task-and-notes) + 1);
$z-this-backdrop: calc(var(--z-task-side-bar-over) - 1);

:host {
  display: flex;
  flex-direction: row;
  justify-content: stretch;
  align-items: stretch;
  height: 100%;
}

.content {
  flex-grow: 1;
  max-width: 100%;
  position: relative;
  z-index: $z-this-content;
}

.side {
  // NOTE: we can't use margin-top: -1px; to prevent double border when header got a bottom one
  // because then there would be none

  position: relative;
  z-index: var(--z-side-panel-task-and-notes);
  transition: var(--transition-leave);
  transition-property: opacity, margin-right, right, transform;
  // NOTE: if this value is to big it might mess, with the push out logic, as
  // the width is not really 40% any more
  min-width: 200px;
  // NOTE: prevents overlapping with nav when open
  max-width: 700px;
  background: transparent;
  //border: 1px solid;
  //border-right: 0;
  //border-bottom: 0;
  //box-shadow: 0px 8px 10px -5px var(--color-overlay-dark-20), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px var(--theme-divider-color) !important;

  @include mq(sm, max) {
    // NOTE: prevents overlapping with nav when open
    max-width: 500px;
  }

  @include mq(xs, max) {
    max-width: 100%;
  }

  background: var(--theme-selected-task-bg-color);
  //border-color: var(--theme-extra-border-color);

  :host.isOver & {
    position: fixed;
    width: 80vw !important;
    top: 0;
    bottom: 0;
    z-index: var(--z-task-side-bar-over);
    right: 0;

    @include mq(xs, max) {
      width: 100% !important;
    }
  }

  :host.isOpen.isOpen.isOpen & {
    box-shadow: var(--whiteframe-shadow-2dp);
    opacity: 1;
    transition: var(--transition-enter);
  }
}

.side-inner {
  @include scrollYImportant();
  height: 100%;
  position: relative;
  z-index: 2;
}

.backdrop {
  // global styles in backdrop.scss
  z-index: $z-this-backdrop;

  @include mq(xs, max) {
    display: none;
  }
}

.close-btn {
  display: none;
  font-size: 32px;
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 46px;
  position: absolute;
  z-index: 23;
  top: 5px;
  cursor: pointer;
  right: var(--s);
  box-shadow: var(--whiteframe-shadow-2dp);
  border-radius: 50%;
  border: 1px solid;

  @include mq(xs) {
    top: 0;
    border-radius: 0;
    right: 100%;
    box-shadow: var(--whiteframe-shadow-1dp);
    border: 1px solid;
  }

  &,
  &:focus,
  &:active {
    outline: none;
  }

  color: var(--theme-text-color);
  background: var(--theme-bg-lightest);
  border-color: var(--theme-extra-border-color);

  @include mq(xs) {
    background: var(--theme-bg-slightly-lighter);
  }

  :host.isOver.isOpen & {
    display: block;
  }

  @include mq(xs) {
    //display: none;
  }
}
