{"includePatterns": ["**/*"], "excludePatterns": ["node_modules/**", "dist/**", "out-tsc/**"], "allowFileAccess": true, "permissions": {"allow": ["Bash(npm ci)", "Bash(npm run lint)", "Bash(npm run prettier)", "Bash(npm run test)", "Bash(npm run test:*)", "Bash(npm run build:*)", "Bash(ng build)", "Bash(ng build --no-watch --configuration=development)", "Bash(npm run electron:dev)", "Read(src/**)", "Read(package*.json)", "<PERSON>(angular.json)", "Edit(src/**)", "Bash(git diff:*)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(find *)", "<PERSON><PERSON>(grep *)", "Bash(rg *)"], "deny": ["Bash(rm:*)", "<PERSON><PERSON>(curl:*)", "Bash(git push:*)"]}, "env": {"NODE_ENV": "development", "NG_CLI_ANALYTICS": "false", "CI": "true"}, "includeCoAuthoredBy": false, "verbose": false}