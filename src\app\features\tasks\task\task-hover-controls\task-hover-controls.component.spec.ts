// import { ComponentFixture, TestBed } from '@angular/core/testing';
//
// import { TaskHoverControlsComponent } from './task-hover-controls.component';
//
// describe('TaskHoverControlsComponent', () => {
//   let component: TaskHoverControlsComponent;
//   let fixture: ComponentFixture<TaskHoverControlsComponent>;
//
//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [TaskHoverControlsComponent]
//     })
//     .compileComponents();
//
//     fixture = TestBed.createComponent(TaskHoverControlsComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
