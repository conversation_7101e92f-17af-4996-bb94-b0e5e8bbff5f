name: AUR Release on Release
on:
  release:
    types: [published]
  workflow_dispatch:
    inputs: {}

jobs:
  aur-release:
    runs-on: ubuntu-latest

    if: '!github.event.release.prerelease'

    steps:
      - uses: actions/checkout@v3

      - run: PACKAGE_VERSION=$(cat ./package.json | grep version | head -1 | awk -F '"' '{print $4}') && echo "package_version=$PACKAGE_VERSION" >> $GITHUB_ENV
      - run: sed "s/PACKAGE_VERSION/${package_version}/g" build/linux/PKGBUILD_template > build/linux/PKGBUILD

      - name: Publish AUR package
        uses: KSXGitHub/github-actions-deploy-aur@v3.0.1
        with:
          pkgname: superproductivity-bin
          pkgbuild: build/linux/PKGBUILD
          commit_username: ${{ secrets.AUR_USERNAME }}
          commit_email: ${{ secrets.AUR_EMAIL }}
          ssh_private_key: ${{ secrets.AUR_SSH_PRIVATE_KEY }}
          commit_message: Update AUR package
          ssh_keyscan_types: rsa,ecdsa,ed25519
