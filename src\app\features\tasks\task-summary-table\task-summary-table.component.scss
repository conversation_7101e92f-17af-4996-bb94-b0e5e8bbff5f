:host {
  display: block;
}

.summary-table {
  width: 100%;

  background: var(--theme-card-bg);

  tr {
    height: 36px !important;
  }

  td {
    text-align: center;

    color: var(--theme-text-color-muted);

    inline-input {
      color: var(--theme-text-color);
    }
  }

  th {
    text-align: center;
  }

  th,
  td {
    min-width: 50px;
  }

  td:first-child {
    text-align: start;
    min-width: 220px;
  }

  .cdk-column-timeSpentToday ::ng-deep .inline-input-wrapper {
    padding: var(--s);

    input {
      width: 100px;
    }
  }
}

.check-done {
  opacity: 0.5;

  &:hover {
    opacity: 1 !important;
  }

  .check,
  .undo {
    opacity: 0;
  }

  .undo {
    margin-right: -24px;
  }

  &:focus,
  &:hover {
    mat-icon {
      animation: var(--transition-duration-m) success-btn-ani linear;
    }

    .check {
      opacity: 1;
    }
  }

  &.isDone {
    .check {
      opacity: 1;
    }

    &:hover {
      .check {
        opacity: 0;
      }

      .undo {
        opacity: 1;
      }
    }
  }
}

.repeat-task-icon {
  vertical-align: middle;
  margin-right: 4px;
  transform: rotate(45deg);
  opacity: 0.7;
}

.task-title {
  display: flex;
  //border: 1px solid orange;
  text-align: start;
  align-items: center;
  // prevent overflow for looooong words
  word-break: break-word;

  &.isDone ::ng-deep .value-wrapper {
    text-decoration: line-through;
  }

  &.isSubTask {
    font-style: italic;
  }

  > span {
    text-decoration: none !important;
    word-break: break-word;
    display: block;
    padding-left: var(--s);
  }

  inline-input {
    flex-grow: 1;
    text-align: start;
    display: block;
  }

  ::ng-deep .inline-input-wrapper {
    padding: var(--s);

    input {
      width: 100%;
      text-align: start;
    }
  }
}
