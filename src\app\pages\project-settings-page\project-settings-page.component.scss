.section-wrapper {
  //display: grid;
  //grid-template-columns: 50% 50%;
  //grid-gap: 0 var(--s2);
}

.config-section {
  border: 1px solid var(--theme-divider-color);
  break-inside: avoid;
  margin-bottom: 10px;
  border-radius: var(--card-border-radius);
  background: var(--theme-card-bg);

  background-color: var(--theme-card-bg);

  .md-title {
    margin-top: 0;
  }

  ::ng-deep .collapsible-header {
    border-bottom: 1px solid transparent;
  }

  ::ng-deep .isExpanded .collapsible-header {
    font-weight: bold;

    border-color: var(--theme-extra-border-color);
  }
}

.settings-col {
  margin-bottom: 30px;

  &:last-child {
    margin-bottom: 0;
  }
}
