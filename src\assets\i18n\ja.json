{"ANDROID": {"NOTIFICATIONS": {"SYNC_CONFLICT_MSG": "確認して何をするか決めてください。", "SYNC_CONFLICT_TITLE": "同期の競合が発生しました"}, "PERMANENT_NOTIFICATION_MSGS": {"INITIAL": "同期を有効にするためにバックグラウンドで実行されているアプリ", "NO_ACTIVE_TASKS": "アクティブなタスクなし", "SYNCING": "同期中"}}, "APP": {"B_INSTALL": {"IGNORE": "無視する", "INSTALL": "インストール", "MSG": "Super ProductivityをPWAとしてインストールしますか？"}, "B_OFFLINE": "インターネットに接続していません。課題プロバイダーデータの同期と要求は機能しません。", "UPDATE_MAIN_MODEL": "Super Productivityが大幅に更新されました！データのいくつかの移行が必要です。これにより、データが古いバージョンのアプリと互換性がなくなることに注意してください。", "UPDATE_MAIN_MODEL_NO_UPDATE": "モデルの更新が選択されていません。モデルのアップグレードを実行しない場合は、前のバージョンにダウングレードする必要があることに注意してください。", "UPDATE_WEB_APP": "新しいバージョンがあります。新しいバージョンをロードしますか？"}, "BL": {"NO_TASKS": "現在、バックログにタスクはありません"}, "CONFIRM": {"AUTO_FIX": "データが破損しているようです。自動修復を試しますか？その結果、データが部分的に失われる可能性があります。", "RELOAD_AFTER_IDB_ERROR": "データベースにアクセスできません:(考えられる原因は、バックグラウンドでのアプリの更新またはディスク容量の不足です。[OK]を押してアプリをリロードします（一部のプラットフォームではアプリを手動で再起動する必要がある場合があります）。", "RESTORE_FILE_BACKUP": "データがないようですが、「{{dir}}」で利用可能なバックアップがあります。 {{from}}から最新のバックアップを復元しますか？", "RESTORE_FILE_BACKUP_ANDROID": "データがないようですが、利用可能なバックアップがあります。ロードしますか？", "RESTORE_STRAY_BACKUP": "最後の同期中に、エラーが発生した可能性があります。最後のバックアップを復元しますか？"}, "DATETIME_SCHEDULE": {"LATER_TODAY": "今日あとで", "NEXT_WEEK": "来週", "PLACEHOLDER": "日付を選択してください", "PRESS_ENTER_AGAIN": "保存するにはもう一度Enterキーを押してください", "TOMORROW": "明日"}, "F": {"ATTACHMENT": {"DIALOG_EDIT": {"ADD_ATTACHMENT": "添付ファイルを追加", "EDIT_ATTACHMENT": "添付ファイルを編集", "LABELS": {"FILE": "ファイルパス", "IMG": "画像", "LINK": "URL"}, "SELECT_TYPE": "タイプを選択", "TYPES": {"FILE": "ファイル（デフォルトのシステムアプリで開かれる）", "IMG": "画像（サムネイル表示）", "LINK": "リンク（ブラウザで開きます）"}}}, "BOARDS": {"DEFAULT": {"DONE": "完成です", "EISENHAUER_MATRIX": "アイゼンハウアーマトリックス", "IMPORTANT": "大事な", "IN_PROGRESS": "進行中で", "KANBAN": "カンバン", "NOT_URGENT_IMPORTANT": "緊急性がなく、重要ではありません", "NOT_URGENT_NOT_IMPORTANT": "緊急ではない、重要ではない", "TO_DO": "やるべきこと", "URGENT": "緊急", "URGENT_IMPORTANT": "緊急かつ重要", "URGENT_NOT_IMPORTANT": "緊急 & 重要ではない"}, "FORM": {"ADD_NEW_PANEL": "新しいパネルを追加", "BACKLOG_TASK_FILTER_ALL": "すべての", "BACKLOG_TASK_FILTER_NO_BACKLOG": "除外する", "BACKLOG_TASK_FILTER_ONLY_BACKLOG": "バックログのみ", "BACKLOG_TASK_FILTER_TYPE": "バックログ タスク", "COLUMNS": "列", "TAGS_EXCLUDED": "除外されたタグ", "TAGS_REQUIRED": "必須タグ", "TASK_DONE_STATE": "タスク完了状態", "TASK_DONE_STATE_ALL": "すべての", "TASK_DONE_STATE_DONE": "完成です", "TASK_DONE_STATE_UNDONE": "取り消す"}, "V": {"ADD_NEW_BOARD": "新しいボードを追加", "CONFIRM_DELETE": "このボードを削除してもよろしいですか?", "CREATE_NEW_TAG_BTN": "タグを作成", "CREATE_NEW_TAG_MSG": "このボードを機能させるには、新しいタグを1つ作成する必要があります", "CREATE_NEW_TAGS_BTN": "タグの作成", "CREATE_NEW_TAGS_MSG": "このボードを機能させるには、{{nr}} 新しいタグを作成する必要があります", "EDIT_BOARD": "編集ボード", "NO_PANELS_BTN": "ボードの構成", "NO_PANELS_MSG": "このボードにはパネルが設定されていません。それにいくつかのパネルを追加します。"}}, "CALDAV": {"DIALOG_INITIAL": {"TITLE": "プロジェクトのCalDavを設定する"}, "FORM": {"CALDAV_CATEGORY_FILTER": "問題をフィルタリングするカテゴリ（なしの場合は空のままにします）", "CALDAV_PASSWORD": "CalDavパスワード", "CALDAV_RESOURCE": "CalDavリソースの名前（カレンダー）", "CALDAV_URL": "CalDav URL（ベースURL）", "CALDAV_USER": "CalDavユーザー名", "IS_TRANSITION_ISSUES_ENABLED": "タスクの完了時にCalDavToDoを自動的に完了します"}, "FORM_SECTION": {"HELP": "<p>ここでは、SuperProductivityを構成して、特定のプロジェクトの未完了のCalDavタスクを日次計画ビューのタスク作成パネルに一覧表示できます。それらは提案としてリストされ、todoへのリンクとそれに関する詳細情報を提供します。</p> <p>さらに、未完了のすべてのToDoをタスクバックログに自動的に追加して同期することができます。</p>", "TITLE": "CalDav"}, "ISSUE_CONTENT": {"ASSIGNEE": "担当者", "AT": "で", "ATTACHMENTS": "添付 ファイル", "CHANGED": "変更", "COMMENTS": "コメント", "COMPONENTS": "コンポーネント", "DESCRIPTION": "説明", "LABELS": "カテゴリー", "LIST_OF_CHANGES": "変更点の一覧", "MARK_AS_CHECKED": "更新をチェック済みとしてマーク", "ON": "オン", "RELATED": "関連", "STATUS": "状態", "STORY_POINTS": "ストーリーポイント", "SUB_TASKS": "サブタスク", "SUMMARY": "概要", "WORKLOG": "ワークログ", "WRITE_A_COMMENT": "コメントを書く"}, "S": {"CALENDAR_NOT_FOUND": "CalDav：カレンダー \"{{calendarName}}\"が見つかりません", "CALENDAR_READ_ONLY": "CalDav：カレンダー \"{{calendarName}}\"は読み取り専用です", "ISSUE_NOT_FOUND": "CalDav：Todo \"{{issueId}}\"がサーバー上で削除されているようです。"}}, "CALENDARS": {"BANNER": {"ADD_AS_TASK": "タスクとして追加", "FOCUS_TASK": "フォーカスタスク", "TXT": "<strong>{{title}}</strong>は<strong>{{start}}</strong>から始まります!", "TXT_MULTIPLE": "<strong>{{title}}</strong>は<strong>{{start}}</strong>から始まります!<br> (および{{nrOfOtherBanners}} 件の他のイベントが予定されています)", "TXT_PAST": "<strong>{{title}}</strong>が<strong>{{start}}</strong>に開始されました!", "TXT_PAST_MULTIPLE": "<strong>{{title}}</strong>が<strong>{{start}}</strong>に開始されました!<br> (および{{nrOfOtherBanners}} 件の他のイベントが予定されています)"}, "S": {"CAL_PROVIDER_ERROR": "カレンダー プロバイダー エラー: {{errTxt}}"}}, "CONFIG": {"S": {"UPDATE_SECTION": "<strong>{{sectionKey}}</strong>の設定を更新しました"}}, "D_RATE": {"A_HOW": "評価方法と場所", "BTN_DONT_BOTHER": "二度と気にしないで", "TITLE": "🙈 ご容赦ください、しかし...", "TXT": "あなたがそれを好きなら、あなたはそれに良い評価を与えることによって、プロジェクトを大いに助けるでしょう、 <strong>!</strong>"}, "DOMINA_MODE": {"FORM": {"HELP": "タスクの時間を追跡する際、設定されたフレーズをXごとに繰り返す。", "L_INTERVAL": "フレーズを繰り返す間隔", "L_TEXT": "テキスト", "L_TEXT_DESCRIPTION": "例えば\"${currentTaskTitle}に取り組む！\"", "L_VOICE": "ボイスを選択", "L_VOICE_DESCRIPTION": "音声を選択する", "TITLE": "ドミナ・モード"}}, "DROPBOX": {"S": {"ACCESS_TOKEN_ERROR": "Dropbox：認証コードからアクセストークンを生成できません", "ACCESS_TOKEN_GENERATED": "Dropbox：認証コードから生成されたアクセストークン", "AUTH_ERROR": "Dropbox：提供された無効なアクセストークン", "AUTH_ERROR_ACTION": "トークンの変更", "OFFLINE": "Dropbox：オフラインのため、同期できません", "SYNC_ERROR": "Dropbox：同期中のエラー", "UNABLE_TO_GENERATE_PKCE_CHALLENGE": "Dropbox: PKCE チャレンジを生成できません。"}}, "FINISH_DAY_BEFORE_EXIT": {"C": {"FINISH_DAY_BEFORE_EXIT": "今日のリストには、まだアーカイブに移動されていない {{nr}} の完了したタスクがあります。あなたは本当にあなたの一日を終えることなくやめたいですか？"}}, "FOCUS_MODE": {"B": {"SESSION_RUNNING": "フォーカスセッションが進行中です", "TO_FOCUS_OVERLAY": "フォーカスオーバーレイ"}, "BACK_TO_PLANNING": "計画に戻る", "CONGRATS": "セッションの完了おめでとうございます!", "CONTINUE_FOCUS_SESSION": "フォーカスセッションを続ける", "COUNTDOWN": "カウントダウン", "FINISH_TASK_AND_SELECT_NEXT": "タスクを終了して次を選択", "FLOWTIME": "フロータイム", "FOR_TASK": "タスク用", "GET_READY": "集中セッションの準備をしましょう!", "GO_TO_PROCRASTINATION": "先延ばしにするときは助けを求める", "GOGOGO": "行け、行け、行け！", "NEXT": "次", "ON": "オン", "OPEN_ISSUE_IN_BROWSER": "ブラウザで問題を開く", "POMODORO_BACK": "バック", "POMODORO_DISABLE": "ポモドーロを無効にする", "POMODORO_INFO": "ポモドーロ タイマーが有効になっている場合、フォーカス セッションを併用することはできません。", "PREP_GET_MENTALLY_READY": "集中して生産的になるために精神的に準備する", "PREP_SIT_UPRIGHT": "まっすぐに座る(または立つ)", "PREP_STRETCH": "軽いストレッチをする", "SELECT_ANOTHER_TASK": "別のタスクを選択", "SELECT_TASK": "集中するタスクを選択", "SESSION_COMPLETED": "フォーカスセッションが完了しました!", "SET_FOCUS_SESSION_DURATION": "フォーカスセッションの継続時間を設定する", "SHOW_HIDE_NOTES_AND_ATTACHMENTS": "タスクのメモと添付ファイルを表示/非表示", "START_FOCUS_SESSION": "フォーカスセッションを開始", "START_NEXT_FOCUS_SESSION": "次のフォーカスセッションを開始", "WORKED_FOR": "あなたが働いていたのは"}, "GITEA": {"DIALOG_INITIAL": {"TITLE": "プロジェクト用にGiteaをセットアップする"}, "FORM": {"FILTER_USER": "ユーザー名 (例:自分で変更をフィルタリングする場合)", "HOST": "ホスト (例:https://try.gitea.io)", "REPO_FULL_NAME": "ユーザー名または組織名/プロジェクト名", "REPO_FULL_NAME_DESCRIPTION": "ブラウザでプロジェクトを表示するときに、URL の一部として見つかります。", "SCOPE": "範囲", "SCOPE_ALL": "全て", "SCOPE_ASSIGNED": "私に割り当てられた", "SCOPE_CREATED": "私が作った", "TOKEN": "アクセストークン"}, "FORM_SECTION": {"HELP": "<p>ここで SuperProductivity を設定し、日次計画ビューのタスク作成パネルで特定のリポジトリに対して未解決の Gitea 課題を一覧表示させることができます。それらは提案としてリストされ、課題へのリンクとそれに関する詳細情報が提供されます。</p> <p>さらに、すべてのオープン課題を自動的に追加してインポートすることもできます。</p><p>利用制限を通過してアクセスするには、アクセストークンを提供することができます。", "TITLE": "<PERSON><PERSON><PERSON>"}, "ISSUE_CONTENT": {"ASSIGNEE": "譲受人", "AT": "で", "DESCRIPTION": "説明", "LABELS": "ラベル", "MARK_AS_CHECKED": "更新をチェック済みとしてマーク", "PROJECT": "プロジェクト", "STATUS": "ステータス", "SUMMARY": "概要", "WRITE_A_COMMENT": "コメントを書く"}, "S": {"ERR_UNKNOWN": "Gitea：不明なエラー {{statusCode}} {{errorMsg}}.Api レートの制限を超えましたか？"}}, "GITHUB": {"DIALOG_INITIAL": {"TITLE": "プロジェクト用GitHubのセットアップ"}, "FORM": {"FILTER_USER": "ユーザー名（例：自分で変更を除外するため）", "INVALID_TOKEN_MESSAGE": "有効な GitHub トークンではありません。ghp_\" で始まる必要があります。", "IS_ASSIGNEE_FILTER": "私に割り当てられた課題のみをバックログにインポートする", "REPO": "username/repositoryName", "TOKEN": "アクセストークン"}, "FORM_SECTION": {"HELP": "<p>ここでは、毎日の計画ビューのタスク作成パネルに、特定のリポジトリの未解決のGitHub issueを一覧表示するようにSuperProductivityを設定できます。それらは提案としてリストされ、課題へのリンクとそれに関する詳細情報を提供します。</p> <p>さらに、未解決の課題をすべて自動的にタスクバックログに追加して同期できます。</p>", "TITLE": "GitHub"}, "ISSUE_CONTENT": {"ASSIGNEE": "担当者", "AT": "で", "DESCRIPTION": "説明", "LABELS": "ラベル", "LAST_COMMENT": "最後のコメント", "LOAD_ALL_COMMENTS": "すべての {{nr}} コメントをロードする", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "説明とすべてのコメントを読み込む", "MARK_AS_CHECKED": "更新をチェック済みとしてマーク", "STATUS": "状態", "SUMMARY": "概要", "WRITE_A_COMMENT": "コメントを書く"}, "S": {"CONFIG_ERROR": "GitHub：データのマッピング中にエラーが発生しました。リポジトリ名は正しいですか？", "ERR_UNKNOWN": "GitHub：不明なエラー {{statusCode}} {{errorMsg}}"}}, "GITLAB": {"DIALOG_INITIAL": {"TITLE": "プロジェクト用GitLabのセットアップ"}, "*********************": {"PAST_DAY_INFO": "プレフィルド・デュレーションには、過去数日間のトラッキングされていないデータが含まれている。", "T_ALREADY_TRACKED": "すでに追跡済み", "T_TITLE": "タイトル", "T_TO_BE_SUBMITTED": "提出予定", "TITLE": "Issueに費やした時間をGitLabに提出する", "TOTAL_MSG": "今日は合計で <em>{{totalTimeToSubmit}}</em> の作業時間を <em>{{nrOfTasksToSubmit}}</em> の異なる問題に送信します。"}, "FORM": {"FILTER": "カスタムフィルター", "FILTER_DESCRIPTION": "Https://docs.gitlab.com/ee/api/issues.html#list-issues を参照。複数の組み合わせは", "FILTER_USER": "ユーザー名（例：自分で変更を除外するため）", "GITLAB_BASE_URL": "カスタムGitLabベースURL", "PROJECT": "フルパスまたはユーザー名/プロジェクト", "PROJECT_HINT": "例：ヨハネスジョ／超生産性", "SCOPE": "範囲", "SCOPE_ALL": "すべて", "SCOPE_ASSIGNED": "私に割り当てられた", "SCOPE_CREATED": "自分で作成", "SOURCE": "ソース", "SOURCE_GLOBAL": "すべて", "SOURCE_GROUP": "グループ", "SOURCE_PROJECT": "プロジェクト", "SUBMIT_TIMELOGS": "タイムログをGitlabに提出する", "SUBMIT_TIMELOGS_DESCRIPTION": "終了日をクリックした後、タイムトラッキングダイアログを表示する", "TOKEN": "アクセストークン"}, "FORM_SECTION": {"HELP": "<p>ここでは、SuperProductivityを構成して、特定のプロジェクトの開いているGitLab（オンラインバージョンまたはセルフホストインスタンス）の課題を日次計画ビューのタスク作成パネルに一覧表示できます。それらは提案としてリストされ、課題へのリンクとそれに関する詳細情報を提供します。</p> <p>さらに、すべての未解決の課題をタスクバックログに自動的に追加して同期できます。</p>", "TITLE": "GitLab"}, "ISSUE_CONTENT": {"ASSIGNEE": "担当者", "AT": "で", "DESCRIPTION": "説明", "LABELS": "ラベル", "MARK_AS_CHECKED": "更新をチェック済みとしてマーク", "PROJECT": "プロジェクト", "STATUS": "状態", "SUMMARY": "概要", "WRITE_A_COMMENT": "コメントを書く"}, "S": {"ERR_UNKNOWN": "GitLab：不明なエラー {{statusCode}} {{errorMsg}}"}}, "ISSUE": {"CROSS_ORIGIN_BROWSER_WARNING": "この統合は、お使いのブラウザでは動作しない可能性があります。デスクトップ版またはAndroid版のSuper Productivityをダウンロードしてください！", "DEFAULT": {"ISSUE_STR": "問題", "ISSUES_STR": "課題"}, "DEFAULT_PROJECT_DESCRIPTION": "課題から作成されたタスクに割り当てられたプロジェクト", "DEFAULT_PROJECT_LABEL": "デフォルトの超生産性プロジェクト", "HOW_TO_GET_A_TOKEN": "トークンの入手方法は？", "ISSUE_CONTENT": {"ASSIGNEE": "担当者", "AT": "で", "ATTACHMENTS": "添付 ファイル", "AUTHOR": "著者", "CATEGORY": "カテゴリ", "CHANGED": "変更", "COMMENTS": "コメント", "COMPONENTS": "コンポーネント", "DESCRIPTION": "形容", "DONE_RATIO": "完了率", "DUE_DATE": "期日", "LABELS": "ラベル", "LAST_COMMENT": "最後のコメント", "LIST_OF_CHANGES": "変更点の一覧", "LOAD_ALL_COMMENTS": "すべての {{nr}} コメントをロードする", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "説明とすべてのコメントをロードする", "LOCATION": "場所", "MARK_AS_CHECKED": "更新をチェック済みとしてマークする", "ON": "オン", "PRIORITY": "優先権", "RELATED": "関連", "START": "始める", "STATUS": "地位", "STORY_POINTS": "ストーリーポイント", "SUB_TASKS": "サブタスク", "SUMMARY": "概要", "TIME_SPENT": "滞在時間", "TYPE": "種類", "VERSION": "バージョン", "WORKLOG": "ワークログ", "WRITE_A_COMMENT": "コメントを書く"}, "S": {"ERR_GENERIC": "{{issue<PERSON><PERSON><PERSON>Name}} エラー： {{errTxt}}", "ERR_NETWORK": "{{issueProviderName}}：クライアント側のネットワークエラーが原因でリクエストが失敗しました", "ERR_NOT_CONFIGURED": "{{issue<PERSON>roviderName}}：適切に構成されていません", "IMPORTED_MULTIPLE_ISSUES": "{{issueProviderName}}： {{nr}} 新しい {{issuesStr}} をバックログにインポートしました", "IMPORTED_SINGLE_ISSUE": "{{issueProviderName}}：バックログにインポートされた {{issueStr}} \"{{issueTitle}}\"", "ISSUE_DELETED_OR_CLOSED": "{{issueProviderName}}： {{issueStrC}} \"{{issueTitle}}\"は削除されたか、閉じられたようです", "ISSUE_NO_UPDATE_REQUIRED": "{{issueProviderName}}：更新は必要ありません", "ISSUE_UPDATE_MULTIPLE": "{{issueProviderName}}： {{nr}} {{issuesStr}}のデータを更新しました", "ISSUE_UPDATE_SINGLE": "{{issueProviderName}}：「{{issueTitle}}」のデータを更新しました", "MANUAL_UPDATE_ISSUE_SUCCESS": "{{issueProviderName}}：「{{issueTitle}}」のデータを更新しました", "MISSING_ISSUE_DATA": "{{issueProviderName}}： {{issueStr}} データが欠落しているタスクが見つかりました。リロード。", "NEW_COMMENT": "{{issueProviderName}}：「{{issueTitle}}」の新しいコメント", "POLLING_BACKLOG": "{{issueProviderName}}：新しい {{issuesStr}}のポーリング", "POLLING_CHANGES": "{{issueProviderName}}： {{issuesStr}}のポーリングの変更"}}, "JIRA": {"BANNER": {"BLOCK_ACCESS_MSG": "Jira：APIからの締め出しを防ぐために、アクセスはSuper Productivityによってブロックされています。あなたはおそらくあなたのJira設定をチェックするべきです！", "BLOCK_ACCESS_UNBLOCK": "ブロック解除"}, "CFG_CMP": {"ALWAYS_ASK": "常にダイアログを開く", "DO_NOT": "移行しない", "DONE": "タスク完了の状況", "ENABLE": "Jira連携を有効にする", "ENABLE_TRANSITIONS": "遷移処理を有効にする", "IN_PROGRESS": "開始タスクの状況", "LOAD_SUGGESTIONS": "候補を読み込む", "MAP_CUSTOM_FIELDS": "カスタムフィールドのマッピング", "MAP_CUSTOM_FIELDS_INFO": "残念ながら、Jiraのデータの中には、インストールごとに異なるカスタムフィールドの下に保存されているものがあります。このデータを含める場合は、適切なカスタムフィールドを選択する必要があります。", "OPEN": "一時停止中のタスクのステータス", "SELECT_ISSUE_FOR_TRANSITIONS": "利用可能なトランジションをロードするために課題を選択", "STORY_POINTS": "ストーリーポイント", "TRANSITION": "トランジション・ハンドリング"}, "DIALOG_CONFIRM_ASSIGNMENT": {"MSG": "<strong>{{summary}}</strong> は現在 <strong>{{assignee}}</strong>に割り当てられています。自分に割り当てますか？", "OK": "やれ！"}, "DIALOG_INITIAL": {"TITLE": "プロジェクトのセットアップJira"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "割り当てるステータスを選択", "CURRENT_ASSIGNEE": "現在の担当者：", "CURRENT_STATUS": "現在の状態：", "TASK_NAME": "タスク名：", "TITLE": "Jira：ステータス更新", "UPDATE_STATUS": "最新状況"}, "DIALOG_WORKLOG": {"CHECKBOXES": {"ALL_TIME": "常にタスクに費やされたすべての時間をデフォルトとして使用します", "ALL_TIME_MINUS_LOGGED": "常に、費やした時間からログに記録された時間を差し引いたもののみをデフォルトとして使用します", "TIME_SPENT_TODAY": "常に今日費やした時間のみをデフォルトとして使用します", "TIME_SPENT_YESTERDAY": "常に昨日費やした時間のみをデフォルトとして使用します"}, "CURRENTLY_LOGGED": "現在記録されている時間：", "INVALID_DATE": "入力された値は日付ではありません。", "SAVE_WORKLOG": "作業ログを保存", "STARTED": "始めました", "SUBMIT_WORKLOG_FOR": "の仕事ログをJiraに提出する", "TIME_SPENT": "使った時間", "TIME_SPENT_TOOLTIP": "異なる時間を追加する", "TITLE": "Jira：作業記録を提出する"}, "FORM_ADV": {"AUTO_ADD_BACKLOG_JQL_QUERY": "バックログにタスクを自動的に追加するために使用されるJQL", "IS_ADD_WORKLOG_ON_SUB_TASK_DONE": "サブタスクが完了したときに作業ログをJiraに送信するためのダイアログを開く", "IS_CHECK_TO_RE_ASSIGN_TICKET_ON_TASK_START": "現在作業中の課題が現在のユーザーに割り当てられているかどうかを確認してください", "IS_WORKLOG_ENABLED": "タスクが完了したら作業ログをJiraに送信するためのダイアログを開く", "SEARCH_JQL_QUERY": "検索者のタスクを制限するためのJQLクエリ", "WORKLOG_DEFAULT_ALL_TIME": "タスクに費やしたすべての時間を記入してください", "WORKLOG_DEFAULT_ALL_TIME_MINUS_LOGGED": "費やした時間から記録した時間を差し引いたすべての時間を入力します", "WORKLOG_DEFAULT_TIME_MODE": "ダイアログのデフォルトの時間値", "WORKLOG_DEFAULT_TODAY": "今日過ごした時間だけを埋める", "WORKLOG_DEFAULT_YESTERDAY": "昨日過ごした時間だけを埋める"}, "FORM_CRED": {"ALLOW_SELF_SIGNED": "自己署名証明書を許可する", "HOST": "ホスト（例：http://my-host.de:1234）", "PASSWORD": "トークン/パスワード", "USE_PAT": "パスワードの代わりにパーソナルアクセストークンを使用する", "USER_NAME": "電子メール/ユーザー名", "WONKY_COOKIE_MODE": "Wonky Cookieフォールバック認証（デスクトップアプリのみ）"}, "FORM_SECTION": {"ADV_CFG": "詳細設定", "HELP_ARR": {"H1": "基本構成", "H2": "作業ログ設定", "H3": "デフォルト遷移", "P1_1": "ログイン名（プロフィールページにあります）と <a target=\"_blank\" href=\"https://confluence.atlassian.com/cloud/api-tokens-938839638.html\" target=\"_blank\">APIトークン</a> 、または何らかの理由でパスワードを生成できない場合はパスワードを入力してください。新しいバージョンのJiraがトークンでしか動作しないことがあります。", "P1_2": "また、Jiraからタスクを追加するための提案に使用されるJQLクエリを指定する必要もあります。このリンクを確認する必要がある場合は、 <a target=\"_blank\" href=\"https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html\" target=\"_blank\">https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html</a>をご覧ください。", "P1_3": "自動的に（たとえば、計画ビューにアクセスするたびに）したい場合は、カスタムJQLクエリで指定されたすべての新しいタスクをバックログに追加するように設定することもできます。", "P1_4": "別の選択肢は「現在のチケットが現在のユーザに割り当てられているかどうかを確認する」です。有効にして開始すると、現在Jiraでそのチケットに割り当てられているかどうかがチェックされます。そうでない場合は、チケットを自分に割り当てることを選択できるダイアログが表示されます。", "P2_1": "いつ、どのように作業ログを送信するかを決定するためのいくつかのオプションがあります。タスクが完了したときに作業ログをJiraに追加するための <em>'作業ログダイアログを開く'</em> を有効にすると、Jiraタスクに完了のマークを付けるたびに作業ログを追加するダイアログが開きます。そのため、これまで追跡してきたすべてのものの上に作業ログが追加されることに注意してください。そのため、タスクを2回目の完了としてマークした場合、そのタスクの全作業時間を再度送信することは望ましくないかもしれません。", "P2_2": "<em>'サブタスクが完了したときにワークログダイアログを開き、サブタスク自体を持つタスクには対しては開きません」</em> は、Jira 課題のサブタスクを完了としてマークするたびにワークログ ダイアログを開きます。サブタスクを使用して時間を追跡しているため、Jira タスク自体を完了としてマークするとダイアログは開かなくなります。", "P2_3": "<em>'ダイアログなしで作業ログに更新を自動的に送信'</em> はそれが言うことをします。タスクを複数回行われたものとしてマークすると、全体の作業時間が2回追跡されることになるため、これはお勧めできません。", "P3_1": "ここでデフォルトのトランジションを再設定できます。 JiraはあなたのJiraアジャイルボード上の異なるコラムとして通常実行される移行の幅広い設定を可能にします。タスクをいつどこに移行するかについて仮定することはできず、手動で設定する必要があります。"}}, "ISSUE_CONTENT": {"ASSIGNEE": "担当者", "AT": "で", "ATTACHMENTS": "添付ファイル", "CHANGED": "かわった", "COMMENTS": "コメント", "COMPONENTS": "コンポーネント", "DESCRIPTION": "説明", "LIST_OF_CHANGES": "変更リスト", "MARK_AS_CHECKED": "更新をチェック済みとしてマーク", "ON": "に", "RELATED": "関連", "STATUS": "状態", "STORY_POINTS": "ストーリーポイント", "SUB_TASKS": "サブタスク", "SUMMARY": "概要", "WORKLOG": "作業記録", "WRITE_A_COMMENT": "コメントを書く"}, "S": {"ADDED_WORKLOG_FOR": "<PERSON>ra： {{issueKey}}の作業ログを追加しました", "EXTENSION_NOT_LOADED": "Super Productivity Extensionがロードされていません。ページをリロードすると役に立つかもしれません", "INSUFFICIENT_SETTINGS": "Jiraの設定が不十分", "INVALID_RESPONSE": "Jira：応答に無効なデータが含まれていました", "ISSUE_NO_UPDATE_REQUIRED": "<PERSON><PERSON>： \"{{issueText}}\"はすでに最新です", "MANUAL_UPDATE_ISSUE_SUCCESS": "<PERSON><PERSON>： \"{{issueText}}\"のデータを更新しました", "MISSING_ISSUE_DATA": "Jira：課題データが欠けているタスクが見つかりました。再読み込みしています。", "NO_AUTO_IMPORT_JQL": "Jira：自動インポート用に定義された検索クエリはありません", "NO_VALID_TRANSITION": "Jira：有効な遷移が設定されていません", "TIMED_OUT": "Jira：リクエストがタイムアウトしました", "TRANSITION": "<PERSON>ra：issue \"{{issueKey}}\"を \"{{name}}\"に設定", "TRANSITION_SUCCESS": "Jira：課題 {{issueKey}} を <strong>に設定してください{{chosenTransition}}</strong>", "TRANSITIONS_LOADED": "Jira：トランジションがロードされました。それらを割り当てるために下記の選択を使用してください", "UNABLE_TO_REASSIGN": "Jira：ユーザー名を指定しなかったので、自分にチケットを再割り当てすることができません。設定をご覧ください。"}, "STEPPER": {"CREDENTIALS": "資格情報", "DONE": "これで終わりです。", "LOGIN_SUCCESS": "ログイン成功！", "TEST_CREDENTIALS": "テスト資格情報", "WELCOME_USER": " {{user}}さん、ようこそ！"}}, "MARKDOWN_PASTE": {"CONFIRM_ADD_TO_SUB_TASK_NOTES": "貼り付けたマークダウンリストをサブタスク「{{parentTaskTitle}}」のノートに追加しますか?", "CONFIRM_PARENT_TASKS": "貼り付けたマークダウンリストから</strong>  <strong>{{tasksCount}} 新しいタスクを作成しますか?", "CONFIRM_PARENT_TASKS_WITH_SUBS": "新しいタスクを作成し <strong>{{tasksCount}} 貼り付けたマークダウンリストから</strong> サブタスク {{subTasksCount}} しますか?", "CONFIRM_SUB_TASKS": "貼り付けたマークダウンリストから新しいサブタスクを {{tasksCount}} 作成しますか?", "CONFIRM_SUB_TASKS_WITH_PARENT": "貼り付けたマークダウンリストから「{{parentTaskTitle}}」</strong> の下に新しいサブタスクを <strong>{{tasksCount}} 作成しますか?", "DIALOG_TITLE": "貼り付けられたMarkdownリストが検出されました!"}, "METRIC": {"BANNER": {"CHECK": "やったよ！"}, "CMP": {"AVG_BREAKS_PER_DAY": "一日あたりの平均休憩回数", "AVG_TASKS_PER_DAY_WORKED": "一日あたりの平均タスク数", "AVG_TIME_SPENT_ON_BREAKS": "平均休憩時間", "AVG_TIME_SPENT_PER_DAY": "一日あたりの平均消費時間", "AVG_TIME_SPENT_PER_TASK": "タスクあたりの平均消費時間", "COUNTING_SUBTASKS": "（サブタスク数）", "DAYS_WORKED": "稼動日数", "GLOBAL_METRICS": "グローバルメトリクス", "IMPROVEMENT_SELECTION_COUNT": "改善要因が選択された回数", "MOOD_PRODUCTIVITY_OVER_TIME": "時間とともに気分と生産性", "NO_ADDITIONAL_DATA_YET": "追加データはまだ収集されていません。そのためには、日次要約の「評価」パネルのフォームを使用してください。", "OBSTRUCTION_SELECTION_COUNT": "妨害要因が選択された回数", "SIMPLE_CLICK_COUNTERS_OVER_TIME": "時間の経過とともにカウンターをクリックします", "SIMPLE_COUNTERS": "シンプルなカウンター", "SIMPLE_STOPWATCH_COUNTERS_OVER_TIME": "時間の経過とともにストップウォッチカウンター", "TASKS_DONE_CREATED": "タスク（完了/作成済み）", "TIME_ESTIMATED": "推定時間", "TIME_SPENT": "使った時間"}, "EVAL_FORM": {"ADD_NOTE_FOR_TOMORROW": "明日のメモを追加", "DISABLE_REPEAT_EVERY_DAY": "毎日繰り返しを無効にする", "ENABLE_REPEAT_EVERY_DAY": "毎日繰り返す", "HELP_H1": "なぜ私は気にする必要がありますか？", "HELP_LINK_TXT": "評価指標セクションに移動します", "HELP_P1": "ちょっとした自己評価の時間です！ここでのあなたの答えは保存されており、メトリックのセクションであなたがどのように働いているかに関する少しの統計をあなたに提供します。さらに明日のための提案は翌日あなたのタスクリストの上に現れるでしょう。", "HELP_P2": "これは、正確な測定基準を計算することや、自分の仕事について感じていることを改善することよりも、機械のように効率的になることを減らすことを意図しています。それはあなたを助ける要因を見つけることであるだけでなく、あなたの毎日の日常生活の中で痛みのポイントを評価するのに役立ちます。ほんの少し体系的であることはうまくいけばこれらをよりよく把握し、あなたができることを改善するのに役立ちます。", "IMPROVEMENTS": "生産性が向上した理由は何ですか？", "IMPROVEMENTS_TOMORROW": "明日を改善するために何ができるでしょうか。", "MOOD": "気分はどうですか？", "MOOD_HINT": "1：ひどい -  10：素晴らしい", "NOTES": "明日のメモ", "OBSTRUCTIONS": "あなたの生産性を妨げたものは何ですか？", "PRODUCTIVITY": "どのくらい効率的でしたか？", "PRODUCTIVITY_HINT": "1：まだ始まっていない -  10：非常に効率的"}, "S": {"SAVE_METRIC": "メトリックを保存しました"}}, "NOTE": {"D_ADD": {"NOTE_LABEL": "メモとして保存するテキストを入力してください..."}, "D_FULLSCREEN": {"VIEW_PARSED": "解析された（編集不可能な）マークダウンとして表示する", "VIEW_SPLIT": "解析されたマークダウンと解析されていないマークダウンを分割表示する", "VIEW_TEXT_ONLY": "解析されていないテキストとして表示"}, "NOTE_CMP": {"DISABLE_PARSE": "マークダウン解析を無効にする", "ENABLE_PARSE": "マークダウン解析を有効にする"}, "NOTES_CMP": {"ADD_BTN": "新しいメモを追加", "DROP_TO_ADD": "新しいメモを追加するには、ここにドロップしてください", "NO_NOTES": "現在、メモはありません"}, "S": {"NOTE_ADDED": "保存されたノート"}}, "OPEN_PROJECT": {"CFG_CMP": {"ALWAYS_ASK": "常にダイアログを開く", "DO_NOT": "移行しない", "DONE": "タスク完了のステータス", "ENABLE": "Openproject統合を有効にする", "ENABLE_TRANSITIONS": "トランジション処理を有効にする", "IN_PROGRESS": "タスク開始のステータス", "OPEN": "タスクを一時停止するステータス", "PROGRESS_ON_SAVE": "保存時のデフォルトの進行状況", "SELECT_ISSUE_FOR_TRANSITIONS": "Issueを選択して利用可能なトランジションを読み込む", "TRANSITION": "トランジション・ハンドリング"}, "DIALOG_INITIAL": {"TITLE": "プロジェクト用にOpenProjectをセットアップする"}, "DIALOG_TRACK_TIME": {"ACTIVITY": "アクティビティ", "CURRENTLY_LOGGED": "現在記録されている時間：", "INVALID_DATE": "入力された値は日付ではありません。", "POST_TIME": "投稿時間", "STARTED": "始めました", "SUBMIT_TIME_FOR": "OpenProjectに時間を送信します", "TIME_SPENT": "使った時間", "TITLE": "OpenProject：ワークログの送信"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "割り当てるステータスを選択する", "CURRENT_ASSIGNEE": "現在の譲受人", "CURRENT_STATUS": "現在の状況", "PERCENTAGE_DONE": "進歩だ：", "TASK_NAME": "タスク名", "TITLE": "OpenProject：ステータスの更新", "UPDATE_STATUS": "更新状況"}, "FORM": {"FILTER_USER": "ユーザー名（例：自分で変更を除外するため）", "HOST": "ホスト（例：https：//www.openproject.org/）", "IS_SHOW_TIME_TRACKING_DIALOG": "OpenProjectにレポートするタイムトラッキングダイアログを表示する", "IS_SHOW_TIME_TRACKING_DIALOG_DESCRIPTION": "OpenProjectプロジェクトでタイムトラッキングモジュールを有効にする必要があります", "IS_SHOW_TIME_TRACKING_DIALOG_FOR_EACH_SUB_TASK": "サブタスクが完了したときにタイムトラッキングダイアログを表示する", "PROJECT_ID": "プロジェクトID", "PROJECT_ID_DESCRIPTION": "ブラウザでプロジェクトを表示すると、URLの一部として見つけることができます。", "SCOPE": "範囲", "SCOPE_ALL": "すべて", "SCOPE_ASSIGNED": "私に割り当てられた", "SCOPE_CREATED": "自分で作成", "TOKEN": "アクセストークン"}, "FORM_SECTION": {"HELP": "<p>ここでは、開いているOpenProjectワークパッケージを一覧表示するようにSuperProductivityを構成できます。これがブラウザで機能するためには、おそらくOpenProjectサーバーのCORSを構成して、app.super-productivity.comからのアクセスを許可する必要があることに注意してください</p>", "TITLE": "OpenProject"}, "ISSUE_CONTENT": {"ASSIGNEE": "譲受人", "ATTACHMENTS": "添付ファイル", "DESCRIPTION": "説明", "MARK_AS_CHECKED": "更新をチェック済みとしてマーク", "STATUS": "状態", "SUMMARY": "概要", "TYPE": "タイプ", "UPLOAD_ATTACHMENT": "タスクにアップロード"}, "ISSUE_STRINGS": {"ISSUE_STR": "ワークパッケージ", "ISSUES_STR": "作業パッケージ"}, "S": {"ERR_NO_FILE": "ファイルが選択されていません", "ERR_UNKNOWN": "OpenProject：不明なエラー {{statusCode}} {{errorMsg}}。 CORSはサーバー用に適切に構成されていますか？", "POST_TIME_SUCCESS": "OpenProject： {{issueTitle}}の時間エントリが正常に作成されました", "TRANSITION": "OpenProject：issue「{{issueKey}}」を「{{name}}」に設定します。\"", "TRANSITION_SUCCESS": "OpenProject：課題 {{issueKey}} を <strong>に設定 {{chosenTransition}}</strong>", "TRANSITIONS_LOADED": "トランジションがロードされました。以下のセレクトを使用して割り当てます。"}}, "PLANNER": {"D": {"ADD_PLANNED": {"ADD_TO_TODAY": "今日に追加", "RE_PLAN_ALL": "すべて再スケジュール", "TITLE": "今日の予定タスクを追加する"}}, "EDIT_REPEATED_TASK": "繰り返しタスク「{{taskName}}」を編集する", "NO_TASKS": "タスクなし", "PLAN_VIEW": {"NO_SCHEDULED_ITEMS": "予定なし"}, "S": {"REMOVED_PLAN_DATE": "タスク「{{taskTitle}}」の計画日が削除されました。", "TASK_ALREADY_PLANNED": "タスクはすでに {{date}}で計画されている。", "TASK_PLANNED_FOR": " <strong>{{date}}</strong>{{extra}}に予定されているタスク"}, "TASK_DRAWER": "タスクドロワー"}, "POMODORO": {"BACK_TO_WORK": "仕事に戻る！", "BREAK_IS_DONE": "休憩時間が終わりました！", "ENJOY_YOURSELF": "楽しんで、体を動かして、また戻ってきてね：", "FINISH_SESSION_X": "セッション <strong>{{nr}}</strong>が正常に終了しました。", "NOTIFICATION": {"BREAK_TIME": "ポモドーロ休憩時間 {{nr}}！", "BREAK_X_START": "ポモドーロ：休憩 {{nr}} が始まりました！", "NO_TASKS": "ポモドーロタイマーを開始する前に、タスクを追加する必要があります。", "SESSION_X_START": "ポモドーロ：セッション {{nr}} が始まりました！"}, "S": {"RESET": "最初のポモドーロ・セッションにリセット", "SESSION_SKIP": "現在のポモドーロ・セッションの終わりまでスキップする", "SESSION_X_START": "ポモドーロ：セッション {{nr}} が始まりました！"}, "SKIP_BREAK": "休憩をスキップする", "START_BREAK": "休憩開始"}, "PROCRASTINATION": {"BACK_TO_WORK": "仕事に戻る！", "COMP": {"INTRO": "自分自身を思いやるのはよい考えです。自己効力感を高め、前向きな感情を育み、そしてもちろん先延ばしの克服にも役立ちます。ちょっとしたエクササイズを試してみましょう：", "L1": "少し座って、体を伸ばします。よければ、少し落ち着いてみましょう。", "L2": "湧き上がってくる思考や感情に耳を傾けてください", "L3": "あなたは友人に応えるように自分自身に応えていますか？", "L4": "答えが「いいえ」なら、あなたの友人を想像してみてください。あなたは彼らに何を言うでしょうか？彼らのために何をしますか？", "OUTRO": "他のエクササイズは<a target=\"_blank\" href=\"https://drsoph.com/blog/2018/9/17/3-exercises-in-self-compassion\" target=\"_blank\">こちら</a>または<a target=\"_blank\" href=\"https://www.google.com/search?q=self+compassion+exercises&oq=self+compassion+excers&aqs=chrome.1.69i57j0l5.4303j0j7&sourceid=chrome&ie=UTF-8\" target=\"_blank\">Google</a>にあります。", "TITLE": "セルフ・コンパッション"}, "CUR": {"INTRO": "先延ばしは面白いですよね。やる意味がありません。長期的な利益には全くなりません。それでも誰もがやってしまいます。楽しく探ってみましょう！", "L1": "先延ばしにしたい誘惑を駆り立てるのはどのような感情ですか？", "L2": "体のどこでそれらを感じますか？", "L3": "それらはあなたに何を思い起こさせますか？", "L4": "先延ばししたい気持ちを観察すると、それはどうなりますか？強くなりますか？消えていきますか？他の感情を引き起こしますか？", "L5": "意識をしばらく休めると、体の中の感覚はどのように変化していますか？", "PROCRASTINATION_TRIGGERS_TEXT": "先延ばしにしたい衝動に駆られたきっかけを記録するのも、非常に効果的な方法だ。例えば、私自身はブラウザのウィンドウにフォーカスが当たると、すぐにredditやお気に入りのニュースサイトに飛びつきたくなる衝動に駆られることがよくある。単純な空のテキスト文書にきっかけを書き出すようになってから、このパターンがいかに根付いているかを自覚し、さまざまな対策を試すのに役立った。", "PROCRASTINATION_TRIGGERS_TITLE": "先延ばしのきっかけを書き出す", "TITLE": "好奇心"}, "H1": "一息つきましょう！", "INTRO": {"AVOIDING": "タスクを避ける", "FEAR": "失敗への恐れ", "STRESSED": "物事を成し遂げられないことへのストレス", "TITLE": "イントロ"}, "P1": "まずはリラックス！誰でもたまにはやるものです。やるべきことをやっていないならば、せめて楽しむことです！そして、以下のセクションをチェックして、何かの参考にしてみてください。", "P2": "覚えておいてください：先延ばしは感情コントロールの問題であり、時間管理の問題ではありません。", "REFRAME": {"INTRO": "そのタスクの悪い点でも、前向きに捉えられることはないか考えてみましょう。", "TITLE": "リフレーミング", "TL1": "そのタスクについて、何か面白いことはありませんか？", "TL2": "そのタスクを完了したら、何が得られますか？", "TL3": "そのタスクを完了したら、あなたはどのように感じますか？"}, "SPLIT_UP": {"INTRO": "できるだけ多くの小さな塊にタスクを分割してください。", "OUTRO": "できましたか？そうしたら考えてみましょう。理論的に考えて、タスクに取り組むなら、最初にするのは何ですか？考えてみてください。", "TITLE": "分割する！"}}, "PROJECT": {"D_CREATE": {"CREATE": "プロジェクトを作成", "EDIT": "プロジェクトを編集", "SETUP_CALDAV": "Caldav統合のセットアップ", "SETUP_GIT": "GitHub連携のセットアップ", "SETUP_GITEA_PROJECT": "Gitea統合のセットアップ", "SETUP_GITLAB": "GitLab連携のセットアップ", "SETUP_JIRA": "Jira連携のセットアップ", "SETUP_OPEN_PROJECT": "OpenProject統合のセットアップ", "SETUP_REDMINE_PROJECT": "Redmineとの統合設定"}, "D_DELETE": {"MSG": "本当にプロジェクト「{{title}}」を削除しますか？"}, "FORM_BASIC": {"L_ENABLE_BACKLOG": "プロジェクトバックログの有効化", "L_IS_HIDDEN_FROM_MENU": "メニューからプロジェクトを非表示", "L_TITLE": "プロジェクト名", "TITLE": "基本設定"}, "FORM_THEME": {"D_IS_DARK_THEME": "システムがグローバルダークモードをサポートしている場合は使用しないでください。", "HELP": "プロジェクトのテーマ設定。", "L_BACKGROUND_IMAGE_DARK": "背景画像のURL（ダークテーマ）", "L_BACKGROUND_IMAGE_LIGHT": "背景画像のURL（ライトテーマ）", "L_COLOR_ACCENT": "アクセントカラー", "L_COLOR_PRIMARY": "プライマリーカラー", "L_COLOR_WARN": "警告/エラーの色", "L_HUE_ACCENT": "アクセントカラーの背景上の暗いテキストのしきい値", "L_HUE_PRIMARY": "プライマリーカラーの背景上の暗いテキストのしきい値", "L_HUE_WARN": "警告色の背景上の暗いテキストのしきい値", "L_IS_AUTO_CONTRAST": "読みやすくするためにテキストの色を自動設定する", "L_IS_DISABLE_BACKGROUND_GRADIENT": "背景のグラデーションを無効にする", "L_IS_REDUCED_THEME": "縮小されたUIを使用する（タスクを囲むボックスはありません）", "L_THEME_COLOR": "テーマカラー", "L_TITLE": "タイトル", "TITLE": "テーマ"}, "S": {"ARCHIVED": "アーカイブされたプロジェクト", "CREATED": "プロジェクト<strong>{{title}}</strong>を作成しました。あなたはそれを左上のメニューから選ぶことができます。", "DELETED": "削除されたプロジェクト", "E_EXISTS": "プロジェクト \"{{title}}\"は既に存在します", "E_INVALID_FILE": "プロジェクトファイルのデータが無効です", "ISSUE_PROVIDER_UPDATED": "<strong>{{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}</strong>のプロジェクト設定を更新しました", "UNARCHIVED": "未アーカイブプロジェクト", "UPDATED": "更新されたプロジェクト設定"}}, "QUICK_HISTORY": {"NO_DATA": "今年度のデータはありません", "PAGE_TITLE": "クイックヒストリー", "WEEK_TITLE": "週 {{nr}} （{{timeSpent}}）"}, "REDMINE": {"DIALOG_INITIAL": {"TITLE": "プロジェクトにRedmineをセットアップする"}, "FORM": {"API_KEY": "APIアクセスキー", "HOST": "ホスト (例: https://redmine.org)", "PROJECT_ID": "プロジェクト識別子", "PROJECT_ID_DESCRIPTION": "ブラウザでプロジェクトを表示するときに、URL の一部として見つかります。", "SCOPE": "範囲", "SCOPE_ALL": "全て", "SCOPE_ASSIGNED": "私に割り当てられた", "SCOPE_CREATED": "私が作った"}, "FORM_SECTION": {"HELP": "<p>ここで SuperProductivity を設定し、日次計画ビューのタスク作成パネルで、特定のプロジェクトの Redmine (オンライン版またはセルフホスト インスタンス) の未解決の課題をリストすることができます。それらは提案としてリストされ、課題へのリンクとそれに関する詳細情報が提供されます。</p><p>さらに、すべての未解決課題を自動的にインポートすることもできます。</p>", "TITLE": "Redmine"}, "ISSUE_CONTENT": {"AUTHOR": "著者", "DESCRIPTION": "説明", "MARK_AS_CHECKED": "更新をチェック済みとしてマーク", "PRIORITY": "優先順位", "STATUS": "ステータス"}, "S": {"ERR_UNKNOWN": "Redmine：不明なエラー {{statusCode}} {{errorMsg}}."}}, "REMINDER": {"COUNTDOWN_BANNER": {"HIDE": "隠す", "START_NOW": "今すぐ始める", "TXT": "<strong>{{title}}</strong> は <strong>から始まる {{start}}</strong>!", "TXT_MULTIPLE": "<strong>{{title}}</strong> は <strong>から始まる {{start}}</strong>!<br> (そして {{nrOfOtherBanners}} 他のタスクの期限)"}, "S_ACTIVE_TASK_DUE": "現在取り組んでいるタスクの期限が迫っています！<br/> ({{title}})", "S_REMINDER_ERR": "リマインダーインターフェースのエラー"}, "SCHEDULE": {"CONTINUED": "続く", "D_INITIAL": {"TEXT": "<p>タイムラインのアイデアは、計画されたタスクが時間の経過とともにどのように実行されるかをより正確に把握することです。これはタスクから自動的に生成され、2つの異なるものを区別します。計画された時間に表示される <strong>スケジュールされたタスク</strong>と、それらの固定イベントの周りを流れる <strong>通常のタスク</strong> です。すべてのタスクは、割り当てられた時間の見積もりを考慮します。</p><p>これに加えて、作業の開始時間と終了時間を指定することもできます。構成されている場合、通常のタスクはこれらの外に表示されることはありません。タイムラインには、今後30日間のみが含まれることに注意してください。</p>", "TITLE": "タイムライン"}, "END": "作業終了", "LUNCH_BREAK": "昼休憩", "NO_TASKS": "現在、タスクはありません。トップバーの+ボタンからいくつかのタスクを追加してください。", "NOW": "今", "PLAN_END_DAY": "一日の終わりに計画を立てる", "PLAN_START_DAY": "一日の始まりに計画を立てる", "START": "作業開始", "TASK_PROJECTION_INFO": "スケジュールされた反復可能なタスクの将来の予測"}, "SEARCH_BAR": {"INFO": "リストアイコンをクリックして、アーカイブされたタスクを検索します", "INFO_ARCHIVED": "アーカイブアイコンをクリックして、通常のタスクを検索します", "NO_RESULTS": "検索に一致するタスクが見つかりませんでした", "PLACEHOLDER": "タスクまたはタスクの説明を検索する", "PLACEHOLDER_ARCHIVED": "アーカイブされたタスクを検索する", "TOO_MANY_RESULTS": "結果が多すぎるため、検索を絞り込んでください"}, "SIMPLE_COUNTER": {"D_CONFIRM_REMOVE": {"MSG": "単純なカウンターを削除すると、そのカウンターで追跡されている過去のデータもすべて削除されます。続行してもよろしいですか？", "OK": "やれ！"}, "D_EDIT": {"CURRENT_STREAK": "現在のストリーク", "DAILY_GOAL": "日々の目標", "DAYS": "日", "L_COUNTER": "カウント"}, "FORM": {"ADD_NEW": "シンプルなカウンターを追加", "HELP": "ここで、右上に表示されるシンプルなボタンを設定できます。タイマー、またはクリックするだけでカウントアップされる単純なカウンターのいずれかです。", "L_COUNTDOWN_DURATION": "カウントダウン時間", "L_DAILY_GOAL": "連勝のための日々の目標", "L_ICON": "アイコン", "L_ICON_ON": "切り替え時のアイコン", "L_IS_ENABLED": "有効", "L_TITLE": "タイトル", "L_TRACK_STREAKS": "トラックストリーク", "L_TYPE": "タイプ", "L_WEEKDAYS": "平日の連休チェック", "TITLE": "シンプルなカウンター", "TYPE_CLICK_COUNTER": "カウンターをクリック", "TYPE_REPEATED_COUNTDOWN": "繰り返しカウントダウン", "TYPE_STOPWATCH": "ストップウォッチ"}, "S": {"GOAL_REACHED_1": "今日の目標は達成した！", "GOAL_REACHED_2": "現在の連勝期間"}}, "SYNC": {"A": {"ARCHIVE_ONLY_UPLOADED": "データが一部しかアップロードされませんでした。後で再試行してください！そうしないと、他のデバイスにデータを同期することができません。", "POSSIBLE_LEGACY_DATA": "Super Productivity は同期を改善し、1 つのファイルではなく 2 つの別々のファイルを使用するようになりました。Super Productivity のすべてのインスタンスを更新し、データが最も新しいアプリインスタンスから最初にデータを同期することをお勧めします。ローカルデバイスのデータである場合は、この警告を無視して、次のダイアログを確認してアップロードしてください。", "REMOTE_MODEL_VERSION_NEWER": "リモートモデルのバージョンは、ローカルのものよりも新しいです。ローカルアプリを最新バージョンに更新してください。"}, "C": {"EMPTY_SYNC": "空のデータオブジェクトを同期しようとしています。これは（ほぼ）未使用のアプリの最初の同期ですか？", "FORCE_UPLOAD": "とにかくローカルデータをアップロードしますか？", "FORCE_UPLOAD_AFTER_ERROR": "ローカルデータのアップロード中にエラーが発生しました。強制的に更新しますか？", "MIGRATE_LEGACY": "インポート中にレガシーデータが検出されましたが、移行してみますか？", "NO_REMOTE_DATA": "リモートデータが見つかりません。ローカルをリモートにアップロードしますか？", "TRY_LOAD_REMOTE_AGAIN": "リモートからもう一度データを再ロードしてみますか？", "UNABLE_TO_LOAD_REMOTE_DATA": "リモートからデータをロードできません。リモートデータをローカルデータで上書きしますか？リモートデータはすべて失われます。"}, "D_AUTH_CODE": {"FOLLOW_LINK": "次のリンクを開き、そこに提供されている認証コードを下の入力フィールドにコピーしてください。", "GET_AUTH_CODE": "認証コードを取得します", "L_AUTH_CODE": "認証コードを入力してください", "TITLE": "ログイン： {{provider}}"}, "D_CONFLICT": {"LAMPORT_CLOCK": "改定", "LAST_CHANGE": "最終変更：", "LAST_SYNC": "最終同期：", "LOCAL": "ローカル", "LOCAL_REMOTE": "ローカル→リモート", "REMOTE": "リモート", "TEXT": "<p>リモートから更新します。ローカルデータとリモートデータの両方が変更されているようです。</p>", "TIMESTAMP": "タイムスタンプ", "TITLE": "同期：矛盾するデータ", "USE_LOCAL": "ローカルを使用", "USE_REMOTE": "リモートを使用"}, "D_DECRYPT_ERROR": {"BTN_OVER_WRITE_REMOTE": "リモートの変更と上書き", "CHANGE_PW_AND_DECRYPT": "変更と復号化の試行", "P1": "データは暗号化され、復号化に失敗しました。正しいパスワードを入力してください。", "P2": "パスワードを変更することもでき、これによりすべてのリモートデータが上書きされます。", "PASSWORD": "パスワード"}, "D_INCOMPLETE_SYNC": {"BTN_CLOSE_APP": "アプリを閉じる", "BTN_DOWNLOAD_BACKUP": "ローカルバックアップのダウンロード", "BTN_FORCE_UPLOAD": "ローカルへの強制アップロード", "P1": "リモート同期データが支離滅裂です!", "P2": "影響を受けるモデル:", "P3": "次の 2 つのオプションがあります。", "P4": "1.他のデバイスに移動し、そこで完全な同期を試みます。", "P5": "2. リモートデータをローカルデータで上書きします。すべてのリモート変更は失われます!", "P6": "上書きするデータのバックアップを作成することをお勧めします!!", "T1": "最後の同期が不完全だった！", "T2": "前回の同期時にアーカイブデータが正しくアップロードされませんでした：", "T3": "選択肢は2つ：", "T4": "1.他のデバイスに移動し、そこで同期を完了します。", "T5": "2.または、リモートのデータをローカルのデータで上書きすることもできます。リモートの変更はすべて\n    は失われます！", "T6": "上書きしたデータのバックアップを作成することをお勧めします！"}, "D_INITIAL_CFG": {"SAVE_AND_ENABLE": "Sync の保存と有効化", "TITLE": "Sync の設定"}, "D_PERMISSION": {"DISABLE_SYNC": "同期を無効にする", "PERM_FILE": "許可を与える", "TEXT": "<p>ローカル同期のファイル権限が取り消されました。</p>", "TITLE": "同期：ローカルファイルのパーミッションが拒否されました"}, "FORM": {"DROPBOX": {"L_ACCESS_TOKEN": "アクセストークン（認証コードから生成）"}, "GOOGLE": {"L_SYNC_FILE_NAME": "同期ファイル名"}, "L_ENABLE_COMPRESSION": "圧縮を有効にする（データ転送の高速化）", "L_ENABLE_ENCRYPTION": "エンドツーエンドの暗号化を有効にする（実験的） - 同期プロバイダがデータにアクセスできないようにする。", "L_ENABLE_SYNCING": "同期を有効にする", "L_ENCRYPTION_NOTES": "重要な注意: すべてが機能するには、次の同期の</strong> 前に、他のデバイスに同じパスワードを設定する必要があります <strong>。安全なパスワードを選択してください。また、このパスワードを忘れた場合、データにアクセスできなくなります <strong>。キーを持っているのはあなただけなので、回復は不可能です</strong>。暗号化はおそらくほとんどの攻撃者を阻止するのに十分なものですが、 <strong>保証はありません。</strong>", "L_ENCRYPTION_PASSWORD": "暗号化パスワード（忘れないこと）", "L_SYNC_INTERVAL": "同期間隔", "L_SYNC_PROVIDER": "同期プロバイダー", "LOCAL_FILE": {"L_SYNC_FILE_PATH_PERMISSION_VALIDATION": "ファイルへのアクセス許可が必要", "L_SYNC_FOLDER_PATH": "同期フォルダパス"}, "TITLE": "同期", "WEB_DAV": {"CORS_INFO": "<strong>実験的!!</strong> これを機能させるには、NextcloudインスタンスのCORSを無効にするか制限する必要があります。これは、セキュリティに悪影響を与える可能性があります。詳細については、 <a href='https://github.com/nextcloud/server/issues/3131'>このスレッド</a> を参照してください。自己責任！", "L_BASE_URL": "ベースURL", "L_PASSWORD": "パスワード", "L_SYNC_FOLDER_PATH": "同期フォルダパス", "L_USER_NAME": "ユーザー名"}}, "S": {"ALREADY_IN_SYNC": "すでに同期", "ALREADY_IN_SYNC_NO_LOCAL_CHANGES": "ローカルの変更なし - すでに同期している", "BTN_CONFIGURE": "構成", "BTN_FORCE_OVERWRITE": "強制上書き", "ERROR_DATA_IS_CURRENTLY_WRITTEN": "リモート・データは現在書き込まれています", "ERROR_FALLBACK_TO_BACKUP": "データのインポート中に問題が発生しました。ローカルバックアップにフォールバックします。", "ERROR_INVALID_DATA": "同期中にエラーが発生しました。無効なデータ", "ERROR_NO_REV": "リモートファイルに有効な rev がない", "ERROR_UNABLE_TO_READ_REMOTE_DATA": "同期中にエラーが発生しました。リモートデータを読み取れません。暗号化を有効にしていて、ローカルのパスワードがリモートデータの暗号化に使用されているものと一致しないのでは？", "IMPORTING": "データをインポートする", "INCOMPLETE_CFG": "同期の認証に失敗しました。設定を確認してください！", "INITIAL_SYNC_ERROR": "初期同期に失敗しました", "SUCCESS_DOWNLOAD": "リモートからの同期データ", "SUCCESS_IMPORT": "インポートされたデータ", "SUCCESS_VIA_BUTTON": "データが正常に同期されました", "UNKNOWN_ERROR": "同期中に不明なエラーが発生しました。コンソールを確認してください。", "UPLOAD_ERROR": "不明なアップロードエラー（設定は正しいですか？）： {{err}}"}}, "TAG": {"D_CREATE": {"CREATE": "タグを作成", "EDIT": "タグを編集"}, "D_DELETE": {"CONFIRM_MSG": "タグ「{{tagName}}」を本当に削除しますか？すべてのタスクから削除されます。これは、元に戻すことはできません。"}, "D_EDIT": {"ADD": "「{{title}}」のタグを追加", "EDIT": "「{{title}}」のタグを編集", "LABEL": "タグ"}, "FORM_BASIC": {"L_COLOR": "色（未定義のプライマリテーマの色が使用されている場合）", "L_ICON": "アイコン", "L_TITLE": "タグ名", "TITLE": "基本設定"}, "S": {"UPDATED": "タグ設定が更新されました"}, "TTL": {"ADD_NEW_TAG": "新しいタグを追加"}}, "TASK": {"ADD_TASK_BAR": {"ADD_EXISTING_TASK": "既存のタスク「{{taskTitle}}」を追加", "ADD_ISSUE_TASK": " {{issueType}}から課題番号{{issueNr}} を追加", "ADD_TASK_TO_BOTTOM_OF_BACKLOG": "バックログの最後にタスクを追加", "ADD_TASK_TO_BOTTOM_OF_TODAY": "リストの一番下にタスクを追加", "ADD_TASK_TO_TOP_OF_BACKLOG": "バックログの先頭にタスクを追加", "ADD_TASK_TO_TOP_OF_TODAY": "リストの一番上にタスクを追加", "CREATE_TASK": "新しいタスクを作成する", "EXAMPLE": "例：「Some task title + projectName #some-tag #some-other-tag 10m / 3h」", "START": "もう一度Enterキーを押して開始します", "TOGGLE_ADD_TO_BACKLOG_TODAY": "タスクのバックログへの追加／今日のリストへの追加を切り替える", "TOGGLE_ADD_TOP_OR_BOTTOM": "タスクをリストの一番上と一番下に追加するトグル"}, "ADDITIONAL_INFO": {"ADD_ATTACHMENT": "添付ファイルを追加する", "ADD_SUB_TASK": "サブタスクを追加", "ATTACHMENTS": "添付ファイル {{nr}}", "DUE": "で予定されている。", "FROM_PARENT": "（親から）", "LOCAL_ATTACHMENTS": "ローカル添付", "NOTES": "メモ", "PARENT": "親タスク", "REMINDER": "リマインダー", "REPEAT": "繰り返す", "SCHEDULE_TASK": "タスクのスケジュール", "SUB_TASKS": "サブタスク（{{nr}}）", "TIME": "時間", "TITLE_PLACEHOLDER": "タイトルを入力"}, "B": {"ADD_HALF_HOUR": "30分追加する", "ESTIMATE_EXCEEDED": "\"{{title}}\"の見積り時間を超えました"}, "CMP": {"ADD_SUB_TASK": "サブタスクを追加", "ADD_TO_MY_DAY": "今日に追加", "ADD_TO_PROJECT": "プロジェクトに追加", "CONVERT_TO_PARENT_TASK": "親タスクに変換", "DELETE": "タスクを削除", "DELETE_REPEAT_INSTANCE": "繰り返されるタスクインスタンスの削除", "DROP_ATTACHMENT": "ここにドロップして\"{{title}}\"に添付", "EDIT_SCHEDULED": "リマインダーを編集", "EDIT_TAGS": "タグを編集", "EDIT_TASK_TITLE": "編集タイトル", "FOCUS_SESSION": "フォーカス・セッション開始", "MARK_DONE": "完了としてマーク", "MARK_UNDONE": "未完了としてマーク", "MOVE_TO_BACKLOG": "バックログに移動", "MOVE_TO_OTHER_PROJECT": "他のプロジェクトに移動", "MOVE_TO_REGULAR": "今日のリストに移動", "MOVE_TO_TOP": "リストの一番上に移動する", "OPEN_ATTACH": "ファイルまたはリンクを添付", "OPEN_ISSUE": "新しいブラウザタブで課題を開く", "OPEN_TIME": "見積もり時間/消費時間の編集", "REMOVE_FROM_MY_DAY": "今日から削除", "REPEAT_EDIT": "繰り返しタスク設定の編集", "SCHEDULE": "タスクのスケジュール", "SHOW_UPDATES": "更新を表示", "TOGGLE_ATTACHMENTS": "添付ファイルの表示/非表示", "TOGGLE_DETAIL_PANEL": "追加情報を表示/非表示", "TOGGLE_DONE": "完了/未完了としてマーク", "TOGGLE_SUB_TASK_VISIBILITY": "サブタスクの表示/非表示", "TOGGLE_TAGS": "トグルタグ", "TRACK_TIME": "タイムトラッキングの開始", "TRACK_TIME_STOP": "タイムトラッキングを一時停止", "UNSCHEDULE_TASK": "タスクのスケジュール解除", "UPDATE_ISSUE_DATA": "課題データを更新"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAG": {"MSG": "新しいタグ {{tagsTxt}}を作成しますか？", "OK": "タグの作成"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAGS": {"MSG": "新しいタグ {{tagsTxt}}を作成しますか？", "OK": "タグの作成"}, "D_REMINDER_VIEW": {"ADD_ALL_TO_TODAY": "今日にすべて追加", "ADD_TO_TODAY": "今日に追加", "DONE": "完了", "DUE_TASK": "期限付きタスク", "DUE_TASKS": "期限付きタスク", "FOR_CURRENT": "タスクは期限が来ています。あなたはそれに取り組み始めますか？", "FOR_OTHER": "タスクは期限が来ています。あなたはそれに取り組み始めますか？", "FROM_PROJECT": "プロジェクトから", "FROM_TAG": "タグから：「{{title}}」", "RESCHEDULE_EDIT": "編集（再スケジュール）", "RESCHEDULE_UNTIL_TOMORROW": "明日まで", "SNOOZE": "スヌーズ", "SNOOZE_ALL": "すべてスヌーズ", "START": "開始タスク", "SWITCH_CONTEXT_START": "コンテキストの切り替えと開始", "UNSCHEDULE": "スケジュール解除", "UNSCHEDULE_ALL": "すべてのスケジュールを解除"}, "D_SCHEDULE_TASK": {"MOVE_TO_BACKLOG": "スケジュールされるまでタスクをバックログに移動", "QA_NEXT_MONTH": "来月のスケジュール", "QA_NEXT_WEEK": "来週のスケジュール", "QA_REMOVE_TODAY": "今日のタスクを削除する", "QA_TODAY": "今日のスケジュール", "QA_TOMORROW": "明日のスケジュール", "REMIND_AT": "で思い出させる", "RO_1H": "開始の1時間前", "RO_5M": "開始の5分前", "RO_10M": "開始の10分前", "RO_15M": "開始の15分前", "RO_30M": "開始の30分前", "RO_NEVER": "Never", "RO_START": "それが始まるとき", "SCHEDULE": "スケジュール", "UNSCHEDULE": "予定外"}, "D_TIME": {"ADD_FOR_OTHER_DAY": "消費時間を他の日に追加", "DELETE_FOR": "その日のエントリを削除", "ESTIMATE": "見積もり時間", "TIME_SPENT": "消費時間", "TIME_SPENT_ON": "{{date}} の消費時間", "TITLE": "消費時間/見積もり時間"}, "D_TIME_FOR_DAY": {"ADD_ENTRY_FOR": " {{date}}の新規エントリを追加", "DATE": "新規エントリを追加する日", "HELP": "例：<br> 30m => 30分<br> 2h => 2時間<br> 2h 30m => 2時間30分", "TINE_SPENT": "消費時間", "TITLE": "他の日に追加"}, "N": {"ESTIMATE_EXCEEDED": "時間見積もりを超えました！", "ESTIMATE_EXCEEDED_BODY": "\"{{title}}\"の予想時間を超えました。"}, "S": {"CANNOT_ASSIGN_PROJECT_FOR_REPEATABLE_TASK": "繰り返しタスクの短い構文でプロジェクトを割り当てられない！", "CREATED_FOR_PROJECT": "タスク「{{taskTitle}}」をプロジェクト「{{projectTitle}}」に移動", "CREATED_FOR_PROJECT_ACTION": "プロジェクトに移動", "DELETED": "削除されたタスク \"{{title}}\"", "FOUND_MOVE_FROM_BACKLOG": "既存のタスク <strong>{{title}}</strong> を今日のタスクリストに移動しました", "FOUND_MOVE_FROM_OTHER_LIST": "タスク <strong>{{title}}</strong> を <strong>{{contextTitle}}</strong> から現在のリストに追加しました", "FOUND_RESTORE_FROM_ARCHIVE": "アーカイブからの発行に関連する復元されたタスク <strong>{{title}}</strong> ", "LAST_TAG_DELETION_WARNING": "非プロジェクトタスクの最後のタグを削除しようとしています。これは許可されていません！", "MOVED_TO_ARCHIVE": "アーカイブに {{nr}} タスクを移動", "MOVED_TO_PROJECT": "タスク「{{taskTitle}}」をプロジェクト「{{projectTitle}}」に移動", "MOVED_TO_PROJECT_ACTION": "プロジェクトに移動", "REMINDER_ADDED": "スケジュール済みタスク \"{{title}}\"", "REMINDER_DELETED": "タスクの通知を削除しました", "REMINDER_UPDATED": "タスク \"{{title}}\"の通知を更新しました", "TASK_CREATED": "タスク「{{title}}」を作成しました"}, "SELECT_OR_CREATE": "タスクを選択または作成する", "SUMMARY_TABLE": {"ESTIMATE": "見積もり", "SPENT_TODAY": "今日の消費", "SPENT_TOTAL": "消費時間合計", "TASK": "タスク", "TOGGLE_DONE": "完了にする/未完了に戻す"}}, "TASK_REPEAT": {"ADD_INFO_PANEL": {"CUSTOM": "カスタムリピート設定", "CUSTOM_AND_TIME": "カスタム、 {{timeStr}}", "CUSTOM_WEEKLY": "{{daysStr}}", "CUSTOM_WEEKLY_AND_TIME": "{{daysStr}}, {{timeStr}}", "DAILY": "毎日", "DAILY_AND_TIME": "毎日、 {{timeStr}}。", "EVERY_X_DAILY": " {{x}} 日ごと", "EVERY_X_DAILY_AND_TIME": " {{x}} 日ごとに、 {{timeStr}}", "EVERY_X_MONTHLY": " {{x}} か月ごと", "EVERY_X_MONTHLY_AND_TIME": " {{x}} か月ごとに、 {{timeStr}}", "EVERY_X_YEARLY": " {{x}} 年ごと", "EVERY_X_YEARLY_AND_TIME": " {{x}} 年ごとに、 {{timeStr}}", "MONDAY_TO_FRIDAY": "月～金", "MONDAY_TO_FRIDAY_AND_TIME": "月曜〜金曜、 {{timeStr}}", "MONTHLY_CURRENT_DATE": "毎月 {{dateDayStr}}日に", "MONTHLY_CURRENT_DATE_AND_TIME": "毎月 {{dateDayStr}}日、 {{timeStr}}日", "WEEKLY_CURRENT_WEEKDAY": "毎週 {{weekdayStr}}", "WEEKLY_CURRENT_WEEKDAY_AND_TIME": "毎週 {{weekdayStr}}、 {{timeStr}}", "YEARLY_CURRENT_DATE": " {{dayAndMonthStr}}の年間", "YEARLY_CURRENT_DATE_AND_TIME": "年間 {{dayAndMonthStr}}、 {{timeStr}}"}, "D_CONFIRM_MOVE_TO_PROJECT": {"MSG": "この反復可能なタスク用に {{tasksNr}} 個のインスタンスが作成されています。これらすべてをプロジェクト \"{{projectName}}\" に移動しますか？\"?", "OK": "すべてのインスタンスを更新する"}, "D_CONFIRM_REMOVE": {"MSG": "繰り返し設定を削除すると、このタスクの以前のインスタンスはすべて通常のタスクに変換されます。続行しますか？", "OK": "完全に削除"}, "D_CONFIRM_UPDATE_INSTANCES": {"CANCEL": "将来のタスクのみ", "MSG": "この反復可能なタスクには {{tasksNr}} 個のインスタンスが作成されています。すべてのインスタンスを新しいデフォルトで更新しますか、それとも将来のタスクだけを更新しますか？", "OK": "すべてのインスタンスを更新する"}, "D_EDIT": {"ADD": "繰り返しタスク設定の追加", "ADVANCED_CFG": "高度な設定", "EDIT": "繰り返しタスク設定の編集", "HELP1": "繰り返しタスクは、日々の雑用、たとえば「整理」、「毎日の会議」、「コードレビュー」、「メールの確認」、といった何度も繰り返し発生するタスクに向いています。", "HELP2": "繰り返しタスクを一度設定すると、プロジェクトを開いたときにタスクが以下で選択した曜日に再作成され、1日の終わりに自動的に完了としてマークされます。個々のタスクそれぞれ別のものとして扱われるため、サブタスクなどは自由に追加できます", "HELP3": "JiraまたはGit Issueからインポートしたタスクを繰り返すことはできません。繰り返しタスクでは、リマインダーもすべて削除されます。", "HELP4": "注文フィールドに関する注意：作成注文の繰り返し可能なタスクを参照してください。同時に作成される繰り返し可能なタスクに対してのみ有効です。値が小さいほど、タスクはリストの上位になり、数値が小さいほど、リストの下位になります。 0より大きい値は、通常のタスクの下部にアイテムが作成されることを意味します。", "TAG_LABEL": "追加するタグ"}, "F": {"C_DAY": "日", "C_MONTH": "月", "C_WEEK": "週間", "C_YEAR": "年", "DEFAULT_ESTIMATE": "デフォルトの見積もり", "FRIDAY": "金曜日", "IS_ADD_TO_BOTTOM": "タスクをリストの一番下に移動", "MONDAY": "月曜日", "NOTES": "デフォルト・ノート", "ORDER": "注文", "ORDER_DESCRIPTION": "繰り返し可能なタスクの作成順序。同時に作成された繰り返し可能なタスクにのみ影響します。値が小さいほど、タスクはリストの上位に作成され、数値が小さいほど、リストの下位に作成されます。 0より大きい値は、通常のタスクの下部にアイテムが作成されることを意味します。", "Q_CUSTOM": "カスタム繰り返し設定", "Q_DAILY": "毎日", "Q_MONDAY_TO_FRIDAY": "毎週月曜日から金曜日", "Q_MONTHLY_CURRENT_DATE": "毎月 {{dateDayStr}}日に", "Q_WEEKLY_CURRENT_WEEKDAY": "毎週 {{weekdayStr}}日", "Q_YEARLY_CURRENT_DATE": "毎年 {{dayAndMonthStr}}日に", "QUICK_SETTING": "リピート設定", "REMIND_AT": "で思い出させる", "REMIND_AT_PLACEHOLDER": "いつ思い出させるかを選択します", "REPEAT_CYCLE": "サイクルを繰り返す", "REPEAT_EVERY": "毎回繰り返す", "SATURDAY": "土曜日", "START_DATE": "開始日", "START_TIME": "開始予定時刻", "START_TIME_DESCRIPTION": "例えば。 15:00。終日のタスクは空白のままにします", "SUNDAY": "日曜日", "THURSDAY": "木曜日", "TITLE": "タスクのタイトル", "TUESDAY": "火曜日", "WEDNESDAY": "水曜日"}}, "TASK_VIEW": {"CUSTOMIZER": {"ENTER_PROJECT": "プロジェクトに入る", "ENTER_TAG": "タグを入力", "ESTIMATED_TIME": "推定時間", "FILTER_BY": "フィルター", "FILTER_DEFAULT": "フィルターなし", "FILTER_ESTIMATED_TIME": "推定時間", "FILTER_PROJECT": "プロジェクト", "FILTER_SCHEDULED_DATE": "開催予定日", "FILTER_TAG": "タグ", "FILTER_TIME_SPENT": "滞在時間", "GROUP_BY": "グループ化", "GROUP_DEFAULT": "グループなし", "GROUP_PROJECT": "プロジェクト", "GROUP_SCHEDULED_DATE": "開催予定日", "GROUP_TAG": "タグ", "RESET_ALL": "すべてリセット", "SCHEDULED_DEFAULT": "任意の日付", "SCHEDULED_NEXT_MONTH": "来月", "SCHEDULED_NEXT_WEEK": "来週", "SCHEDULED_THIS_MONTH": "今月", "SCHEDULED_THIS_WEEK": "今週", "SCHEDULED_TODAY": "今日", "SCHEDULED_TOMORROW": "明日", "SORT_BY": "並べ替え", "SORT_CREATION_DATE": "作成日", "SORT_DEFAULT": "デフォルト", "SORT_NAME": "名前", "SORT_SCHEDULED_DATE": "開催予定日", "TIME_1HOUR": "> 1時間", "TIME_2HOUR": "> 2時間", "TIME_10MIN": "> 10分間", "TIME_30MIN": ">30分", "TIME_DEFAULT": "任意の期間", "TIME_SPENT": "滞在時間", "TITLE": "タスクビューのカスタマイズ"}}, "TIME_TRACKING": {"B": {"ALREADY_DID": "私はすでにやった", "SNOOZE": "スヌーズ {{time}}"}, "B_TTR": {"ADD_TO_TASK": "タスクに追加", "MSG": "タイムトラッキングしていない時間が{{time}}あります"}, "D_IDLE": {"ADD_ENTRY": "トラッキングのエントリーを追加", "BREAK": "休憩", "CREATE_AND_TRACK": "<em>作成</em>して追加", "IDLE_FOR": "アイドル状態になっている時間：", "RESET_BREAK_REMINDER_TIMER": "休憩リマインダー・タイマーのリセット", "SIMPLE_CONFIRM_COUNTER_CANCEL": "スキップ", "SIMPLE_CONFIRM_COUNTER_OK": "トラック", "SIMPLE_COUNTER_CONFIRM_TXT": "スキップを選択しましたが、 {{nr}} のシンプルなカウンターボタンをアクティブにしました。あなたは彼らへのアイドル時間を追跡したいですか？", "SIMPLE_COUNTER_TOOLTIP": "クリックして {{title}}まで追跡", "SIMPLE_COUNTER_TOOLTIP_DISABLE": "クリックして {{title}}を追跡しない", "SKIP": "スキップ", "SPLIT_TIME": "時間を複数のタスクと休憩に分ける", "TASK": "タスク", "TRACK_TO": "以下に追加する："}, "D_TRACKING_REMINDER": {"CREATE_AND_TRACK": "<em>作成</em>して追加", "IDLE_FOR": "アイドル状態になっている時間：", "NOTIFICATION_TITLE": "あなたの時間を追跡してください!", "TASK": "タスク", "TRACK_TO": "以下に追加する：", "UNTRACKED_TIME": "トラッキングしていない時間："}}, "WORKLOG": {"CMP": {"DAYS_WORKED": "稼動日数：", "MONTH_WORKED": "稼動月数：", "REPEATING_TASK": "繰り返しタスク", "RESTORE_TASK_FROM_ARCHIVE": "アーカイブからタスクを復元", "TASKS": "タスク", "TOTAL_TIME": "合計時間：", "WEEK_NR": "{{nr}}週目", "WORKED": "消費時間"}, "D_CONFIRM_RESTORE": "タスク <strong>\"{{title}}\"</strong> を今日のタスクリストに移動しますか？", "D_EXPORT_TITLE": "作業ログのエクスポート {{start}} - {{end}}", "D_EXPORT_TITLE_SINGLE": "作業ログのエクスポート {{day}}", "EXPORT": {"ADD_COL": "列を追加", "COPY_TO_CLIPBOARD": "クリップボードにコピー", "DONT_ROUND": "丸めない", "EDIT_COL": "列を編集", "GROUP_BY": "グループ化", "O": {"DATE": "日付", "ENDED_WORKING": "終了時刻", "ESTIMATE_AS_CLOCK": "見積もり時間（例：5:23）", "ESTIMATE_AS_MILLISECONDS": "ミリ秒単位の見積もり時間", "ESTIMATE_AS_STRING": "見積もり時間（例：5h23m）", "FULL_HALF_HOURS": "30分", "FULL_HOURS": "1時間", "FULL_QUARTERS": "15分", "NOTES": "タスクの説明", "PARENT_TASK": "親タスク", "PARENT_TASK_TITLES_ONLY": "親タスクタイトルのみ", "PROJECTS": "プロジェクト名", "STARTED_WORKING": "開始時刻", "TAGS": "タグ", "TASK_SUBTASK": "タスク/サブタスク", "TIME_AS_CLOCK": "作業時間（例：5:23）", "TIME_AS_MILLISECONDS": "ミリ秒単位の作業時間", "TIME_AS_STRING": "作業時間（例：5h23m）", "TITLES_AND_SUB_TASK_TITLES": "タスク/サブタスクのタイトル", "WORKLOG": "作業記録"}, "OPTIONS": "オプション", "ROUND_END_TIME_TO": "終了時刻を丸める", "ROUND_START_TIME_TO": "開始時刻を丸める", "ROUND_TIME_WORKED_TO": "作業時間を丸める", "SAVE_TO_FILE": "ファイルに保存", "SEPARATE_TASKS_BY": "タスクを分割する", "SHOW_AS_TEXT": "テキストで表示"}, "WEEK": {"EXPORT": "週データのエクスポート", "NO_DATA": "今週はまだタスクがありません。", "TITLE": "タイトル"}}}, "FILE_IMEX": {"DIALOG_CONFIRM_URL_IMPORT": {"INITIATED_MSG": "自動データインポートが開始されました。", "SOURCE_URL_DOMAIN": "ソースドメイン", "TITLE": "URLからのデータインポートを確認", "WARNING_MSG": "続行すると、指定されたURLの内容で現在のアプリケーションデータと設定が上書きされます。この操作は元に戻せません。", "WARNING_TITLE": "警告"}, "EXPORT_DATA": "データのエクスポート", "IMPORT_FROM_FILE": "ファイルからインポート", "IMPORT_FROM_URL": "URLからインポート", "IMPORT_FROM_URL_DIALOG_DESCRIPTION": "インポートしたいSuper ProductivityのバックアップJSONファイルの完全なURLを入力してください。", "IMPORT_FROM_URL_DIALOG_TITLE": "URLからインポート", "OPEN_IMPORT_FROM_URL_DIALOG": "URLからインポート", "PRIVACY_EXPORT": "匿名化されたデータをエクスポートする（デバッグ用に <EMAIL> に送信する）。", "S_BACKUP_DOWNLOADED": "バックアップをAndroidドキュメントフォルダにダウンロードしました", "S_ERR_IMPORT_FAILED": "データのインポートに失敗しました", "S_ERR_INVALID_DATA": "インポートに失敗しました：無効なJSON", "S_ERR_INVALID_URL": "インポートに失敗しました：無効なURLが指定されました", "S_ERR_NETWORK": "インポートに失敗しました：URLからデータを取得中にネットワークエラーが発生しました", "S_IMPORT_FROM_URL_ERR_DECODE": "エラー：インポート用のURLパラメータをデコードできませんでした。正しくフォーマットされていることを確認してください。", "URL_PLACEHOLDER": "インポートするURLを入力してください"}, "G": {"ADD": "追加", "ADVANCED_CFG": "詳細設定", "CANCEL": "キャンセル", "CLOSE": "閉じる", "CONFIRM": "確認する", "DELETE": "削除", "DISMISS": "やめる", "DO_IT": "やれ！", "DURATION_DESCRIPTION": "例：\"5h 23m \"で5時間23分", "EDIT": "編集", "ENABLED": "有効", "EXAMPLE_VAL": "クリックして編集する", "EXTENSION_INFO": "Jira ApiおよびIdle Time Handlingとの通信を可能にするために、<a target=\"_blank\" href=\"https://chrome.google.com/webstore/detail/super-productivity/ljkbjodfmekklcoibdnhahlaalhihmlb\">Chrome拡張機能をダウンロード</a>してください。これはモバイルでは機能しないことに注意してください。", "HIDE": "隠す", "ICON_INP_DESCRIPTION": "すべてのutf-8の絵文字もサポートしています！", "INBOX_PROJECT_TITLE": "受信トレイ", "LOGIN": "ログイン", "LOGOUT": "ログアウト", "MINUTES": "{{m}} 分", "MOVE_BACKWARD": "後方に移動", "MOVE_FORWARD": "前進", "NEXT": "次", "NO_CON": "現在オフラインです。インターネットに再接続してください。", "NONE": "無し", "OK": "OK", "OVERDUE": "遅れた", "PREVIOUS": "前", "REMOVE": "削除する", "RESET": "リセット", "SAVE": "保存する", "SUBMIT": "投稿する", "TITLE": "タイトル", "TODAY_TAG_TITLE": "今日", "TRACKING_INTERVAL_DESCRIPTION": "この間隔をミリ秒単位で時間を追跡する。ディスクへの書き込みを減らすためにこれを変更したくなるかもしれません。issue #2355 を参照。", "UNDO": "元に戻す", "UPDATE": "更新", "WITHOUT_PROJECT": "プロジェクトなし", "YESTERDAY": "昨日"}, "GCF": {"AUTO_BACKUPS": {"HELP": "問題が発生した場合に備えて、すべてのデータをアプリフォルダに自動保存して準備します。", "LABEL_IS_ENABLED": "自動バックアップを有効にする", "LOCATION_INFO": "バックアップは次の場所に保存されます：", "TITLE": "自動バックアップ"}, "CALENDARS": {"BROWSER_WARNING": "クロスオリジン制限 <b>のため、これは Super Productivity のブラウザ バージョンでは動作しない可能性があります。<br /> この機能を使用するには、 <a href=\"https://super-productivity.com/download/\">デスクトップ バージョン</a> をダウンロードしてください。</b>", "CAL_PATH": "ICalソースのURL", "CAL_PROVIDERS": "カレンダー・プロバイダー（実験的、オプション）", "CHECK_UPDATES": "リモートのアップデートを毎X回チェックする", "DEFAULT_PROJECT": "追加されたカレンダータスクのデフォルトプロジェクト", "HELP": "カレンダーを統合してリマインドしたり、Super Productivity内でタスクとして追加することができます。統合はiCal形式を使用して動作します。この機能を使用するには、カレンダーにインターネット経由またはファイルシステム経由でアクセスできる必要があります。", "SHOW_BANNER_THRESHOLD": "イベント前に通知Xを表示（無効は空白）"}, "EVALUATION": {"IS_HIDE_EVALUATION_SHEET": "日次サマリーの評価シートを非表示", "TITLE": "評価と指標"}, "FOCUS_MODE": {"HELP": "フォーカスモードは、気が散らない画面を開き、現在のタスクに集中できるようにします。", "L_ALWAYS_OPEN_FOCUS_MODE": "トラッキング時は常にオープンフォーカスモード", "L_SKIP_PREPARATION_SCREEN": "準備画面をスキップ（ストレッチなど）", "TITLE": "フォーカスモード"}, "IDLE": {"HELP": "<div><p>アイドル時間処理が有効になっていると、アイドル時間が経過したときに、どのタスクで時間を追跡するかを確認するために、指定した時間が経過した後にダイアログが開きます。</p></div>", "IS_ENABLE_IDLE_TIME_TRACKING": "アイドル時間処理を有効にする", "IS_ONLY_OPEN_IDLE_WHEN_CURRENT_TASK": "現在のタスクが選択されている場合にのみアイドル時間ダイアログを起動する", "MIN_IDLE_TIME": "アイドル状態になるまでの時間", "TITLE": "アイドル処理"}, "IMEX": {"HELP": "<p>ここでは、すべてのデータをバックアップ用の <strong>JSON</strong> としてエクスポートできますが、それを別のコンテキストで使用することもできます（たとえば、プロジェクトをブラウザにエクスポートしてデスクトップバージョンにインポートできます） </p> <p>インポートは、有効なJSONがテキスト領域にコピーされることを期待しています。 <strong>注：インポートボタンを押すと、現在の設定とデータがすべて上書きされます。</strong></p>", "TITLE": "インポート/エクスポート"}, "KEYBOARD": {"ADD_NEW_NOTE": "新しいメモを追加", "ADD_NEW_TASK": "新しいタスクを追加する", "APP_WIDE_SHORTCUTS": "グローバルショートカット（アプリケーション全体）", "COLLAPSE_SUB_TASKS": "サブタスクを折りたたむ", "EXPAND_SUB_TASKS": "サブタスクを展開", "GLOBAL_ADD_NOTE": "新しいメモを追加", "GLOBAL_ADD_TASK": "新しいタスクを追加する", "GLOBAL_SHOW_HIDE": "Super Productivityの表示/非表示", "GLOBAL_TOGGLE_TASK_START": "最後のアクティブタスクのタイムトラッキングを切り替えます", "GO_TO_DAILY_AGENDA": "アジェンダに移動", "GO_TO_FOCUS_MODE": "フォーカスモードに移動", "GO_TO_SCHEDULE": "タイムラインに移動", "GO_TO_SCHEDULED_VIEW": "スケジュールされたタスクに移動", "GO_TO_SETTINGS": "設定に移動", "GO_TO_WORK_VIEW": "作業ビューに移動", "HELP": "<p>ここですべてのキーボードショートカットを設定できます。</p> <p>テキスト入力をクリックして、希望のキーボードの組み合わせを入力してください。保存するにはEnterキーを押し、中止するにはEscapeキーを押してください。</p> <p>3種類のショートカットがあります。</p> <ul> <li> <strong>グローバルショートカット：</strong> アプリが実行されているとき、他のすべてのアプリケーションからアクションが起動されます。 </li> <li> <strong>アプリケーションレベルのショートカット：</strong> アプリケーションのすべての画面から起動しますが、現在テキストフィールドを編集している場合はそうではありません。 </li> <li> <strong>タスクレベルのショートカット：</strong> マウスまたはキーボードでタスクを選択した場合にのみ起動され、通常そのタスクに関連したアクションを起動します。 </li> </ul>", "MOVE_TASK_DOWN": "リスト内のタスクを下に移動", "MOVE_TASK_TO_BOTTOM": "タスクをリストの一番下に移動する", "MOVE_TASK_TO_TOP": "タスクをリストのトップに移動する", "MOVE_TASK_UP": "リスト内のタスクを上に移動", "MOVE_TO_BACKLOG": "タスクをタスクバックログに移動", "MOVE_TO_REGULARS_TASKS": "今日のタスクリストにタスクを移動する", "OPEN_PROJECT_NOTES": "プロジェクトノートの表示/非表示", "SAVE_NOTE": "メモを保存", "SELECT_NEXT_TASK": "次のタスクを選択", "SELECT_PREVIOUS_TASK": "前のタスクを選択", "SHOW_SEARCH_BAR": "検索バーを表示する", "SYSTEM_SHORTCUTS": "グローバルショートカット（システム全体）", "TASK_ADD_ATTACHMENT": "ファイルまたはリンクを添付", "TASK_ADD_SUB_TASK": "サブタスクを追加", "TASK_DELETE": "タスクを削除", "TASK_EDIT_TAGS": "タグを編集", "TASK_EDIT_TITLE": "タイトルを編集", "TASK_MOVE_TO_PROJECT": "プロジェクトメニューへの移動タスクを開く", "TASK_OPEN_CONTEXT_MENU": "タスクのコンテキストメニューを開く", "TASK_OPEN_ESTIMATION_DIALOG": "見積もり時間/消費時間を編集", "TASK_PLAN_FORDAY": "当日のプラン", "TASK_SCHEDULE": "スケジュールタスク", "TASK_SHORTCUTS": "タスク", "TASK_SHORTCUTS_INFO": "次のショートカットは、現在選択されているタスク（タブまたはマウスで選択）に適用されます。", "TASK_TOGGLE_DETAIL_PANEL_OPEN": "追加の仕事情報を表示/隠す", "TASK_TOGGLE_DONE": "完了を切り替える", "TITLE": "キーボードショートカット", "TOGGLE_BACKLOG": "タスクバックログの表示/非表示", "TOGGLE_BOOKMARKS": "ブックマークバーの表示/非表示", "TOGGLE_ISSUE_PANEL": "課題パネルの表示／非表示", "TOGGLE_PLAY": "タスクの開始/停止", "TOGGLE_SIDE_NAV": "Sidenavの表示とフォーカス/非表示", "TOGGLE_TASK_VIEW_CUSTOMIZER_PANEL": "トグルフィルター/グループ/ソートパネル", "TRIGGER_SYNC": "トリガー同期（設定されている場合）", "ZOOM_DEFAULT": "ズームのデフォルト（デスクトップのみ）", "ZOOM_IN": "ズームイン（デスクトップのみ）", "ZOOM_OUT": "ズームアウト（デスクトップのみ）"}, "LANG": {"AR": "عرب<PERSON>", "CZ": "チェコ", "DE": "ドイツ語", "EN": "英語", "ES": "スペイン語", "FA": "فار<PERSON>ی", "FR": "フランセ", "HR": "アルバツキー", "ID": "インドネシア語", "IT": "イタリア語", "JA": "日本語", "KO": "한국어", "LABEL": "言語を選択してください", "NB": "ノルウェー語", "NL": "オランダ語", "PL": "ポーランド語", "PT": "ポルトガル語", "RU": "ロシア語", "SK": "スロバキア語", "TITLE": "言語", "TR": "トルコ語", "UK": "Українська", "ZH": "中文(简体)", "ZH_TW": "中文(繁體)"}, "MISC": {"DEFAULT_PROJECT": "何も指定されていない場合にタスクに使用するデフォルトのプロジェクト", "FIRST_DAY_OF_WEEK": "週の最初の日", "HELP": "<p><strong>デスクトップ通知が表示されませんか？</strong> Windowsの場合は、[システム]> [通知とアクション]を確認し、必要な通知が有効になっているかどうかを確認します。</p>", "IS_AUTO_ADD_WORKED_ON_TO_TODAY": "タスクに取り組むために今日タグを自動的に追加する", "IS_AUTO_MARK_PARENT_AS_DONE": "すべてのサブタスクが完了したら、親タスクを完了としてマークする", "IS_CONFIRM_BEFORE_EXIT": "アプリを終了する前に確認する", "IS_CONFIRM_BEFORE_EXIT_WITHOUT_FINISH_DAY": "最初に1日を終えずにアプリを終了する前に確認してください", "IS_DARK_MODE": "ダークモード", "IS_DISABLE_ANIMATIONS": "すべてのアニメーションを無効にする", "IS_HIDE_NAV": "メインヘッダーが表示されるまでナビゲーションを非表示にする（デスクトップのみ）", "IS_MINIMIZE_TO_TRAY": "トレイに最小化（デスクトップのみ）", "IS_SHOW_TIP_LONGER": "アプリの起動時に生産性のヒントを少し長く表示する", "IS_TRAY_SHOW_CURRENT_COUNTDOWN": "トレイ/ステータスメニューに現在のカウントダウンを表示する(デスクトップMacのみ)", "IS_TRAY_SHOW_CURRENT_TASK": "トレイ/ステータスメニューに現在のタスクを表示する（デスクトップのみ）", "IS_TURN_OFF_MARKDOWN": "ノートのマークダウン解析をオフにする", "IS_USE_MINIMAL_SIDE_NAV": "最小限のナビゲーションバーを使用する（アイコンのみを表示する）", "START_OF_NEXT_DAY": "翌日の開始時間", "START_OF_NEXT_DAY_HINT": "いつ（時間単位）から翌日が始まったかをカウントする。デフォルトは0時で、0となる。", "TASK_NOTES_TPL": "タスク記述テンプレート", "TITLE": "その他の設定"}, "POMODORO": {"BREAK_DURATION": "短い休憩時間", "CYCLES_BEFORE_LONGER_BREAK": "長い休憩までの作業セッションの回数", "DURATION": "作業セッションの時間", "HELP": "<p>ポモドーロタイマーは各作業セッションの時間、短い休憩時間、長い休憩までの作業セッションの回数、長い休憩時間など複数の項目で設定できます。</p> <p>ポモドーロが中断している間に気を散らすものを表示するかどうかも設定できます。</p> <p>「ポモドーロ休憩時間追跡の一時停止」を設定すると、作業時間がタスクに費やされたときに休憩時間も追跡されます。 </p> <p>「アクティブなタスクがないときにポモドーロセッションを一時停止」を有効にすると、タスクを一時停止したときにポモドーロセッションも一時停止します。</p>", "IS_ENABLED": "ポモドーロタイマーを有効にする", "IS_MANUAL_CONTINUE": "次のポモドーロセッションを手動で開始する", "IS_MANUAL_CONTINUE_BREAK": "次の休憩の開始を手動で確認", "IS_PLAY_SOUND": "セッション終了時に音を鳴らす", "IS_PLAY_SOUND_AFTER_BREAK": "休憩終了時に音を鳴らす", "IS_PLAY_TICK": "1秒ごとにチクタク音を鳴らす", "IS_STOP_TRACKING_ON_BREAK": "休憩中のタスクのタイムトラッキングを停止する", "LONGER_BREAK_DURATION": "長い休憩時間", "TITLE": "ポモドーロタイマー"}, "REMINDER": {"COUNTDOWN_DURATION": "リマインダーの前にバナーXを表示", "IS_COUNTDOWN_BANNER_ENABLED": "リマインダーの期限前にカウントダウンバナーを表示する", "TITLE": "注意事項"}, "SCHEDULE": {"HELP": "タイムライン機能は、計画されたタスクが時間の経過とともにどのように実行されるかについての簡単な概要を提供する必要があります。左側のメニューの <a href='#/schedule'>'タイムライン'</a>の下にあります。", "L_IS_LUNCH_BREAK_ENABLED": "昼食休憩の有効化", "L_IS_WORK_START_END_ENABLED": "予定外のタスクフローを特定の作業時間に制限する", "L_LUNCH_BREAK_END": "昼食休憩終了", "L_LUNCH_BREAK_START": "昼休み開始", "L_WORK_END": "就業日の終わり", "L_WORK_START": "就業日開始", "LUNCH_BREAK_START_END_DESCRIPTION": "例 13:00", "TITLE": "タイムライン", "WORK_START_END_DESCRIPTION": "例えば17:00"}, "SHORT_SYNTAX": {"HELP": "<p>ここでタスク作成時の短い構文オプションを制御できます</p>。", "IS_ENABLE_DUE": "短い構文による有効化 (@<Due time>)", "IS_ENABLE_PROJECT": "プロジェクト短縮構文を有効にする (+<Project name>)", "IS_ENABLE_TAG": "タグの短縮構文を有効にする (#<Tag>)", "TITLE": "短い構文"}, "SOUND": {"BREAK_REMINDER_SOUND": "休憩リマインダー音", "DONE_SOUND": "タスク完了音", "IS_INCREASE_DONE_PITCH": "実行するすべてのタスクのピッチを上げる", "TITLE": "音", "TRACK_TIME_SOUND": "時間リマインダー音を追跡", "VOLUME": "ボリューム"}, "TAKE_A_BREAK": {"ADD_NEW_IMG": "モチベーションを高める画像を追加", "FULL_SCREEN_BLOCKER_DURATION": "フルスクリーンウィンドウの表示時間（デスクトップのみ）", "HELP": "<div> <p>指定した時間休憩せずに作業したときに、繰り返し発生するリマインダーを設定できます。</p> <p>表示するメッセージは変更できます。 ${duration} は休憩せずに作業した時間に置き換えられます。</p> </div>", "IS_ENABLED": "休憩リマインダーを有効にする", "IS_FOCUS_WINDOW": "リマインダーがアクティブなときにアプリウィンドウをフォーカスする（デスクトップのみ）", "IS_FULL_SCREEN_BLOCKER": "フルスクリーンウィンドウにメッセージを表示する（デスクトップのみ）", "IS_LOCK_SCREEN": "休憩時間が来たら画面をロックする（デスクトップのみ）", "MESSAGE": "休憩メッセージを受け取る", "MIN_WORKING_TIME": "休憩の通知を受け取るまでの時間", "MOTIVATIONAL_IMGS": "やる気を起こさせる画像（ウェブのURL）", "NOTIFICATION_TITLE": "休憩する！", "SNOOZE_TIME": "休憩をとるように促されたときに時間をスヌーズする", "TITLE": "休憩リマインダー"}, "TIME_TRACKING": {"HELP": "タイムトラッキングリマインダーは、タイムトラッキングの開始を忘れた場合に表示されるバナーです。", "L_DEFAULT_ESTIMATE": "新規タスクのデフォルト見積時間", "L_DEFAULT_ESTIMATE_SUB_TASKS": "新規サブタスクのデフォルト見積時間", "L_IS_AUTO_START_NEXT_TASK": "現在のタスクに完了マークを付けると、次のタスクのトラッキングを開始", "L_IS_NOTIFY_WHEN_TIME_ESTIMATE_EXCEEDED": "見積もり時間を超過した場合に通知", "L_IS_TRACKING_REMINDER_ENABLED": "トラッキング・リマインダー有効", "L_IS_TRACKING_REMINDER_FOCUS_WINDOW": "リマインダーがアクティブなときにフォーカス アプリ ウィンドウ (デスクトップのみ)", "L_IS_TRACKING_REMINDER_NOTIFY": "タイムトラッキングリマインダーが表示されたときに通知", "L_IS_TRACKING_REMINDER_SHOW_ON_MOBILE": "モバイルアプリにトラッキングリマインダーを表示", "L_TRACKING_INTERVAL": "時間追跡間隔（実験的）", "L_TRACKING_REMINDER_MIN_TIME": "追跡リマインダーを表示するまでの待ち時間 バナー", "TITLE": "タイムトラッキング"}}, "GLOBAL_RELATIVE_TIME": {"FUTURE": {"A_DAY": "一日で", "A_MINUTE": "1分で", "A_MONTH": "一ヶ月で", "A_YEAR": "1年で", "AN_HOUR": "1時間で", "DAYS": " {{count}} 日で", "FEW_SECONDS": "数秒で", "HOURS": " {{count}} 時間で", "MINUTES": " {{count}} 分で完了", "MONTHS": " {{count}} か月で", "YEARS": " {{count}} 年後"}, "PAST": {"A_DAY": "一日前", "A_MINUTE": "1分前", "A_MONTH": "一ヶ月前", "A_YEAR": "一年前", "AN_HOUR": "一時間前", "DAYS": "{{count}} 日前", "FEW_SECONDS": "数秒前", "HOURS": "{{count}} 時間前", "MINUTES": "{{count}} 分前", "MONTHS": "{{count}} ヶ月前", "YEARS": "{{count}} 年前"}}, "GLOBAL_SNACK": {"COPY_TO_CLIPPBOARD": "クリップボードにコピー", "ERR_COMPRESSION": "圧縮インターフェースのエラー", "FILE_DOWNLOADED": "{{fileName}} ダウンロード", "FILE_DOWNLOADED_BTN": "開いたフォルダ", "NAVIGATE_TO_TASK_ERR": "タスクに集中できませんでした。削除しましたか？", "PERSISTENCE_DISALLOWED": "データは永続的に保持されません。これはデータの損失につながる可能性があることに注意してください!!", "PERSISTENCE_ERROR": "データの永続化をリクエストする際のエラー： {{err}}", "RUNNING_X": "\"{{str}}\"を実行しています。", "SHORTCUT_WARN_OPEN_BOOKMARKS_FROM_TAG": "{{keyCombo}} が押されましたが、ブックマークを開くショートカットはプロジェクトコンテキストでのみ使用できます。"}, "GPB": {"ASSETS": "アセットを読み込んでいます...", "DBX_DOWNLOAD": "Dropbox：ファイルをダウンロード...", "DBX_GEN_TOKEN": "Dropbox：トークンを生成...", "DBX_META": "Dropbox：ファイルメタを取得...", "DBX_UPLOAD": "Dropbox：ファイルをアップロード...", "GITHUB_LOAD_ISSUE": "GitHub：課題データをロード...", "JIRA_LOAD_ISSUE": "Jira：課題データをロード...", "SYNC": "データの同期...", "UNKNOWN": "リモートデータの読み込み", "WEB_DAV_DOWNLOAD": "WebDAV：データのダウンロード中...", "WEB_DAV_UPLOAD": "WebDAV：データのアップロード中..."}, "MH": {"ADD_NEW_TASK": "新しいタスクを追加する", "ALL_PLANNED_LIST": "リピート／予定", "BOARDS": "ポート", "CREATE_PROJECT": "プロジェクトを作成", "CREATE_TAG": "タグを作成", "DELETE_PROJECT": "プロジェクトの削除", "DELETE_TAG": "タグを削除", "ENTER_FOCUS_MODE": "フォーカスモードに入る", "GO_TO_TASK_LIST": "タスクリストへ", "HELP": "ヘルプ", "HM": {"CALENDARS": "ハウツーカレンダーの接続", "CONTRIBUTE": "貢献する", "GET_HELP_ONLINE": "オンラインヘルプ", "KEYBOARD": "ハウツー高度なキーボード", "REDDIT_COMMUNITY": "Reddit コミュニティ", "REPORT_A_PROBLEM": "問題を報告する", "START_WELCOME": "ウェルカムツアー開始", "SYNC": "ハウツー同期の設定"}, "METRICS": "メトリック", "NO_PROJECT_INFO": "利用可能なプロジェクトはありません。「プロジェクトを作成」ボタンをクリックすると、新しいプロジェクトを作成できます。", "NO_TAG_INFO": "現在タグはありません。タスクの追加や編集の際に`#yourTagName`と入力することでタグを追加することができます。", "NOTES": "ノート", "NOTES_PANEL_INFO": "メモはスケジュールと通常のタスクリストビューからのみ表示できます。", "PLANNER": "プランナー", "PROCRASTINATE": "先延ばし", "PROJECT_MENU": "プロジェクトメニュー", "PROJECT_SETTINGS": "プロジェクト設定", "PROJECTS": "プロジェクト", "QUICK_HISTORY": "クイックヒストリー", "SCHEDULE": "スケジュール", "SEARCH": "捜索", "SETTINGS": "設定", "SHOW_SEARCH_BAR": "検索バーを表示する", "TAGS": "タグ", "TASK_LIST": "タスクリスト", "TASKS": "タスク", "TOGGLE_SHOW_BOOKMARKS": "ブックマークの表示/非表示", "TOGGLE_SHOW_ISSUE_PANEL": "問題パネルの表示/非表示", "TOGGLE_SHOW_NOTES": "プロジェクトノートの表示/非表示", "TOGGLE_TRACK_TIME": "追跡時間の開始/停止", "TRIGGER_SYNC": "手動で同期をトリガーする", "WORKLOG": "作業記録"}, "MIGRATE": {"C_DOWNLOAD_BACKUP": "レガシーデータのバックアップをダウンロードしますか(古いバージョンのSuper Productivityで使用できます)?", "DETECTED_LEGACY": "レガシーデータを検出しました。私たちはあなたのためにそれを移行します!", "E_MIGRATION_FAILED": "移行に失敗しました。エラーあり:", "E_RESTART_FAILED": "自動再起動に失敗しました。アプリを手動で再起動してください。", "SUCCESS": "移行はすべて完了しました!今すぐアプリを再起動しています..."}, "PDS": {"ADD_TASKS_FROM_TODAY": "今日のタスクを追加", "BACK": "待って！何か忘れてる！", "BREAK_LABEL": "休憩（回数/時間）", "CELEBRATE": " <i>お祝いしましょう！</i>", "CLEAR_ALL_CONTINUE": "完了したすべてをクリアして続行", "D_CONFIRM_APP_CLOSE": {"CANCEL": "いいえ、タスクをクリアするだけです", "MSG": "あなたの仕事は終わりました。家に帰る時間！", "OK": "アイアイ！シャットダウンします！"}, "ESTIMATE_TOTAL": "合計見積もり：", "EVALUATE_DAY": "評価", "EXPORT_TASK_LIST": "タスクリストのエクスポート", "NO_TASKS": "この日は何もしていません", "PLAN_TOMORROW": "プラン", "REVIEW_TASKS": "レビュー", "ROUND_5M": "すべてのタスクを5分に丸める", "ROUND_15M": "すべてのタスクを15分に丸める", "ROUND_30M": "すべてのタスクを30分に丸める", "ROUND_TIME_SPENT": "時間を丸める", "ROUND_TIME_SPENT_TITLE": "すべてのタスクにかかったラウンド時間。注意してください！これを元に戻すことはできません。", "ROUND_TIME_WARNING": "!!!元に戻せないので注意!!!", "ROUND_UP_5M": "すべてのタスクを5分に切り上げ", "ROUND_UP_15M": "すべてのタスクを15分に切り上げ", "ROUND_UP_30M": "すべてのタスクを30分に切り上げ", "SAVE_AND_GO_HOME": "保存して家に帰る", "SAVE_AND_GO_HOME_TOOLTIP": "完了したすべてのタスクをアーカイブ（ワークログ）に移動し、オプションですべてのデータを同期してアプリを終了します。", "START_END": "開始 - 終了", "SUMMARY_FOR": " {{dayStr}}の日別サマリー", "TASKS_COMPLETED": "完了したタスク", "TIME_SPENT_AND_ESTIMATE_LABEL": "消費時間/推定時間", "TIME_SPENT_ESTIMATE_TITLE": "消費時間：今日消費した合計時間。アーカイブされたタスクは含まれていません。 - 推定時間：今日作業したタスクの見積もり時間から、他の日にすでに消費した時間を差し引いたもの。", "TIME_SPENT_TODAY_BY_TAG": "タグ別今日使った時間", "WEEK": "週間"}, "PM": {"TITLE": "プロジェクトメトリクス"}, "PS": {"GLOBAL_SETTINGS": "全体設定", "ISSUE_INTEGRATION": "課題の連携", "PRIVACY_POLICY": "プライベートポリシー", "PRODUCTIVITY_HELPER": "生産性ヘルパー", "PROJECT_SETTINGS": "プロジェクト固有の設定", "PROVIDE_FEEDBACK": "フィードバックを提供します", "SYNC_EXPORT": "同期とエクスポート", "TAG_SETTINGS": "タグ固有の設定", "TOGGLE_DARK_MODE": "ダークモードの切り替え"}, "SCHEDULE": {"NO_REPEATABLE_TASKS": "現在、繰り返しタスクはありません。タスクのサイドパネルで \"リピートタスク \"を選択すると、タスクをスケジュールすることができます。タスクをクリックすると表示される一番右のアイコンをクリックしてください。", "NO_SCHEDULED": "現在スケジュールされているタスクはありません。タスクのコンテキストメニューで[タスクのスケジュール]を選択してタスクをスケジュールできます。開くには、タスクの右側にある3つの小さな点をクリックします。", "NO_SCHEDULED_TITLE": "その日の予定タスク", "REPEATED_TASKS": "繰り返されるタスク", "SCHEDULED_TASKS": "スケジュールされたタスク", "SCHEDULED_TASKS_WITH_TIME": "リマインダー付きスケジュールタスク", "START_TASK": "今すぐタスクを開始してリマインダーを削除"}, "THEMES": {"amber": "アンバー", "blue": "青", "blue-grey": "ブルーグレー", "cyan": "シアン", "deep-orange": "濃いオレンジ", "deep-purple": "濃い紫", "green": "緑", "indigo": "インディゴ", "light-blue": "ライトブルー", "light-green": "薄緑", "lime": "ライム", "pink": "ピンク", "purple": "紫", "SELECT_THEME": "テーマを選択", "teal": "ティール", "yellow": "黄"}, "V": {"E_1TO10": "1から10までの値を入力してください", "E_DATETIME": "入力された値は日時ではありません", "E_DURATION": "有効な期間を入力してください(例:1時間)", "E_MAX": " {{val}}より大きくてはいけません", "E_MAX_LENGTH": "最大 {{val}} 文字にする必要があります", "E_MIN": " {{val}}より小さくなければなりません", "E_MIN_LENGTH": "少なくとも {{val}} 文字にする必要があります", "E_PATTERN": "無効入力", "E_REQUIRED": "この項目は必須です"}, "WW": {"ADD_MORE": "さらに追加", "ADD_SCHEDULED_FOR_TOMORROW": "明日予定されているタスクを追加します（{{nr}}）", "ADD_SOME_TASKS": "一日を計画するためタスクをいくつか追加しましょう！", "DONE_TASKS": "完了したタスク", "DONE_TASKS_IN_ARCHIVE": "現在、ここには完了したタスクはありませんが、すでにアーカイブされているタスクがいくつかあります。", "ESTIMATE_REMAINING": "推定残り：", "FINISH_DAY": "一日を終える", "FINISH_DAY_FOR_PROJECT": "プロジェクト終了日", "FINISH_DAY_FOR_TAG": "このタグの終了日", "FINISH_DAY_TOOLTIP": "一日を評価し、完了したタスクをアーカイブに移動し（オプション）、次の日の計画を立てる。", "HELP_PROCRASTINATION": "先延ばししてるので助けて！", "MOVE_DONE_TO_ARCHIVE": "アーカイブへの移動が完了しました", "NO_DONE_TASKS": "現在、完了したタスクはありません", "NO_PLANNED_TASK_ALL_DONE": "完了したすべてのタスク", "NO_PLANNED_TASKS": "計画タスクなし", "READY_TO_WORK": "準備オーケー！", "RESET_BREAK_TIMER": "休憩タイマーなしでリセット", "TIME_ESTIMATED": "推定所要時間：", "TODAY_REMAINING": "今日も残っている：", "WITHOUT_BREAK": "連続：", "WORKING_TODAY": "今日：", "WORKING_TODAY_ARCHIVED": "アーカイブされたタスクの今日の作業時間"}}