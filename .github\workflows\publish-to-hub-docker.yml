# inspired by: https://docs.github.com/en/actions/guides/publishing-docker-images
name: Publish Docker image To hub.docker

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs: {}

jobs:
  push_to_registry:
    name: Push Docker image to Docker Hub
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      # work around for npm installs from git+https://github.com/johannesjo/J2M.git
      - name: Reconfigure git to use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@369eb591f429131d6889c46b94e711f089e6ca96
        with:
          images: johannes<PERSON>/super-productivity

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            BUILDKIT_CONTEXT_KEEP_GIT_DIR=1
          platforms: linux/amd64, linux/arm64, linux/arm/v7
          cache-from: type=gha
          cache-to: type=gha,mode=max
