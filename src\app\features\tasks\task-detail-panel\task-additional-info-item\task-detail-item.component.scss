@import '../../../../../common';

:host {
  display: block;

  &:focus {
    outline: none;
  }

  &.color-warn {
    .input-item__value {
      color: var(--c-warn) !important;
      font-weight: bold;
    }
  }

  &::ng-deep task ::ng-deep {
    margin: 2px;

    .parent-title {
      display: none;
    }

    tag-list {
      display: none;
    }

    .drag-handle {
      // since not draggable
      display: none;
    }

    .title-and-tags-wrapper {
      margin-left: var(--s2);
    }

    .box {
      @include lightTheme {
        border-color: var(--theme-extra-border-color);
        bottom: 0;
        top: -1px;
      }

      @include darkTheme {
        box-shadow: var(--whiteframe-shadow-1dp);
        background: var(--dark16) !important;
      }
    }
  }
}

// SHARED
// ------
.input-item,
.mat-expansion-panel {
  box-shadow: none;
  //overflow: inherit !important;
  border-width: 1px;
  border-style: solid;
  margin: 8px;
  border-radius: var(--card-border-radius);
  //overflow: visible;
  border-color: transparent;

  :host-context(.isNoTouchOnly):focus & {
    border-color: var(--palette-primary-400) !important;
  }

  &,
  ::ng-deep & .mat-expansion-panel-header {
    font-size: 14px;
  }

  ::ng-deep & .mat-expansion-panel-header-title {
    ::ng-deep mat-icon:first-of-type {
      margin-right: var(--s);
    }
  }

  @include darkTheme() {
    background: var(--dark10);
    box-shadow: var(--whiteframe-shadow-1dp);
    //border-color: $dark-theme-separator-color;
  }
  @include lightTheme {
    background: var(--theme-card-bg);
    border-color: var(--theme-extra-border-color);
    //border-color: $light-theme-separator-color;
  }
}

mat-expansion-panel::ng-deep .mat-expansion-panel-header:not(.mat-expanded),
.input-item.standard-size {
  &:hover,
  &:focus {
    :host-context(.isNoTouchOnly.isLightTheme) & {
      background: var(--theme-bg-darker);
    }

    :host-context(.isNoTouchOnly.isDarkTheme) & {
      background: var(--theme-bg-lightest);
    }
  }
}

// PANEL
// -----
:host::ng-deep {
  .mat-expansion-panel:not([class*='mat-elevation-z']) {
    box-shadow: none;
  }

  .mat-expansion-panel-body {
    padding: 0;
  }

  .mat-expansion-panel-header {
    position: sticky;
    top: 0;
  }

  .mat-expansion-panel-header-title {
    flex-grow: 1;
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;

    //min-width: 130px;
  }

  .mat-expansion-panel-header-description {
    text-align: right;

    > * {
      margin-left: auto;
      display: flex;
      align-items: center;
    }
  }
}

.panel-content-wrapper {
  margin: 0 var(--s) var(--s);
  display: block;
  color: var(--theme-text-color-less-intense);
  &::ng-deep inline-markdown {
    &.isFocused {
      border-color: transparent;
    }

    .markdown-wrapper {
      min-height: 60px;
      border-radius: var(--card-border-radius);
      overflow: visible !important;
      transition: var(--transition-standard);

      @include lightTheme {
        background: var(--theme-bg-lightest);
        //border: 1px solid $light-theme-extra-border-color;
      }
      @include darkTheme() {
        background: var(--theme-bg-lightest);
        box-shadow: var(--whiteframe-shadow-1dp);
      }
    }

    &.isFocused .markdown-wrapper {
      box-shadow: var(--whiteframe-shadow-3dp) !important;
      border-color: transparent;
    }

    .markdown-unparsed,
    .markdown-parsed {
      color: inherit;
      background-color: inherit;
    }
  }
}

:host::ng-deep issue-header > * {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.ripple {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}

// Input-Item
// ----------
.input-item {
  position: relative;
  display: flex;
  align-items: center;
  height: 48px;
  cursor: pointer;

  &.full-size {
    height: auto;
    min-height: 48px;
    align-items: flex-start;
    cursor: default;
  }

  color: var(--theme-text-color);

  &.--estimate {
    progress-bar {
      background: var(--theme-extra-border-color);
    }

    &:hover progress-bar {
      background: var(--c-accent) !important;
    }
  }
}

.input-item__title {
  display: flex;
  align-items: center;
  margin: 0 var(--s3);
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 90px;
  flex-grow: 1;

  ::ng-deep mat-icon:first-of-type {
    margin-right: var(--s);
  }

  .full-size & {
    min-width: 40px;
    min-height: 48px;
    flex-grow: 0;
    margin-right: 0;
  }
}

.input-item__value {
  text-align: right;
  flex-grow: 0;
  display: flex;
  align-items: center;
  margin-right: var(--s3);

  .full-size & {
    min-width: 40px;
    flex-grow: 1;
    padding-top: 4px;
    padding-bottom: 4px;
    min-height: 48px;

    > * {
      display: block;
    }
  }

  &,
  ::ng-deep span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // TODO find out if still needed
  //::ng-deep mat-icon {
  //  margin-right: var(--s);
  //  font-size: 32px;
  //  height: 32px;
  //  width: 32px;
  //}

  color: var(--theme-task-detail-value-color);

  > * {
    flex-grow: 1;
    margin-left: auto;
    text-align: right;
    display: inline-block;
  }
}

.input-item__edit-btn {
  //opacity: 0;
  margin-left: calc(-1 * var(--s3));
  margin-right: var(--s);
  display: none !important;

  .input-item:hover & {
    //opacity: 1;
    display: block !important;
  }
}
