<svg width="500"
     height="100"
     xmlns="http://www.w3.org/2000/svg">

  <defs>
    <linearGradient id="groundGradient"
                    x1="0"
                    y1="1"
                    x2="0"
                    y2="0">
      <stop offset="0%"
            stop-color="white"
            stop-opacity="0" />
      <stop offset="50%"
            stop-color="white"
            stop-opacity="0.01" />
      <stop offset="100%"
            stop-color="white"
            stop-opacity="0.12" />
    </linearGradient>

    <mask id="fadeSides">
      <linearGradient id="sideFade"
                      x1="0"
                      y1="0"
                      x2="1"
                      y2="0">
        <stop offset="0%"
              stop-color="black"
              stop-opacity="0" />
        <stop offset="20%"
              stop-color="white"
              stop-opacity="1" />
        <stop offset="80%"
              stop-color="white"
              stop-opacity="1" />
        <stop offset="100%"
              stop-color="black"
              stop-opacity="0" />
      </linearGradient>
      <rect width="100%"
            height="100%"
            fill="url(#sideFade)" />
    </mask>

    <filter id="glow"
            x="-50%"
            y="-50%"
            width="200%"
            height="200%">
      <feDropShadow dx="0"
                    dy="0"
                    stdDeviation="5"
                    flood-color="white"
                    flood-opacity="0.9" />
    </filter>

    <clipPath id="cut-off-bottom">
      <rect x="0"
            y="0"
            width="400"
            height="50" />
    </clipPath>
  </defs>

  <!--  <rect width="100%"-->
  <!--        height="100%"-->
  <!--        fill="#003" />-->


  <circle cx="250"
          cy="50"
          r="40"
          filter="url(#glow)"
          fill="#fff"
          opacity=".5"
          clip-path="url(#cut-off-bottom)" />

  <circle cx="250"
          cy="50"
          r="42"
          filter="url(#glow)"
          fill="#fff"
          opacity=".3" />

  <rect y="50"
        width="100%"
        height="50"
        fill="url(#groundGradient)"
        mask="url(#fadeSides)" />


</svg>
