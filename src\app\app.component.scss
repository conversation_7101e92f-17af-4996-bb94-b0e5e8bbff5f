@import '../common';

:host {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header-wrapper {
  position: relative;
  z-index: var(--z-main-header-wrapper);
  display: block;
  transition: var(--transition-enter);
  border-bottom: 1px solid transparent;

  border-color: var(--theme-extra-border-color);

  &.isNotScrolled {
    border-color: transparent !important;
  }
}

.route-wrapper {
  flex-grow: 1;
  position: relative;
  overflow: hidden;
  -webkit-app-region: no-drag !important;

  > ::ng-deep router-outlet + * {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    overflow-x: hidden;
    @include scrollY();
  }

  > ::ng-deep {
    tag-task-page,
    project-task-page {
      overflow: hidden;
    }
  }
}

.backdrop {
  // global styles in backdrop.scss
}

add-task-bar {
  z-index: var(--z-add-task-bar);
}

main-header {
  position: relative;
  z-index: var(--z-main-header);
}

.right-panel {
  min-width: 300px;
  width: 40vw;
  max-width: 700px;
  min-height: 100vh;
  // mobile viewport bug fix
  min-height: -webkit-fill-available;

  .mat-drawer-over & {
    width: 80vw;

    @include mq(xs, max) {
      width: 90vw;
    }
  }
}

// SIDENAV
// -------
:host ::ng-deep .mat-drawer-content {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

mat-sidenav-container.mat-sidenav-container {
  background: transparent;
}

mat-sidenav,
mat-sidenav-container {
  height: 100%;
}

// sidebars
mat-sidenav ::ng-deep .mat-drawer-inner-container {
  @include scrollYImportant();

  background: var(--theme-sidebar-bg);
}

mat-sidenav {
  width: fit-content;
  z-index: var(--z-side-nav);
}
@media (min-width: 800px) {
  button.mat-mdc-menu-item {
    width: 600px;
  }
}

.bg-image {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: none;
  width: 100%;
  height: 100%;
  background-size: cover !important;
  background-position: center !important;
  background-attachment: fixed;
}
