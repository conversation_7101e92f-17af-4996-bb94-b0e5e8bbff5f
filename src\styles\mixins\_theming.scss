@mixin darkTheme($is-global: false) {
  $sel: #{&};
  @if ($is-global) {
    .isDarkTheme & {
      @content;
    }
  } @else if ($sel and $sel == ':host') {
    @at-root :host-context(.isDarkTheme) {
      @content;
    }
  } @else if ($sel and $sel != '') {
    @at-root :host-context(.isDarkTheme) & {
      @content;
    }
  } @else {
    :host-context(.isDarkTheme) {
      @content;
    }
  }
}

@mixin lightTheme($is-global: false) {
  $sel: #{&};
  @if ($is-global) {
    .isLightTheme & {
      @content;
    }
  } @else if ($sel and $sel == ':host') {
    @at-root :host-context(.isLightTheme) {
      @content;
    }
  } @else if ($sel and $sel != '') {
    @at-root :host-context(.isLightTheme) & {
      @content;
    }
  } @else {
    :host-context(.isLightTheme) {
      @content;
    }
  }
}

@mixin standardThemeTextColor {
  color: var(--theme-text-color);
}

@mixin standardThemeTextColorLessIntense {
  color: var(--theme-text-color-less-intense);
}

@mixin standardThemeTextColorMostIntense {
  color: var(--theme-text-color-most-intense);
}

@mixin extraBorder($sideSuffix: '') {
  #{'border'+$sideSuffix}: 1px solid var(--theme-extra-border-color);
}

// e.g. cards and tasks
@mixin layerTextAndBgHigher {
  color: var(--theme-text-color-less-intense);
  background-color: var(--theme-bg-lighter);
}

// e.g. nested cards / tabs etc
@mixin layerTextAndBgHighest {
  // NOTE: needs to be more intense than light color, because we have a lighter background
  color: var(--theme-text-color-more-intense);
  background-color: var(--theme-bg-lightest);
}

@mixin dividerBorderColor($border-width: 0 0 1px 0, $is-global: false) {
  border-style: solid;
  border-width: $border-width;
  border-color: var(--theme-divider-color);
}

@mixin flatBox($is-global: false) {
  border:
    1px,
    $is-global solid var(--theme-divider-color);
}
