@use 'angular-material-css-vars' as mat-css-vars;
@import '../_common';

html,
body,
app-root,
.app-frame {
  overflow: hidden;
  margin: 0;
  /* stylelint-disable-next-line no-duplicate-selectors */
  height: 100%;
  height: -webkit-fill-available;
  box-sizing: border-box;
  //font-family: 'Montserrat', 'Roboto', sans-serif;
  //font-family: 'Open Sans', sans-serif;
  //font-family: 'Roboto', sans-serif;
  font-family: 'Roboto', 'Comic Sans MS', sans-serif;
}

html {
  overflow: hidden !important;

  button,
  a {
    -webkit-app-region: no-drag !important;
  }
}

body {
  // to fix older android web view
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;

  &:before {
    display: none;
    transition: 1s opacity;
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    opacity: 0;
  }

  &.isEnabledBackgroundGradient {
    &:before {
      display: block;
      opacity: 1;
    }
  }

  &.isLightTheme {
    color: var(--theme-text-color);
    background: var(--theme-bg);

    &.isDisableBackgroundGradient {
      background: var(--theme-bg);
      //&:before {
      //  display: block;
      //  opacity: .3;
      //  background: mat-css-vars.mat-css-color(50, 1);
      //}
    }

    &:before {
      filter: saturate(0.8);
      background: linear-gradient(
        180deg,
        mat-css-vars.mat-css-color(100, 1) 0%,
        mat-css-vars.mat-css-color(50, 1) 140px,
        var(--theme-bg) 100%
      );
    }
  }

  &.isDarkTheme {
    color: var(--theme-text-color);
    background: var(--theme-bg);

    &.isDisableBackgroundGradient {
      background: var(--theme-bg);
    }

    &:before {
      filter: saturate(0.8);
      background: linear-gradient(
        180deg,
        mat-css-vars.mat-css-color(800, 0.35) 0%,
        mat-css-vars.mat-css-color(900, 0.1) 140px,
        mat-css-vars.mat-css-color(900, 0.02) 100%
      );
    }
  }

  // also hide material dialogs while import is in progress
  &.isDataImportInProgress {
    .cdk-overlay-container {
      display: none !important;
    }
  }
}

.page-wrapper {
  padding: var(--s) var(--s) calc(var(--s) * 9);

  @include mq(xs) {
    padding: var(--s) var(--s2) calc(var(--s) * 9);
  }
}

.component-wrapper {
  max-width: var(--component-max-width);
  margin: auto;
}

.task-list-wrapper {
  padding: 0 0 var(--s7);
  // for a little bit of extra space for 800 width
  max-width: calc(var(--component-max-width) - 40px);
  margin: auto;

  @include mq(xxs) {
    padding: 0 var(--s) var(--s7);
  }

  @include mq(xs) {
    padding: 0 calc(var(--s) * 1.5) var(--s7);
  }

  @include mq(sm) {
    padding: 0 var(--s3) var(--s7);
    max-width: var(--component-max-width);
  }

  @include mq(lg) {
    padding: 0 var(--s3) var(--s7);
    max-width: calc(var(--component-max-width) + 100px);
  }

  improvement-banner + & {
    padding-top: 0;
  }
}

blockquote {
  border-left: 4px solid rgba(var(--c-accent), 1);
  margin: 20px 0;
  padding: 1px 20px;
}

a[href] {
  color: var(--c-accent);
}

*,
*:after,
*:before {
  box-sizing: border-box;
}

// make images responsive per default
img {
  max-width: 100%;
  height: auto;
  display: block;
}

// scrollbars
* {
  --scrollbar-width: thin;
  --scrollbar-width-legacy: 4px;
  --scrollbar-color-thumb: var(--theme-scrollbar-thumb);
  --scrollbar-color-track: var(--theme-scrollbar-track);

  .isDarkTheme & {
    --scrollbar-color-thumb: var(--theme-scrollbar-thumb);
    --scrollbar-color-track: var(--theme-scrollbar-track);
  }

  /* Modern browsers with `scrollbar-*` support */
  @supports (scrollbar-width: auto) {
    & {
      scrollbar-color: var(--scrollbar-color-thumb) var(--scrollbar-color-track);
      scrollbar-width: var(--scrollbar-width);
    }
  }

  /* Legacy browsers with `::-webkit-scrollbar-*` support */
  @supports selector(::-webkit-scrollbar) {
    &::-webkit-scrollbar-thumb {
      background: var(--scrollbar-color-thumb);
    }

    &::-webkit-scrollbar-track {
      background: var(--scrollbar-color-track);
    }

    &::-webkit-scrollbar {
      max-width: var(--scrollbar-width-legacy);
      max-height: var(--scrollbar-width-legacy);
    }
  }
}
