@import '../mixins/media-queries';

@mixin _scrollBarStyles() {
  scrollbar-color: var(--theme-scrollbar-thumb) var(--theme-scrollbar-track);
  scrollbar-width: 4px;

  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: var(--theme-scrollbar-track);
    border-radius: 4px;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: var(--theme-scrollbar-thumb);
    border-radius: 16px;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: var(--theme-scrollbar-thumb-hover);
  }
}

@include mq(xs) {
  .isNoMac {
    @include _scrollBarStyles;
  }
}

@include mq(xs, max) {
  .isNoTouchOnly {
    @include _scrollBarStyles;
  }
}
