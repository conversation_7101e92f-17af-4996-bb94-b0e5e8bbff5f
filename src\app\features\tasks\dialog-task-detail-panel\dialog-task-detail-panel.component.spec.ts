// import { ComponentFixture, TestBed } from '@angular/core/testing';
//
// import { DialogTaskAdditionalInfoPanelComponent } from './dialog-task-additional-info-panel.component';
//
// describe('DialogTaskAdditionalInfoPanelComponent', () => {
//   let component: DialogTaskAdditionalInfoPanelComponent;
//   let fixture: ComponentFixture<DialogTaskAdditionalInfoPanelComponent>;
//
//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [DialogTaskAdditionalInfoPanelComponent]
//     })
//     .compileComponents();
//
//     fixture = TestBed.createComponent(DialogTaskAdditionalInfoPanelComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
