<!--<h1 mat-dialog-title>{{T.F.SYNC.D_CONFLICT.TITLE|translate}}</h1>-->
<h1 mat-dialog-title>{{ T.F.SYNC.D_INITIAL_CFG.TITLE | translate }}</h1>

<mat-dialog-content>
  @if (_tmpUpdatedCfg) {
    <form [formGroup]="form">
      <formly-form
        (modelChange)="updateTmpCfg($event)"
        [fields]="fields()"
        [form]="form"
        [model]="_tmpUpdatedCfg"
      >
      </formly-form>
    </form>
  }
</mat-dialog-content>

<mat-dialog-actions align="end">
  <div class="wrap-buttons">
    <button
      (click)="close()"
      color="primary"
      mat-button
    >
      {{ T.G.CANCEL | translate }}
    </button>
    <button
      [disabled]="!form.valid"
      (click)="save()"
      color="primary"
      mat-stroked-button
    >
      <mat-icon>save</mat-icon>
      {{
        (this.isWasEnabled() ? T.G.SAVE : T.F.SYNC.D_INITIAL_CFG.SAVE_AND_ENABLE)
          | translate
      }}
    </button>
  </div>
</mat-dialog-actions>
