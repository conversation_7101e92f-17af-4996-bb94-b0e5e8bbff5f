// import * as fromTimeTracking from './time-tracking.reducer';
// import { selectTimeTrackingState } from './time-tracking.selectors';
//
// describe('TimeTracking Selectors', () => {
//   it('should select the feature state', () => {
//     const result = selectTimeTrackingState({
//       [fromTimeTracking.TIME_TRACKING_FEATURE_KEY]: {},
//     });
//
//     expect(result).toEqual({});
//   });
// });
