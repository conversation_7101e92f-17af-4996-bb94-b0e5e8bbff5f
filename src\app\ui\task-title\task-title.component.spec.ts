// import { ComponentFixture, TestBed } from '@angular/core/testing';
//
// import { InlineMultilineInputComponent } from './inline-multiline-input.component';
//
// describe('InlineMultilineInputComponent', () => {
//   let component: InlineMultilineInputComponent;
//   let fixture: ComponentFixture<InlineMultilineInputComponent>;
//
//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [InlineMultilineInputComponent],
//     }).compileComponents();
//
//     fixture = TestBed.createComponent(InlineMultilineInputComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
