:host {
  display: block;
}

.collapsible-header {
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none; /* Safari */
  display: flex;
  flex-direction: row;
  align-items: center;

  &:focus {
    outline: none;
  }

  :host.isInline & {
    display: inline-block;

    & > * {
      display: inline-block;
      vertical-align: middle;
    }
  }
}

.collapsible-title {
  flex-grow: 1;

  :host.isInline & {
    //display: inline-block;
  }
}

.collapsible-expand-icon {
  margin-right: var(--s);
  transition: all var(--transition-standard);

  :host.isExpanded & {
    transform: rotate(180deg);
  }
}

.collapsible-panel {
  height: auto;
  display: block;

  // make sure we don't produce clunky animations
  padding: 0 !important;
  margin: 0 !important;
}
