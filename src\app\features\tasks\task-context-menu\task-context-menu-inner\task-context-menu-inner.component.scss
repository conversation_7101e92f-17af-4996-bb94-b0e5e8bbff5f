@import '../../../../../common.scss';
@import '../task-context-menu-touch-fix.scss';

.key-i {
  opacity: 0.4;
  font-size: 11px;
  text-align: center;
  border-radius: 20px;
  max-width: 66px;
  letter-spacing: -1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: none;

  &:not(:empty) {
    display: block;
    line-height: 1;
    min-width: 20px;
    border: 1px solid;
    padding: 4px;
    margin-left: 4px;
  }
}

.menuItemLeft {
  text-align: left;
  display: flex;
  align-items: center;
}

.quick-access {
  display: flex;
  justify-content: space-evenly;
  border-bottom: 1px solid var(--theme-extra-border-color);
  height: 48px;

  > button {
    height: 48px;
    min-width: 40px;
    flex-grow: 1;
    border-radius: 4px !important;

    ::ng-deep .mat-mdc-button-persistent-ripple {
      border-radius: 4px !important;
    }
  }

  button + button {
    border-left: 1px solid var(--theme-extra-border-color) !important;
  }
}

.label {
  display: block;
  text-align: center;
}

[mat-menu-item] {
  @include mq(xs, max) {
    padding-right: 12px;
  }

  mat-icon {
    @include mq(xs, max) {
      margin-right: 12px;
    }
  }
}

.tag-ico {
  margin-right: 4px;

  @include mq(xs, max) {
    font-size: 16px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    margin-right: 4px;
  }
}

.check-ico {
  margin-right: 16px;

  @include mq(xs, max) {
    //font-size: 20px;
    //width: 20px;
    //height: 20px;
    //line-height: 20px;
    margin-right: 12px;
  }
}
