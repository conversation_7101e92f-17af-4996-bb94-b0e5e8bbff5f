name: 💡 Feature Request
description: Suggest an idea for this project
title: '💡 '
labels: ['enhancement']
assignees: []

body:
  - type: markdown
    attributes:
      value: |

        Thank you for taking the time to fill out this feature request fully! This will help a lot to communicate what this is about and focus the discussion.

        Please also make sure that there is no similar feature request opened up already by searching the issues in this repository!

  - type: textarea
    id: problem-statement
    attributes:
      label: Problem Statement
      description: "A clear and concise description of what the problem is. E.g. I'm always frustrated when [...]"
      placeholder: "Describe the problem you're facing or the limitation you've encountered."
    validations:
      required: true

  - type: textarea
    id: possible-solution
    attributes:
      label: 'Possible Solution'
      description: 'A clear and concise description of what you want to happen.'
      placeholder: 'Describe the feature or enhancement you are proposing.'
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: "Describe alternatives you've considered"
      description: "A clear and concise description of any alternative solutions or features you've considered."
      placeholder: 'Have you thought about other ways to solve the problem? What were they?'
    validations:
      required: false # Often optional, but good to prompt for

  - type: textarea
    id: additional-context
    attributes:
      label: 'Additional context'
      description: 'Add any other context about the problem here. e.g. related issues or existing pull requests.'
      placeholder: 'Add links to related issues, screenshots, mockups, or any other relevant information.'
    validations:
      required: false
