@use 'sass:map';

// Media query breakpoints - must be SASS variables, not CSS variables
// because media queries are evaluated at parse time
$layout-xxxs: 398px;
$layout-xxs: 440px;
$layout-xs: 600px;
$layout-sm: 960px;
$layout-md: 1280px;
$layout-lg: 1920px;
$layout-xl: 2000px;
$component-max-width: 800px;

@mixin mq($size, $type: min) {
  $f: 0;
  @if ($type == max) {
    $f: -1;
  }

  $width: map.get(
    (
      'xxxs': $layout-xxxs,
      'xxs': $layout-xxs,
      'xs': $layout-xs,
      'sm': $layout-sm,
      'md': $layout-md,
      'lg': $layout-lg,
      'xl': $layout-xl,
      'component': $component-max-width,
    ),
    $size
  );

  @if ($width) {
    @media screen and ($type + -width: $width  + $f) {
      @content;
    }
  } @else {
    @error 'Invalid size for mq';
  }
}
