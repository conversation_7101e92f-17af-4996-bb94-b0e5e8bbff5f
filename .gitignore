# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/.tmp
/dist
/app-builds
/tmp
/logs
packages/plugin-api/dist/**


# dependencies
/node_modules

# IDEs and editors
/.idea
android/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

### VisualStudioCode Patch ###
# Ignore all local history of files
.history

# misc
\\backups
/backups
/.angular/cache
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings
chromedriver.log
.eslintcache

# System Files
.DS_Store
Thumbs.db

# iOS build artifacts
ios/App/App/public/
ios/App/App/capacitor.config.json
ios/App/App/config.xml
ios/App/App/Plugins/

# sometimes idea does compile ts files...
src/app/**/*.js
src/app/**/*.js.map
# added
build/hub
electron/**/*.js
electron/**/*.js.map

# mac os deployment
*.provisionprofile
*.Xprovisionprofile
tools/mac-profiles
all-certs.p12
mac-cert.tar
build/electron-builder.appx.yaml
electron-builder.win-store.yaml

# ignore changes
./src/environments/versions.ts
/src/environments/environment.js
/src/environments/environment.js.map
/.claude/settings.local.json
.aider*

/packages/plugin-api/dist/**/*.*
/packages/plugin-api/src/**/*.js
/packages/plugin-api/src/**/*.js.map

#
perf-metrics-create-tasks.json
perf-metrics-initial-load.json


# bundled plugins (generated by build process)
src/assets/bundled-plugins/**/*.*


electron-builder-appx.yaml
