// import { async, ComponentFixture, TestBed } from '@angular/core/testing';
//
// import { DialogViewTaskReminderComponent } from './dialog-view-task-reminder.component';
//
// describe('DialogViewTaskReminderComponent', () => {
//   let component: DialogViewTaskReminderComponent;
//   let fixture: ComponentFixture<DialogViewTaskReminderComponent>;
//
//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [DialogViewTaskReminderComponent]
//     })
//       .compileComponents();
//   }));
//
//   beforeEach(() => {
//     fixture = TestBed.createComponent(DialogViewTaskReminderComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
