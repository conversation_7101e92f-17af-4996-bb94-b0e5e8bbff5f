// import { ComponentFixture, TestBed } from '@angular/core/testing';
//
// import { SimpleCounterSummaryItemComponent } from './simple-counter-summary-item.component';
//
// describe('SimpleCounterSummaryItemComponent', () => {
//   let component: SimpleCounterSummaryItemComponent;
//   let fixture: ComponentFixture<SimpleCounterSummaryItemComponent>;
//
//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [SimpleCounterSummaryItemComponent]
//     })
//     .compileComponents();
//
//     fixture = TestBed.createComponent(SimpleCounterSummaryItemComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
