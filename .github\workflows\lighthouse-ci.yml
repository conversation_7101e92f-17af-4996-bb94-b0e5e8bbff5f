name: Lighthouse CI
on:
  pull_request:
    branches:
      - master
      - main
  workflow_dispatch:
    inputs: {}
  release:
    types: [published]

jobs:
  lighthouse:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm i

      - name: Build production web app
        run: npm run buildFrontend:prodWeb

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v12
        with:
          # Configure Lighthouse CI
          configPath: './tools/lighthouse/.lighthouserc.json'
          # Upload results to temporary storage
          uploadArtifacts: true
          temporaryPublicStorage: true
          # Run 3 times for consistency
          runs: 3
          # Configure budgets
          budgetPath: './tools/lighthouse/budget.json'
