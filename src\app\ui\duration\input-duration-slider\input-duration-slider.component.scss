@import '../../../../common';

$this-size: 130px;
$this-handle-size: 20px;
$this-extra-touch-space: $this-handle-size * 0.5;
$this-margin: $this-extra-touch-space;
$this-handle-scale: 2;
$this-handle-color: var(--c-accent);
$this-circle-width: 2px;
$this-dot-size: 14px;

@mixin onCircle($item-count, $circle-size, $item-size, $start-rotation: -90) {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -($item-size * 0.5);
  width: $item-size;
  height: $item-size;

  $angle: ddivide(360, $item-count);
  $rot: $start-rotation;

  @for $i from 1 through $item-count {
    &:nth-of-type(#{$i}) {
      transform: rotate($rot * 1deg) translate($circle-size * 0.5);
    }

    $rot: $rot + $angle;
  }
}

:host {
  overflow: hidden;
  display: block;
  user-select: none;
  -webkit-user-select: none; /* Safari */
  width: $this-size + $this-margin * 2;
  min-width: $this-size + $this-margin * 2;
  color: var(--theme-text-color);
  &.is-dragging .circle {
    cursor: grabbing;
  }

  &.is-dragging .handle-wrapper {
    transition: none;
  }

  &.is-dragging {
    .value-wrapper {
      z-index: 1;
      pointer-events: none;
    }
  }
}

.circle {
  margin: $this-margin;
  width: $this-size;
  height: $this-size;
  border-radius: 100%;
  position: relative;
  box-shadow: 1px 2px 2px 0 rgba(0, 0, 0, 0.26);
  cursor: grab;

  border-color: transparent;

  // touch spacer
  &:after {
    content: '';
    position: absolute;
    top: -$this-extra-touch-space;
    right: -$this-extra-touch-space;
    left: -$this-extra-touch-space;
    bottom: -$this-extra-touch-space;
    border-radius: 100%;
  }

  // inner circle
  .inner-circle {
    position: absolute;
    top: $this-circle-width;
    right: $this-circle-width;
    left: $this-circle-width;
    bottom: $this-circle-width;
    border-radius: 100%;
    background: var(--theme-bg-darker);
    border-color: transparent;
  }

  .dots {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    width: $this-size;
    height: $this-size;
    z-index: 4;
    pointer-events: none;
  }

  .dot {
    box-shadow: 1px 1px 1px var(--theme-separator-alpha);
    height: 8px;
    width: 8px;
    border-radius: 100%;
    cursor: grabbing;
    background: var(--palette-primary-300);
    @include onCircle(12, $this-size - $this-circle-width, $this-dot-size);
  }
}

$this-handle-border-width: 4px;

.handle-wrapper {
  cursor: grab;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 2;
  transition: var(--transition-duration-m) var(--ani-standard-timing);

  &:after {
    content: '';
    position: absolute;
    top: -$this-extra-touch-space;
    right: -$this-extra-touch-space;
    left: -$this-extra-touch-space;
    bottom: -$this-extra-touch-space;
  }
}

$this-stroke-width: 4px;

.handle {
  top: 0;
  background: $this-handle-color;
  left: 50%;
  width: 0;
  height: $this-handle-size + 5;
  position: absolute;
  transform-origin: 50% 100%;
  cursor: grabbing;

  // actual handle
  &:after {
    box-shadow: 1px 1px 2px var(--theme-separator-alpha);
    content: '';
    top: -$this-handle-size * 0.5 + $this-circle-width * 0.5;
    background: $this-handle-color;
    left: 50%;
    margin-left: -#{$this-handle-size * 0.5};
    position: absolute;
    transform-origin: 50% 100%;
    height: $this-handle-size;
    width: $this-handle-size;
    border-radius: 100%;
    cursor: grabbing;
  }
}

.value-wrapper {
  z-index: 3;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  text-align: center;
  transform: translate3d(-50%, -50%, 0);
}

.value {
  width: 100%;
  text-align: center;
  font-size: 16px;
  line-height: 16px;
  padding: 0;
  cursor: text;
  transition: all var(--transition-duration-m) var(--ani-leave-timing);
  transition-property: opacity, transform;
  display: block;
  position: relative;
  overflow: visible;
  user-select: none;
  -webkit-user-select: none; /* Safari */
  backface-visibility: hidden;
  transform: translateZ(0);
  z-index: 4;
  border: none;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  background-color: transparent;

  caret-color: var(--theme-text-color-most-intense);
  color: var(--theme-text-color-most-intense);

  &:focus {
    @include inlineEditElevation();
    outline: none;
    z-index: 5;
    user-select: all;
    -webkit-user-select: all; /* Safari */
    transform: scale(1.2);

    &:after {
      opacity: 1;
    }

    @media (max-width: var(--layout-xs)) {
      transform: scale(1.05);
    }
  }
}

label {
  font-size: 13px;
  margin-bottom: 3px;
  display: inline-block;
  cursor: pointer;
  text-align: center;
  backface-visibility: hidden;
}
