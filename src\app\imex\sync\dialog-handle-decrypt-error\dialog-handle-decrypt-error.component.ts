import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import {
  MatDialogActions,
  MatDialogContent,
  MatDialogRef,
} from '@angular/material/dialog';
import { T } from '../../../t.const';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { FormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { TranslatePipe } from '@ngx-translate/core';
import { SyncConfigService } from '../sync-config.service';

@Component({
  selector: 'dialog-handle-decrypt-error',
  templateUrl: './dialog-handle-decrypt-error.component.html',
  styleUrls: ['./dialog-handle-decrypt-error.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    MatD<PERSON>ogContent,
    <PERSON>Form<PERSON>ield,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>Input,
    FormsModule,
    Mat<PERSON><PERSON>ogA<PERSON>,
    Mat<PERSON>utton,
    MatIcon,
    TranslatePipe,
  ],
})
export class DialogHandleDecryptErrorComponent {
  private _syncConfigService = inject(SyncConfigService);

  private _matDialogRef =
    inject<MatDialogRef<DialogHandleDecryptErrorComponent>>(MatDialogRef);

  T: typeof T = T;
  passwordVal: string = '';

  updatePWAndForceUpload(): void {
    this._syncConfigService.updateEncryptionPassword(this.passwordVal);
    this._matDialogRef.close({ isForceUpload: true });
  }

  updatePwAndResync(): void {
    this._syncConfigService.updateEncryptionPassword(this.passwordVal);
    this._matDialogRef.close({ isReSync: true });
  }

  cancel(): void {
    this._matDialogRef.close({});
  }
}
