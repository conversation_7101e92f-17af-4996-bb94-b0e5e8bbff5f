// import { async, ComponentFixture, TestBed } from '@angular/core/testing';
//
// import { DialogEditAttachmentComponent } from './dialog-edit-attachment.component';
//
// describe('DialogEditAttachmentComponent', () => {
//   let component: DialogEditAttachmentComponent;
//   let fixture: ComponentFixture<DialogEditAttachmentComponent>;
//
//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [DialogEditAttachmentComponent]
//     })
//       .compileComponents();
//   }));
//
//   beforeEach(() => {
//     fixture = TestBed.createComponent(DialogEditAttachmentComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
