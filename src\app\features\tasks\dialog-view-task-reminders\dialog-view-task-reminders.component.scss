@import '../../../../common';

:host {
  //text-align: center;
}

.tasks {
  margin-bottom: var(--s2);
}

.task {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--theme-extra-border-color);
  padding: var(--s);
  padding-left: 0;

  &:last-child {
    border-bottom: 0;
  }

  .title-wrapper {
    flex: 1;
    margin-right: var(--s);
  }

  .due-for {
    margin-left: var(--s);
    font-size: 11px;
    line-height: 1.4;
    padding-bottom: 4px;

    &.emphasized {
      //font-weight: bold;
    }
  }

  .title {
    margin-left: var(--s);
  }

  tag-list {
    margin-top: var(--s-half);
  }

  .actions {
    @include mq(sm) {
      margin: var(--s);
    }

    button {
      &:last-of-type {
        margin-right: 0;
      }
    }
  }
}
