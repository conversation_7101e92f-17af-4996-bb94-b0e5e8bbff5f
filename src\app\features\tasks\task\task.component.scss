@import '../../../../common';
@import './task.component.mixins';

// TASK BOX STYLES
// ---------------
// NOTE: needs to come first to be overwritten by :host.isCurrent
// NOTE: needs to be very light, because we're on a lighter background for the dark theme
:host-context(.isDarkTheme) {
  color: var(--theme-text-color);
}

:host-context(.isDarkTheme).isSelected,
:host-context(.isDarkTheme).isCurrent {
  color: var(--theme-text-color-most-intense);
}

:host {
  --task-button-spacer: 1px;
  --play-icon-size: 22px;
  --task-icon-size: 40px;
  --mini-badge-size: var(--s2);

  display: block;
  position: relative;

  color: var(--theme-text-color);

  &.isSelected {
    z-index: var(--z-is-selected-host);
    //position: sticky;
    //top: 16px;
  }

  &.isCurrent {
    z-index: var(--z-is-current-host);
    position: sticky;
    top: var(--s);
  }

  &:focus {
    z-index: var(--z-focus-host);
    outline: none;
  }
}

.inner-wrapper {
  position: relative;
  // NOTE: -1 px because we move the box slightly up
  padding: calc(var(--task-inner-padding-top-bottom) - 1px) 0
    var(--task-inner-padding-top-bottom);
  margin-bottom: 0;
}

//::ng-deep task .box {
//  background: red !important;
//}

.box {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 1px;
  top: 1px;
  transition: var(--transition-standard);
  transition-property: transform, box-shadow;
  border: 1px solid transparent;

  background: var(--task-c-bg);

  :host.gu-mirror & {
    box-shadow: var(--whiteframe-shadow-8dp) !important;
    border-radius: var(--task-border-radius) !important;
  }

  :host-context(.isDarkTheme).gu-mirror & {
    background: var(--task-c-drag-drop-bg) !important;
  }

  // NOTE: also applies to child tasks
  :host-context(.isLightTheme).isSelected.isSelected & {
    background: var(--task-c-selected-bg);
  }

  :host-context(.isDarkTheme).isDone & {
    background: var(--task-c-bg-done);
  }

  :host-context(.isDarkTheme).isSelected.isSelected & {
    background: var(--task-c-selected-bg);
  }

  :host.isSelected.isSelected > .inner-wrapper > & {
    transition: var(--transition-enter);
    transition-property: left, right, box-shadow;
    box-shadow: var(--task-selected-shadow);
    // because of the shadow, this looks better
    //bottom: 2px;

    @include darkTheme {
      box-shadow: var(--whiteframe-shadow-4dp);
    }

    @include mq(xs) {
      transform-origin: left;
      left: calc(-1 * var(--task-border-radius));
      border-top-left-radius: var(--task-border-radius);
      border-bottom-left-radius: var(--task-border-radius);
    }

    @at-root :host-context(.isDarkTheme).isSelected.isSelected.isSelected
        .sub-tasks
        ::ng-deep
        .box {
      background: var(--sub-task-c-bg-in-selected);
    }
  }

  :host:focus > .inner-wrapper > & {
    border-radius: var(--task-border-radius);
    // we don't want focus borders for touch only devices
    @include noTouchOnly() {
      border-color: var(--palette-primary-400);
      border-width: 1px;
      border-style: solid !important;
    }
  }

  :host:focus.mobile-highlight > .inner-wrapper > & {
    border-color: var(--palette-primary-400);
    border-width: 1px;
    border-style: solid !important;
  }

  :host.isCurrent.isCurrent.isCurrent.isCurrent.isCurrent & {
    border-radius: var(--task-border-radius);
    box-shadow: var(--task-current-shadow);
    // because of the shadow, this looks better
    //bottom: 2px;

    @include mq(xs) {
      //transform: scale($task-current-task-zoom);
      left: calc(-1 * var(--task-border-radius));
      right: calc(-1 * var(--task-border-radius));
    }
  }

  :host.isSelected.isSelected > .inner-wrapper > &,
  :host.isSelected.isCurrent.isCurrent.isCurrent.isCurrent & {
    @include mq(xs) {
      right: -34px;
    }
    @include mq(md) {
      right: -234px;
    }
    @include mq(xl) {
      right: calc(-1 * var(--task-border-radius));
    }
  }

  :host-context(.isLightTheme).isCurrent.isCurrent.isCurrent.isCurrent & {
    border-color: var(--c-accent) !important;
    border-width: 1px !important;
    border-style: dashed;
  }

  :host-context(.isDarkTheme).isCurrent.isCurrent.isCurrent.isCurrent & {
    background: var(--task-c-current-bg);
    box-shadow: var(--task-current-shadow);
  }

  :host:first-child > .inner-wrapper > & {
    border-top-left-radius: var(--task-border-radius);
    border-top-right-radius: var(--task-border-radius);
  }

  :host:last-child > .inner-wrapper > & {
    border-bottom-left-radius: var(--task-border-radius);
    border-bottom-right-radius: var(--task-border-radius);
  }
}

.sub-tasks {
  margin: var(--s) var(--s) var(--s) var(--s3);
  position: relative;

  @include smallMainContainer {
    margin: var(--s) var(--s) var(--s) var(--s3);
    position: relative;
  }

  @include mq(xs) {
    //margin: var(--s) var(--s2) var(--s) var(--s3);
    margin: var(--s) var(--s) var(--s) var(--s5);
  }

  ::ng-deep .box {
    @include lightTheme {
      border-color: var(--theme-extra-border-color);
      bottom: 0;
      top: -1px;
    }

    @include darkTheme {
      box-shadow: var(--whiteframe-shadow-1dp);
      background: var(--sub-task-c-bg);
    }
  }
}

:host-context(.isDarkTheme) ::ng-deep .isDone.isDone .box {
  background: var(--sub-task-c-bg-done);
}

.first-line {
  position: relative;
  display: flex;
  align-items: stretch;
  flex-wrap: nowrap;
  user-select: none;
  -webkit-user-select: none; /* Safari */
  justify-content: space-between;
  min-height: var(--task-first-line-min-height);
  touch-action: pan-y;

  @include mq(xs, max) {
    flex-wrap: wrap;
    justify-content: flex-end;
  }

  //@include smallMainContainer{
  //  flex-wrap: wrap;
  //  justify-content: flex-end;
  //}
}

.title-and-left-btns-wrapper {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  min-height: 36px;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 70%;
  // we cannot do this since this cuts of the box shadow of the task edit
  //overflow: hidden;

  @include mq(xs, max) {
    //margin-bottom: 5px;
    //flex-shrink: 0;
    flex-basis: 50%;
  }

  @include isDoneForFirstLine {
    opacity: var(--task-is-done-dim-opacity);
  }
}

// CONTROLS
// --------
.all-controls-wrapper {
  display: flex;
  flex-flow: row;
  position: relative;
  margin-left: var(--s);
  margin-right: var(--s);
  align-items: stretch;

  //@include verySmallMainContainer {
  //  flex-direction: column-reverse;
  //  align-items: flex-end;
  //  justify-content: center;
  //}
}

.controls {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  text-align: right;
  margin-right: 2px;
  position: relative;

  button {
    margin-top: auto;
    margin-bottom: auto;
    height: 100% !important;
    min-height: 40px;
    border-radius: var(--card-border-radius);

    ::ng-deep {
      .mat-mdc-button-persistent-ripple {
        border-radius: var(--card-border-radius);
      }
    }

    &:hover {
      ////  background: var(--color-overlay-light-10);
      //
      //
    }
  }

  @include mq(xs, max) {
    white-space: normal;
    margin-left: 5px;
  }

  @include smallMainContainer {
    white-space: normal;
    margin-left: 5px;
  }

  //@include verySmallMainContainer {
  //  min-height: 40px;
  //}
}

// OTHER UI ELEMENTS
// -----------------
.title-and-tags-wrapper {
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 180px;
  // we cannot do this since this cuts of the box shadow of the task edit
  //overflow: hidden;

  > * {
    @include touchOnlyDevice {
      pointer-events: none;
    }
  }

  // note: we use .tags-container, since we don't want margins for an empty list
  ::ng-deep .tags-container {
    // to match task title padding
    margin-left: 6px;
    margin-bottom: 5px;
  }
}

@mixin timeWrapperMinimalStyles() {
  .time {
    font-size: 13px;
    flex-direction: column;
  }

  .separator {
    border-top: 1px solid;
    height: 1px;
    opacity: 0.1;
    width: 100%;
  }
}

// TIME
.time-wrapper {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-right: var(--s);
  z-index: var(--z-time-wrapper);
  // note: fixes styles for empty time for very small mobile
  min-width: 25px;
  //border: 1px solid red;

  @include mq(xxs) {
    min-width: 0;
  }

  &.isEditable {
    cursor: pointer;

    &:hover {
      .time {
        transition: var(--transition-standard);
        opacity: 1 !important;
      }
    }
  }

  &.hasNoTimeSpentOrEstimate {
    display: none;
  }

  .time {
    font-style: italic;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0.6;
    font-weight: 300;

    @include mq(xs) {
      opacity: 0.5;
      flex-direction: row;
    }
  }

  .time-val,
  .separator {
    display: block;
    white-space: nowrap;
  }

  .time-val {
    mat-icon {
      display: inline-flex;
      vertical-align: middle;
      height: 16px;
      margin-right: 2px;
      margin-top: -2px;

      @include mq(xs, max) {
        margin-right: 2px;
        height: 14px;
      }
    }

    span:not(:last-child) {
      margin-right: 2px;
    }
  }

  .separator {
    padding: 0 var(--s-half);
  }

  //@include smallMainContainer {
  //  @include timeWrapperMinimalStyles;
  //}

  @include mq(xs, max) {
    @at-root .time-wrapper {
      margin-right: 0;
      @include timeWrapperMinimalStyles;

      @include isDoneForFirstLine {
        opacity: calc(var(--task-is-done-dim-opacity) + 0.1);
      }
    }
  }

  //@include verySmallMainContainer {
  //  flex-direction: row;
  //  &:after {
  //    display: none;
  //  }
  //  .time {
  //    flex-direction: row;
  //  }
  //  .separator {
  //    display: block;
  //  }
  //}
}

.additional-info {
  display: block;
  // required as sort of a clearfix (fixes padding issue)
  overflow: hidden;
}

.drag-over-msg {
  pointer-events: none;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  opacity: 1;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  display: flex;
  z-index: var(--z-drag-over-msg);
}

.check-done {
  opacity: 0.5;
  position: absolute;
  left: 50%;
  top: 50%;
  display: block;
  z-index: var(--z-check-done);
  transform: translate(-50%, -50%);
  transition: var(--transition-standard);
  border-radius: 50%;

  &:hover {
    opacity: 1 !important;

    mat-icon {
      animation: var(--transition-duration-m) success-btn-ani linear;
    }

    .check {
      display: none;
    }

    .undo {
      display: block;
    }
  }

  mat-icon {
    font-size: var(--task-icon-size);
    height: var(--task-icon-size);
    width: var(--task-icon-size);
    line-height: var(--task-icon-size);

    &.undo {
      display: none;
    }
  }
}

// SWIPE BLOCKS
.block-left,
.block-right {
  pointer-events: none;
  color: var(--palette-primary-contrast-500);
  position: absolute;
  bottom: 2px;
  top: 1px;
  width: 0;
  z-index: var(--z-swipe-block);
  transition: var(--transition-standard);
  border-radius: var(--task-border-radius);
  overflow: hidden;

  &.isActive {
    background-color: var(--c-accent);
  }

  mat-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scaleX(1);
  }
}

.block-left {
  left: 0;
}

.block-right {
  right: 0;
}

progress-bar {
  z-index: var(--z-progress-bar);
}

// BUTTON STYLES AND DRAG HANDLE
// -----------------------------
.toggle-sub-tasks-btn,
.first-line ::ng-deep .ico-btn,
.first-line .ico-btn {
  margin: 0 var(--task-button-spacer);
  padding: 0;
  z-index: var(--z-btn);
  @extend %standardTaskOpacityChange;

  &:hover {
    z-index: var(--z-btn-hover);
  }

  &:focus {
    outline: none;
  }
}

.first-line .menu-trigger {
  @include mq(xs) {
    opacity: 0;
  }
}

.first-line:hover .menu-trigger {
  @include standardTaskOpacityChange;
}

.delete-btn {
  mat-icon.delete-icon {
    color: var(--c-warn) !important;
  }
}

.drag-handle {
  margin-left: var(--s);
  margin-right: 0;
  width: 40px;
  height: 40px;
  min-width: 40px;
  z-index: var(--z-drag-handle);
  //display: flex;
  //justify-content: center;
  //align-items: center;
  position: relative;

  // fixes drag and drop on mobile
  touch-action: none;
  pointer-events: all;

  @include grabCursor();

  > * {
    pointer-events: none;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .type-ico-wrapper {
    width: 40px;
    height: 40px;
    min-width: 40px;
    opacity: var(--task-icon-default-opacity);
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;

    @include mq(xs, max) {
      display: none;
    }

    :host.isCurrent &::ng-deep mat-icon {
      transform: scale(1.4) !important;
      transform-origin: center center;
      opacity: 0.18;
    }
  }

  .drag-handle-ico {
    @include mq(xs) {
      opacity: 0.15;
      //.first-line:hover & {
      //opacity: 0.3;
      //}
    }

    :host.isCurrent & {
      opacity: 0;
    }
  }

  &:active {
    cursor: grabbing;
  }

  // hit area
  &:after {
    content: '';
    position: absolute;
    top: calc(-1 * var(--s-quarter));
    bottom: calc(-1 * var(--s-quarter));
    left: calc(-1 * var(--s-quarter));
    right: calc(-1 * var(--s-quarter));

    @include mq(xs) {
      top: calc(-1 * var(--s));
      bottom: calc(-1 * var(--s));
      left: calc(-1 * var(--s));
      right: calc(-1 * var(--s-half));
    }
  }
}

//
.play-icon-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--c-accent);

  height: var(--play-icon-size);
  margin-top: 1px;
  width: var(--play-icon-size);
  font-size: var(--play-icon-size);
  line-height: var(--play-icon-size);
  opacity: 1 !important;
  z-index: 3;
  pointer-events: none;

  @include mq(xs) {
    margin-top: 0;
    //left: 10px;
  }

  //.first-line:hover & {
  //  display: none;
  //}
}

.ico-btn {
  color: inherit;

  &.mat-accent {
    color: var(--c-accent);
  }
}

.toggle-sub-tasks-btn,
.first-line .ico-btn {
  margin: auto var(--task-button-spacer);
  padding: 0;
  z-index: var(--z-btn);

  @extend %standardTaskOpacityChange;

  &:hover {
    z-index: var(--z-btn-hover);
  }

  &:focus {
    outline: none;
  }
}

.toggle-sub-tasks-btn.toggle-sub-tasks-btn {
  position: absolute !important;
  // overwrite any lower values set elsewhere
  opacity: 1;
  transform: translateY(-50%);
  left: -24px;
  top: 50%;
  z-index: var(--z-toggle-sub-task-btn) !important;
  min-width: 0;
  min-height: 0;
  width: 32px !important;
  height: 32px !important;
  margin-top: -1px !important;
  transform-origin: left top;
  transition-property: all;
  box-shadow: var(--whiteframe-shadow-1dp);

  :host-context(.isNoTouchOnly) & {
    //opacity: 0;
  }

  :host:hover & {
    opacity: 1;
  }

  mat-icon {
    transition: var(--transition-standard);
    opacity: var(--task-icon-default-opacity);

    &.isHideDoneTasks {
      transform: rotate(-45deg);
    }
  }
}

.attachment-btn,
.show-additional-info-btn {
  mat-icon {
    transition: transform var(--transition-standard);
  }
}

.updated-icon {
  position: absolute;
  transform: scale(1.5) translate(-50%, -50%);
  transform-origin: top left;
  top: 50%;
  left: 50%;
}

.start-task-btn {
  :host-context(.isHidePlayBtn) & {
    display: none !important;
  }
}

// ANIMATIONS ETC
// --------------
@keyframes success-btn-ani {
  0% {
    transform: scale(0.5) rotate(-180deg);
  }
  50% {
    transform: scale(1) rotate(-90deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

.isPreventPointerEventsWhilePanning {
  * {
    user-select: none !important;
    -webkit-user-select: none !important; /* Safari */
    pointer-events: none !important;
    transition: none !important;
  }
}

.mini-badge {
  line-height: var(--mini-badge-size);
  width: var(--mini-badge-size);
  height: var(--mini-badge-size);
  font-size: 12px;
  position: absolute;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  text-align: center;
}

.parent-title {
  display: flex;
  align-items: center;
  padding-left: var(--s);
  padding-top: var(--s-half);
  margin-bottom: -2px;

  .title {
    font-size: 12px;
    text-overflow: ellipsis;
  }
}

.schedule-btn {
  position: relative;

  &.mat-warn mat-icon {
    color: var(--c-warn) !important;
  }
}

.repeat-date-badge,
.time-badge {
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  top: 50%;
  margin-top: 10px;
  font-size: 12px;
  z-index: 10;
  line-height: 1;
  text-align: center;
  padding: 1px 2px 0;
  border: 1px solid var(--theme-extra-border-color);
  border-radius: var(--card-border-radius);
  white-space: nowrap;
  background: #fff;

  // avoid affecting drag handle
  pointer-events: none;

  @include darkTheme() {
    border-color: var(--theme-extra-border-color);
    background: var(--theme-bg-lighter);
  }

  ::ng-deep span {
    font-size: 10px;
  }
}

@media (min-width: #{$layout-sm}) and (max-width: #{$layout-xl - 1px}) {
  .closeBtn {
    min-width: 40px;
    margin-right: -29px !important;
  }
}

.tag-ico {
  font-size: 20px;
  line-height: 20px;
  height: 20px;
  width: 20px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
}
