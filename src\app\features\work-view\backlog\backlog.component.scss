@import './../../../../_common';

:host {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;

  task-list ::ng-deep > .task-list-inner {
    min-height: 50px;
  }

  background: var(--palette-primary-200);

  &:before {
    position: absolute;
    content: '';
    background: var(--color-overlay-dark-20);
    width: 100%;
    top: 0;
    left: 0;
    height: 100%;
    pointer-events: none;
  }

  &:after {
    pointer-events: none;
    position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 44;
    box-shadow:
      inset 0 10px 20px rgba(0, 0, 0, 0.19),
      inset 0 6px 6px rgba(0, 0, 0, 0.23);
  }
}

body.isDarkTheme :host {
  background: var(--palette-primary-900);

  &:before {
    background: var(--color-overlay-dark-50);
  }
}

.wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding-top: var(--s5);
  overflow-x: hidden;
  @include scrollYImportant();
}
