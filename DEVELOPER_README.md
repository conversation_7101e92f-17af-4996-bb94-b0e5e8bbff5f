# Super Productivity - Developer Documentation

## Project Overview
Super Productivity is a feature-rich task management and time tracking application with Jira/GitHub integration, built with Angular and Electron.

**Version**: 14.0.2  
**License**: MIT  
**Author**: <PERSON> <<EMAIL>>  
**Website**: https://super-productivity.com  
**Repository**: https://github.com/johannesjo/super-productivity

## Core Features
- Task management with projects and tags
- Time tracking with detailed reports
- <PERSON>ra and GitHub integration
- Calendar synchronization
- Cross-platform support (Windows, Linux, Mac, Android)
- Offline functionality
- Dark/Light theme support

## Project Structure

### Main Directories
```
.
├── .angular/            # Angular build cache
├── .devcontainer/       # Dev container configuration
├── .github/            # GitHub workflows and templates
├── android/            # Android platform specific code
├── build/              # Build configurations
├── docs/               # Documentation
├── e2e/                # End-to-end tests
├── electron/           # Electron main process code
├── node_modules/       # Node.js dependencies
├── packages/           # Internal packages/modules
├── src/                # Main application source code
├── tools/              # Build and utility scripts
```

### Core Source Code (src/)
```
src/
├── app/                # Angular application code
│   ├── core/           # Core services and utilities
│   ├── features/       # Feature modules
│   ├── pages/          # Page components
│   ├── root-store/     # NgRx state management
│   ├── ui/             # UI components
│   └── util/           # Utility functions
├── assets/             # Static assets (images, fonts, etc.)
├── environments/       # Environment configurations
└── styles/             # Global styles and themes
```

## Development Setup

### Prerequisites
- Node.js (version specified in .nvmrc)
- npm/yarn
- Angular CLI
- Electron (for desktop development)
- Android SDK (for Android development)

### Installation
1. Clone the repository:
   ```bash
   git clone https://github.com/johannesjo/super-productivity.git
   cd super-productivity
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Application
- **Web development server**:
  ```bash
   npm run startFrontend
   ```
  
- **Electron development**:
  ```bash
   npm start
   ```

### Building the Application
- **Production build (Electron)**:
  ```bash
   npm run buildAllElectron:prod
   ```

- **Production build (Web)**:
  ```bash
   npm run buildFrontend:prodWeb
   ```

- **Android build**:
  ```bash
   npm run dist:android
   ```

## Core Modules

### State Management (NgRx)
The application uses NgRx for state management. The main store configuration is located in:
```
src/app/root-store/
```

### Electron Integration
Electron-specific code is located in:
```
electron/
```

Key files:
- `main.js` - Electron main process entry point
- `electron-builder.yaml` - Electron build configuration

### Plugins System
The application supports plugins through:
```
packages/plugin-api/
packages/plugin-dev/
```

## Testing
- **Unit tests**:
  ```bash
   npm test
   ```

- **End-to-end tests**:
  ```bash
   npm run e2e
   ```

- **Timezone tests** (verifies timezone handling):
  ```bash
   npm run test:tz:all
   ```

## Contribution Guidelines
Please refer to [CONTRIBUTING.md](CONTRIBUTING.md) for detailed contribution guidelines.

## Additional Resources
- [CHANGELOG.md](CHANGELOG.md) - Release history
- [SECURITY.md](SECURITY.md) - Security policy
- [CLAUSE.md](CLAUSE.md) - Contributor License Agreement