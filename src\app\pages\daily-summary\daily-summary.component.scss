@import '../../../common';

$summary-point-inner-margin: var(--s);

:host {
  evaluation-sheet {
    display: block;
  }

  ::ng-deep {
    .mat-mdc-tab .mdc-tab__text-label {
      //color: var(--theme-text-color);
      //font-weight: 300;
    }
  }

  ::ng-deep .mat-mdc-tab-list {
    border-bottom: 1px solid var(--theme-separator-color);

    border-color: var(--theme-separator-color);
  }

  ::ng-deep .mat-mdc-tab-labels {
    justify-content: center;
    align-items: center;
  }

  ::ng-deep .mat-mdc-tab {
    flex-grow: 0 !important;
    user-select: none;

    @include mq(xs) {
      min-width: 200px;
    }
  }

  // in case we every want to go sticky
  //::ng-deep .mat-tab-header {
  //  top: 0;
  //  z-index: 1000;
  //  position: sticky;
  //  position: -webkit-sticky; /* macOS/iOS Safari */
  //}
}

.page-wrapper {
  //display: flex;
  //align-items: center;
  //flex-direction: column;
  text-align: center;
}

.tab-inner {
  margin: auto;
  max-width: var(--component-max-width);
  padding: var(--s);

  &.full-width {
    max-width: none;
  }
}

.back-btn {
  margin-top: var(--s2);
}

.done-headline {
  margin-top: var(--s4);
  margin-bottom: calc(var(--s3) - var(--s));
}

.daily-summary-summary {
  margin-bottom: var(--s5);
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  p {
    margin: 5px;
  }
}

.summary-point {
  vertical-align: center;
  display: inline-block;
  align-items: center;
  text-align: center;
  margin: $summary-point-inner-margin;
  //flex-grow: 1;
  flex-basis: 160px;
  position: relative;
}

.summary-val {
  font-weight: bold;
}

.tabs {
  @include mq(xs) {
    margin: 0;
  }
}

.simple-counter-summary {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: var(--s6);
  margin-top: calc(-1 * var(--s));

  simple-counter-summary-item {
    flex-basis: 140px;
  }
}

.daily-summary-actions {
  margin-top: 30px;

  button {
    margin: 0 var(--s-half);
  }
}

.success-animation-wrapper {
  $this-ani-style: 1s linear infinite;
  $this-ico-size: 128px;
  clear: both;
  display: block;
  position: fixed;
  z-index: 100;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  backface-visibility: hidden;

  mat-icon {
    width: $this-ico-size;
    height: $this-ico-size;
    display: block;
    font-size: $this-ico-size;
    color: var(--yellow);
    //transform-origin: center center;
    animation: success-animation-sun #{$this-ani-style};
    backface-visibility: hidden;
  }

  .unicorn-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .unicorn {
    transform-origin: center center;
    animation: success-animation-unicorn #{$this-ani-style};
  }
}

.tomorrows-note {
  display: inline-block;
  width: 500px;
  max-width: 100%;
}

.day-end-note {
  margin-bottom: 48px;
  //margin-top: -8px;

  &::ng-deep inline-markdown {
    max-width: 400px;
    text-align: left;
    margin: auto;

    &.isFocused {
      border-color: transparent;
    }

    .markdown-wrapper {
      min-height: 60px;
      overflow: visible !important;
      transition: var(--transition-standard);

      //background: var(--theme-bg);
      //box-shadow: var(--whiteframe-shadow-1dp);
      border: 1px solid var(--theme-extra-border-color);

      &,
      > * {
        border-radius: calc(var(--card-border-radius) * 2);
      }
    }

    &.isFocused .markdown-wrapper {
      box-shadow: var(--whiteframe-shadow-3dp) !important;
      border-color: transparent;
    }

    .markdown-unparsed,
    .markdown-parsed {
      color: inherit;
      background-color: inherit;
    }
  }
}

@keyframes success-animation-sun {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 1;
  }
  50% {
    opacity: 1;
    transform: scale(5) rotate(0deg);
  }
  75% {
    transform: scale(10) rotate(90deg);
    opacity: 0.4;
  }
  100% {
    transform: scale(12) rotate(180deg);
    opacity: 0;
  }
}

@keyframes success-animation-unicorn {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  25% {
    opacity: 1;
  }
  50% {
    opacity: 1;
  }
  55% {
    transform: scale(2);
  }
  75% {
    transform: scale(3);
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
