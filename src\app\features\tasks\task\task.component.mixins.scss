// Import only to access the material design color variables
@import '../../../../common';

@mixin isDone($direct-parent-only-sel: false) {
  @if $direct-parent-only-sel {
    .inner-wrapper.isDone > #{$direct-parent-only-sel} > & {
      @content;
    }
  } @else {
    .inner-wrapper.isDone & {
      @content;
    }
  }
}

@mixin isDoneForFirstLine($direct-parent-only-sel: false) {
  :host.isDone > .inner-wrapper > .first-line & {
    @content;
  }
}

@mixin standardTaskOpacityChange {
  opacity: var(--task-icon-default-opacity);
  transition: transform var(--transition-fast);

  .isCurrent &,
  &:hover,
  &:focus {
    opacity: 1;
  }
}

%standardTaskOpacityChange {
  @include standardTaskOpacityChange;
}

%standardTaskZoom {
  &:hover,
  &:focus {
    transform: scale(1.2);
  }
}
