@import '../../../../common';
@import '../task/task.component.mixins';

:host {
  position: relative;
  z-index: 1;
  display: block;
  padding-bottom: var(--s);

  task-list {
    padding: 0;
    margin-bottom: 0;
  }
}

progress-bar {
  position: absolute !important;
  bottom: 0;
}

.wrapper {
  transition: var(--transition-standard);

  @media (max-width: #{$layout-xs}) {
    margin: var(--s-half);
    margin-bottom: var(--s);
  }
}

.task-title-wrapper {
  min-height: var(--bar-height);
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  padding: 0 var(--s);
  // NOTE make space for close button
  padding-right: 50px;
  z-index: 44;
  border-bottom: 2px solid var(--c-primary) !important;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;

  background: var(--theme-card-bg);
  //border-bottom: 1px solid var(--c-primary);

  @include mq(xs) {
    padding: 0 var(--s);
  }

  ::ng-deep textarea {
    border: 2px solid red;
    font-weight: 600 !important;
  }

  task-title {
    padding-top: 4px;
    padding-bottom: 4px;
    margin-top: 0;

    color: var(--theme-text-color);
  }
}

.task-title.task-title {
  font-weight: bold;
  transform-origin: top center;
  padding: var(--s);
  font-size: 17px;
  margin: 0;
  flex-grow: 1;
  margin-top: 4px;
  line-height: 1.4;
  min-height: 40px;

  &:focus {
    transform: scale(1);
  }

  &[contenteditable='true']:empty:before {
    content: attr(placeholder);
    pointer-events: none;
    display: block;
    opacity: 0.4;
  }
}

.created,
.date-info {
  padding: var(--s);
  opacity: 0.5;
  text-align: center;
  color: var(--theme-text-color-muted);
}

.drag-over-msg {
  pointer-events: none;
  position: absolute;
  top: calc(-1 * var(--s));
  right: 0;
  left: 0;
  bottom: 0;
  opacity: 1;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  display: flex;
  z-index: 2;
}
