:host {
  --split-line-height: 2px;

  z-index: 100;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  //cursor: row-resize;

  .handle {
    position: absolute;
    width: 100%;
    height: var(--split-line-height);
    top: calc(-0.5 * var(--split-line-height));
    display: block;
    z-index: 5;

    &:after {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      //box-shadow: 0 -3px 5px 0 var(--color-overlay-dark-40);
      pointer-events: none;
    }
  }

  .handle.isHideShadow button {
    box-shadow: none !important;
  }

  button {
    position: absolute;
    z-index: 2;
    transform: translate(-50%, -50%);
    left: 50%;
    cursor: grab;
    transform-origin: left center;

    //$this-size: 56px;
    //transform: translate(-50%, -100%);
    //height: $this-size/2;
    //width: $this-size;
    //border-bottom: 0;
    //border-radius: $this-size $this-size 0 0;
    //top: $splitLineHeight/2;

    &:active {
      cursor: grabbing;
    }

    &.isAnimate {
      animation: var(--transition-duration-m) splitWiggleAni var(--ani-standard-timing);
    }
  }

  .counter {
    position: absolute;
    top: -25px;
    left: -27px;
    width: 30px;
    text-align: center;
  }
}

@keyframes splitWiggleAni {
  0% {
    transform: scale(1) translate(-50%, -50%);
  }
  50% {
    transform: scale(1.2) translate(-50%, -50%);
  }
  100% {
    transform: scale(1) translate(-50%, -50%);
  }
}
