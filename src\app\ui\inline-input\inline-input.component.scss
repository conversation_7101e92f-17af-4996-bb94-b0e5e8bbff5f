@import '../../../_common';

:host {
  display: inline-block;
  position: relative;

  &:hover,
  &.isFocused {
    z-index: 11;
  }
}

:host-context(td) {
  display: block;

  .inline-input-wrapper {
    display: block;
  }
}

.inline-input-wrapper {
  display: inline-block;
  position: relative;
  cursor: pointer;
  width: 100%;
}

.value-wrapper {
  display: inline-block;
  user-select: none;
  -webkit-user-select: none; /* Safari */
  position: relative;
}

input {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scaleX(0);
  opacity: 0;
  //transition: var(--transition-fast);
  background: var(--theme-card-bg);
  color: var(--theme-text-color);
  padding: var(--s-half) var(--s-half);
  text-align: center;
  font-size: 14px;
  min-height: 100%;

  @include inlineEditElevation();
  z-index: 999999;

  &.duration-input {
    width: 100%;
    min-width: 70px;
  }

  :host:hover &,
  &:focus {
    opacity: 1;
    outline: none;
    transform: translate(-50%, -50%) scaleX(1);
    visibility: visible;
  }
}
