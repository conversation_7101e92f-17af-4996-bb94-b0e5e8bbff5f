{"name": "superProductivity", "version": "14.0.2", "description": "ToDo list and Time Tracking", "keywords": ["ToDo", "Task Management", "<PERSON><PERSON>", "GitHub", "Time Tracking"], "homepage": "https://super-productivity.com", "repository": {"type": "git", "url": "git://github.com/johannesjo/super-productivity.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>> (http://super-productivity.com)", "husky": {"hooks": {"pre-commit": "pretty-quick --staged && npm run lint", "pre-push": "npm run lint && npm run test"}}, "main": "./electron/main.js", "scripts": {"assemble:android:prod": "cd android && ./gradlew assembleRelease && cd ..", "assemble:android:stage": "cd android && ./gradlew assembleDebug && cd ..", "prebuild": "node ./tools/git-version.js && npm run build:packages", "build": "npm run buildAllElectron:noTests:prod", "build:packages": "node ./packages/build-packages.js", "buildAllElectron:noTests:prod": "npm run lint && npm run buildFrontend:prod:es6 && npm run electron:build", "buildAllElectron:prod": "npm run preCheck && npm run buildFrontend:prod:es6 && npm run electron:build", "buildAllElectron:stage": "npm run preCheck && npm run buildFrontend:stage:es6 && npm run electron:build", "buildFrontend:prod:es6": "npm run prebuild && cross-env BROWSERSLIST_ENV='modern' ng build --configuration production && npm run removeWOFF1", "buildFrontend:prod:watch": "npm run prebuild && ng build --configuration production --watch", "buildFrontend:prodWeb": "npm run prebuild && ng build  --configuration productionWeb", "buildFrontend:stage:es6": "npm run prebuild && cross-env BROWSERSLIST_ENV='modern' ng build --configuration stage && npm run removeWOFF1", "buildFrontend:stageWeb": "npm run prebuild && ng build --configuration stageWeb", "buildFrontend:stageWeb:unminified": "npm run prebuild && ng build --configuration stageWeb --optimization=false --aot=false", "dist": "npm run buildAllElectron:prod && electron-builder", "dist:android": "npm run buildFrontend:stageWeb:unminified && npm run sync:android && npm run assemble:android:stage && echo 'Staging Android APK generated at android/app/build/outputs/apk/debug/'", "dist:android:prod": "npm run buildFrontend:prodWeb && npm run sync:android && npm run assemble:android:prod && echo 'Production Android APK generated at android/app/build/outputs/apk/release/'", "dist:linuxAndWin": "npm run buildAllElectron:prod && electron-builder --linux --win", "dist:mac:dl": "cp tools/mac-profiles/dl.provisionprofile embedded.provisionprofile && electron-builder --mac", "dist:mac:mas": "cp tools/mac-profiles/mas.provisionprofile embedded.provisionprofile; electron-builder --mac mas --config=build/electron-builder.mas.yaml", "dist:mac:mas:buildOnly": "electron-builder --config=build/electron-builder.mas.yaml", "dist:mac:mas:dev": "cp tools/mac-profiles/mas-dev.provisionprofile embedded.provisionprofile; electron-builder --mac mas-dev --config=build/electron-builder.mas-dev.yaml", "dist:mac:mas:x64": "cp tools/mac-profiles/mas.provisionprofile embedded.provisionprofile; electron-builder --mac mas --config=build/electron-builder.mas.yaml --x64", "dist:only": "electron-builder", "dist:win": "npm run buildAllElectron:noTests:prod && electron-builder --win", "dist:win:appx": "npm run buildAllElectron:prod && electron-builder --win --config=build/electron-builder.appx.yaml", "dist:win:only": "electron-builder --win", "dist:win:store": "git pull && npm run && copy electron-builder.win-store.yaml electron-builder.yaml && npm run dist:win && git checkout electron-builder.yaml || git checkout electron-builder.yaml", "droid": "npm run buildFrontend:stageWeb:unminified && npx cap sync", "e2e": "cross-env TZ='Europe/Berlin' DETECT_CHROMEDRIVER_VERSION=true SKIP_POST_INSTALL=true npm i -D chromedriver --legacy-peer-deps && tsc --project e2e/tsconfig.e2e.json && start-server-and-test 'ng serve --no-live-reload' http://localhost:4200 'nightwatch -c ./e2e/nightwatch.conf.js --suiteRetries 1 --retries 1'", "e2e:tag": "killall chromedriver; rm -R ./.tmp/out-tsc; tsc --project e2e/tsconfig.e2e.json --outDir ./.tmp/out-tsc && nightwatch -c ./e2e/nightwatch.conf.js --suiteRetries 0 --retries 0 --tag ", "electron": "NODE_ENV=PROD electron .", "electron:build": "tsc -p electron/tsconfig.electron.json", "electron:watch": "tsc -p electron/tsconfig.electron.json --watch", "electronBuilderOnly": "electron-builder", "empty": "echo 'EMPTY YEAH'", "install:android": "adb install -r android/app/build/outputs/apk/fdroid/debug/app-fdroid-debug.apk && echo 'Staging APK installed successfully.'", "install:android:prod": "adb install -r android/app/build/outputs/apk/fdroid/release/app-fdroid-release.apk && echo 'Production APK installed successfully.'", "int": "node ./tools/extract-i18n-single.js", "int:clean": "ngx-translate-extract --input ./src --output ./src/assets/i18n/*.json --clean --sort --format namespaced-json --marker _", "int:find": "ngx-translate-extract --input ./src --output ./src/assets/i18n/*.json  --sort --format namespaced-json --marker _", "int:test": "node ./tools/test-lng-files.js", "int:watch": "node ./tools/extract-i18n-watch.js", "lint": "npm run lint:ts && npm run lint:scss", "lint:ci": "npm run lint:ts && npm run lint:scss:ci", "lint:ts": "ng lint", "lint:scss": "stylelint \"**/*.scss\"", "lint:scss:ci": "npm run lint:scss -- --custom-formatter @csstools/stylelint-formatter-github", "localInstall": "sudo echo 'Starting local install' && rm -Rf ./.tmp/angular-dist/ && rm -Rf ./.tmp/app-builds/ && npm run buildAllElectron:stage && electron-builder --linux deb && sudo dpkg -i .tmp/app-builds/superProductivity*.deb", "localInstall:mac": "sudo echo 'Starting local install MAC. Don`t forget APPLEID & APPLEIDPASS !!' && npm run buildAllElectron:noTests:prod && sudo echo '' && electron-builder && sudo cp -rf .tmp/app-builds/mac/superProductivity.app/ /Applications/superProductivity.app", "localInstall:prod": "sudo echo 'Starting local install PROD' && rm -Rf ./.tmp/angular-dist/ && rm -Rf ./.tmp/app-builds/ && npm run buildAllElectron:prod && electron-builder --linux deb && sudo dpkg -i .tmp/app-builds/superProductivity*.deb", "localInstall:quick": "sudo echo 'Starting local install QUICK' && rm -Rf ./.tmp/angular-dist/ && rm -Rf ./.tmp/app-builds/ && npm run buildFrontend:stage:es6 && npm run electron:build && electron-builder --linux deb && sudo dpkg -i .tmp/app-builds/superProductivity*.deb", "localInstall:test:snap": "sudo echo 'Starting local install SNAP QUICK' && rm -Rf ./.tmp/angular-dist/ && rm -Rf ./.tmp/app-builds/ && npm run buildFrontend:stage:es6 && npm run electron:build && electron-builder --linux snap && sudo snap install --dangerous .tmp/app-builds/superProductivity*.snap", "ng": "ng", "pack": "electron-builder --dir", "preCheck": "npm run lint && npm run test & npm run int:test && npm run e2e", "release": "npm run release.changelog && npm run dist", "release.changelog": "conventional-changelog -i CHANGELOG.md -s -p angular", "removeWOFF1": "node ./tools/remove-woff.js", "serveProd": "ng serve --configuration production", "start": "npm run electron:build && cross-env NODE_ENV=DEV electron .", "startFrontend": "ng serve", "sync:android": "npx cap sync android", "stats": "ng build --configuration production --source-map --stats-json && npx esbuild-visualizer --metadata .tmp/angular-dist/stats.json && xdg-open stats.html", "test": "cross-env TZ='Europe/Berlin' ng test --watch=false && npm run test:tz:ci", "test:watch": "cross-env TZ='Europe/Berlin' ng test --browsers ChromeHeadless", "test:tz:ci": "npm run test:tz:la", "test:tz:la": "cross-env TZ='America/Los_Angeles' ng test --watch=false --include='**/*.spec.ts'", "test:tz:tokyo": "cross-env TZ='Asia/Tokyo' ng test --watch=false --include='**/*.spec.ts'", "test:tz:sydney": "cross-env TZ='Australia/Sydney' ng test --watch=false --include='**/*.spec.ts'", "test:tz:utc": "cross-env TZ='UTC' ng test --watch=false --include='**/*.spec.ts'", "test:tz:all": "npm run test && npm run test:tz:la && npm run test:tz:tokyo && npm run test:tz:sydney && npm run test:tz:utc", "test:date-utils": "cross-env TZ='Europe/Berlin' ng test --watch=false --include='src/app/util/**/format-*.spec.ts' --include='src/app/util/**/date-*.spec.ts'", "test:date-utils:tz": "npm run test:date-utils && cross-env TZ='America/Los_Angeles' npm run test:date-utils && cross-env TZ='Asia/Tokyo' npm run test:date-utils", "version": "npm run prebuild && npm run release.changelog && node ./tools/bump-android-version.js && git add -A", "prepare": "ts-patch install && npm run plugin-api:build", "prettier": "pretty-quick", "clean:translations": "node ./tools/clean-translations.js", "plugin-api:build": "cd packages/plugin-api && npm run build", "plugin-api:build:watch": "cd packages/plugin-api && npm run build:watch", "plugins:build": "cd packages/plugin-dev && npm run build:all"}, "resolutions": {"sass": "1.32.6"}, "dependencies": {"electron-dl": "^3.5.2", "electron-localshortcut": "^3.2.1", "electron-log": "^5.4.1", "electron-window-state": "^5.0.3", "fs-extra": "^11.3.0", "node-fetch": "^2.7.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.11", "@angular-eslint/builder": "^19.3.0", "@angular-eslint/eslint-plugin": "^19.3.0", "@angular-eslint/eslint-plugin-template": "^19.3.0", "@angular-eslint/schematics": "^20.1.1", "@angular-eslint/template-parser": "^19.3.0", "@angular/animations": "^19.2.10", "@angular/cdk": "^19.2.8", "@angular/cli": "^19.2.11", "@angular/common": "^19.2.10", "@angular/compiler": "^19.2.10", "@angular/compiler-cli": "^19.2.10", "@angular/core": "^19.2.10", "@angular/forms": "^19.2.10", "@angular/language-service": "^19.2.10", "@angular/material": "^19.2.8", "@angular/platform-browser": "^19.2.10", "@angular/platform-browser-dynamic": "^19.2.10", "@angular/platform-server": "^19.2.10", "@angular/router": "^19.2.10", "@angular/service-worker": "^19.2.10", "@capacitor/android": "^7.3.0", "@capacitor/app": "^7.0.1", "@capacitor/cli": "^7.3.0", "@capacitor/core": "^7.3.0", "@capacitor/filesystem": "^7.1.1", "@capacitor/local-notifications": "^7.0.1", "@capawesome/capacitor-android-dark-mode-support": "^7.0.0", "@capawesome/capacitor-background-task": "^7.0.1", "@csstools/stylelint-formatter-github": "^1.0.0", "@electron/notarize": "^3.0.1", "@fontsource/roboto": "^4.5.8", "@nextcloud/cdav-library": "^1.5.3", "@ngrx/effects": "^19.0.0", "@ngrx/entity": "^19.0.0", "@ngrx/schematics": "^19.0.0", "@ngrx/store": "19.0.0", "@ngrx/store-devtools": "^19.0.0", "@ngx-formly/core": "6.3.12", "@ngx-formly/material": "6.3.12", "@ngx-translate/core": "^16.0.3", "@ngx-translate/http-loader": "^16.0.1", "@schematics/angular": "^18.0.4", "@types/electron-localshortcut": "^3.1.3", "@types/file-saver": "^2.0.5", "@types/hammerjs": "^2.0.45", "@types/jasmine": "^3.10.2", "@types/jasminewd2": "~2.0.13", "@types/node": "20.12.4", "@types/node-fetch": "^2.6.6", "@types/object-path": "^0.11.4", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "7.18.0", "@typescript-eslint/types": "^8.17.0", "@typescript-eslint/utils": "^8.35.1", "angular-material-css-vars": "^8.0.0", "angular-mentions": "^1.5.0", "canvas-confetti": "^1.9.3", "chai": "^5.1.2", "chart.js": "^4.4.7", "chromedriver": "^137.0.4", "chrono-node": "^2.8.0", "clipboard": "^2.0.11", "conventional-changelog-cli": "^5.0.0", "core-js": "^3.39.0", "cross-env": "^7.0.3", "detect-it": "^4.0.1", "electron": "^36.4.0", "electron-builder": "^26.0.12", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "50.6.0", "eslint-plugin-prefer-arrow": "1.2.3", "eslint-plugin-prettier": "^5.2.1", "fflate": "^0.8.2", "file-saver": "^2.0.5", "glob": "^9.3.5", "hammerjs": "^2.0.8", "helpful-decorators": "^2.1.0", "husky": "^4.2.5", "ical.js": "^2.1.0", "idb": "^8.0.3", "jasmine-core": "^4.0.0", "jasmine-marbles": "^0.8.4", "jasmine-spec-reporter": "~7.0.0", "jira2md": "git+https://github.com/johannesjo/J2M.git", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^1.6.0", "marked": "^12.0.2", "nanoid": "^5.1.5", "new-github-issue-url": "^1.1.0", "ng2-charts": "^8.0.0", "ngx-markdown": "^19.0.0", "nightwatch": "^3.12.1", "prettier": "^3.5.1", "pretty-quick": "^4.1.1", "query-string": "^7.1.3", "rxjs": "^6.6.7", "shepherd.js": "^11.2.0", "spark-md5": "^3.0.2", "stacktrace-js": "^2.0.2", "start-server-and-test": "^2.0.9", "stylelint": "^16.18.0", "stylelint-config-recommended-scss": "^14.1.0", "ts-node": "~10.9.2", "ts-patch": "^3.3.0", "tslib": "^2.7.0", "typescript": "~5.8.3", "typia": "^9.1.0"}, "overrides": {"angular-mentions": {"@angular/common": "$@angular/common"}, "ngx-markdown": {"marked": "12.0.2"}}, "optionalDependencies": {"@lmdb/lmdb-darwin-x64": "^3.2.0", "@lmdb/lmdb-linux-x64": "^3.2.0", "@lmdb/lmdb-win32-x64": "^3.2.0", "@rollup/rollup-darwin-x64": "4.27.4", "@rollup/rollup-linux-x64-gnu": "4.27.4", "@rollup/rollup-win32-x64-msvc": "4.44.1"}, "workspaces": ["packages/*"], "publish": [{"provider": "github", "repo": "super-productivity", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}