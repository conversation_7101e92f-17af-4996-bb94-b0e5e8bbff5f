{"root": true, "ignorePatterns": ["app-builds/**/*", "dist/**", "node_modules/**/*", "src/app/t.const.ts", "src/assets/bundled-plugins/**/*"], "overrides": [{"files": ["*.ts"], "parser": "@typescript-eslint/parser", "excludedFiles": [], "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "createDefaultProgram": true}, "extends": ["plugin:@typescript-eslint/recommended", "plugin:@angular-eslint/recommended", "plugin:prettier/recommended"], "plugins": ["@typescript-eslint", "prettier", "prefer-arrow"], "rules": {"@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/ban-ts-comment": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/explicit-module-boundary-types": 0, "prettier/prettier": "error", "@angular-eslint/component-selector": ["off"], "@typescript-eslint/no-unused-vars": ["error", {"args": "none"}], "@typescript-eslint/explicit-function-return-type": ["error", {"allowExpressions": true, "allowTypedFunctionExpressions": true, "allowHigherOrderFunctions": true, "allowDirectConstAssertionInArrowFunctions": true, "allowConciseArrowFunctionExpressionsStartingWithVoid": true}], "@typescript-eslint/explicit-member-accessibility": ["off", {"accessibility": "explicit"}], "arrow-parens": ["off", "always"], "import/order": "off", "@typescript-eslint/member-ordering": "off", "@typescript-eslint/no-inferrable-types": "off", "no-underscore-dangle": "off", "arrow-body-style": "off", "@angular-eslint/no-input-rename": "off", "@typescript-eslint/naming-convention": ["error", {"selector": "default", "format": ["camelCase", "snake_case", "UPPER_CASE", "PascalCase"], "leadingUnderscore": "allowSingleOrDouble", "trailingUnderscore": "allow", "filter": {"regex": "(should)|@tags", "match": false}}, {"selector": "variable", "format": ["camelCase", "snake_case", "UPPER_CASE", "PascalCase"], "leadingUnderscore": "allowSingleOrDouble", "trailingUnderscore": "allow"}, {"selector": "enum", "format": ["PascalCase", "UPPER_CASE"]}, {"selector": "typeLike", "format": ["PascalCase"]}], "prefer-const": "error", "@typescript-eslint/no-unused-expressions": "error", "@typescript-eslint/no-empty-interface": "error", "max-len": ["error", {"ignorePattern": "^import \\{.+;$", "ignoreRegExpLiterals": true, "ignoreStrings": true, "ignoreUrls": true, "code": 150}], "id-blacklist": "error", "@typescript-eslint/member-delimiter-style": "error", "no-shadow": "off", "@typescript-eslint/no-shadow": ["error"], "comma-dangle": ["error", "always-multiline"], "no-mixed-operators": "error", "prefer-arrow/prefer-arrow-functions": "error", "@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "", "style": "camelCase"}], "@typescript-eslint/ban-types": "error"}}, {"files": ["*.html"], "extends": ["plugin:@typescript-eslint/recommended", "plugin:@angular-eslint/template/recommended", "prettier"], "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "processor": "@angular-eslint/template/extract-inline-html", "plugins": ["prettier"], "rules": {"@angular-eslint/template/no-negated-async": "off", "prettier/prettier": "error"}}]}