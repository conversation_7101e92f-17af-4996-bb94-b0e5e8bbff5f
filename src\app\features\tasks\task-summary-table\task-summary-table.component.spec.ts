// import {async, ComponentFixture, TestBed} from '@angular/core/testing';
//
// import {TaskSummaryTableComponent} from './task-summary-table.component';
//
// describe('TaskSummaryTableComponent', () => {
//   let component: TaskSummaryTableComponent;
//   let fixture: ComponentFixture<TaskSummaryTableComponent>;
//
//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [TaskSummaryTableComponent]
//     })
//       .compileComponents();
//   }));
//
//   beforeEach(() => {
//     fixture = TestBed.createComponent(TaskSummaryTableComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
