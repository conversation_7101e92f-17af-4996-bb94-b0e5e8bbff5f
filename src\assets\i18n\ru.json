{"ANDROID": {"NOTIFICATIONS": {"SYNC_CONFLICT_MSG": "Проверьте и решите, что делать", "SYNC_CONFLICT_TITLE": "Конфликт синхронизации"}, "PERMANENT_NOTIFICATION_MSGS": {"INITIAL": "Приложение работает в фоновом режиме, чтобы обеспечить синхронизацию, если она включена", "NO_ACTIVE_TASKS": "Нет активных задач", "SYNCING": "Синхронизация"}}, "APP": {"B_INSTALL": {"IGNORE": "Игнорировать", "INSTALL": "Установить", "MSG": "Хотите установить Super Productivity в качестве PWA?"}, "B_OFFLINE": "Вы отключены от интернета. Синхронизация и запрос данных поставщика проблемы не будут работать.", "UPDATE_MAIN_MODEL": "Super Productivity получил серьезное обновление! Требуются некоторые миграции для ваших данных. Обратите внимание, что это делает ваши данные несовместимыми с более старыми версиями приложения.", "UPDATE_MAIN_MODEL_NO_UPDATE": "Не выбрано обновление модели. Обратите внимание, что вам нужно обновиться до последней версии, если вы не хотите выполнять обновление модели.", "UPDATE_WEB_APP": "Доступна новая версия. Загрузить новую версию?"}, "BL": {"NO_TASKS": "В настоящее время нет задач в вашем списке"}, "CONFIRM": {"AUTO_FIX": "Кажется, ваши данные повреждены. Хотите попробовать исправить это автоматически? Это может привести к частичной потере данных.", "RELOAD_AFTER_IDB_ERROR": "Невозможно получить доступ к базе данных. Возможные причины - обновление приложения в фоновом режиме или нехватка места на диске. Нажмите OK, чтобы перезагрузить приложение (на некоторых платформах может потребоваться перезапуск приложения вручную).", "RESTORE_FILE_BACKUP": "Данных не найдено, но есть резервные копии, доступные в \"{{dir}}\". Вы хотите восстановить последнюю резервную копию с {{from}}?", "RESTORE_FILE_BACKUP_ANDROID": "Данных не найдено, но есть резервная копия. Вы хотите её загрузить?", "RESTORE_STRAY_BACKUP": "Во время последней синхронизации могла произойти ошибка. Вы хотите восстановить последнюю резервную копию?"}, "DATETIME_SCHEDULE": {"LATER_TODAY": "Позже сегодня", "NEXT_WEEK": "На след. неделе", "PLACEHOLDER": "Пожалуйста, выберите дату", "PRESS_ENTER_AGAIN": "Нажмите Enter еще раз, чтобы сохранить", "TOMORROW": "Завтра"}, "F": {"ATTACHMENT": {"DIALOG_EDIT": {"ADD_ATTACHMENT": "Добавить вложение", "EDIT_ATTACHMENT": "Редактировать вложение", "LABELS": {"FILE": "Путь к файлу", "IMG": "Изображение", "LINK": "Ссылка"}, "SELECT_TYPE": "Выберите тип", "TYPES": {"FILE": "Файл (открывается в системном приложении по умолчанию)", "IMG": "Изображение (отображается в виде эскиза)", "LINK": "Ссылка (открывается в браузере)"}}}, "BOARDS": {"DEFAULT": {"DONE": "Готово", "EISENHAUER_MATRIX": "Матрица Эйзенхауэра", "IMPORTANT": "Важно", "IN_PROGRESS": "В процессе", "KANBAN": "Ка<PERSON><PERSON><PERSON>н", "NOT_URGENT_IMPORTANT": "Не срочно и важно", "NOT_URGENT_NOT_IMPORTANT": "Не срочно и не важно", "TO_DO": "Сделать", "URGENT": "Срочно", "URGENT_IMPORTANT": "Срочно и важно", "URGENT_NOT_IMPORTANT": "Срочно и не важно"}, "FORM": {"ADD_NEW_PANEL": "Добавить новую панель", "BACKLOG_TASK_FILTER_ALL": "Все", "BACKLOG_TASK_FILTER_NO_BACKLOG": "Исключать", "BACKLOG_TASK_FILTER_ONLY_BACKLOG": "Только бэклог", "BACKLOG_TASK_FILTER_TYPE": "Задачи бэклога", "COLUMNS": "Столбцы", "TAGS_EXCLUDED": "Исключенные теги", "TAGS_REQUIRED": "Обязательные теги", "TASK_DONE_STATE": "Состояние выполнения задачи", "TASK_DONE_STATE_ALL": "Все", "TASK_DONE_STATE_DONE": "Готово", "TASK_DONE_STATE_UNDONE": "Не выполнено"}, "V": {"ADD_NEW_BOARD": "Добавить новую доску", "CONFIRM_DELETE": "Вы действительно хотите удалить эту доску?", "CREATE_NEW_TAG_BTN": "Создать тег", "CREATE_NEW_TAG_MSG": "Для работы этой доски необходимо создать 1 новый тег", "CREATE_NEW_TAGS_BTN": "Создать теги", "CREATE_NEW_TAGS_MSG": "Для работы этой доски необходимо создать {{nr}} новых тегов", "EDIT_BOARD": "Редактировать доску", "NO_PANELS_BTN": "Настроить доску", "NO_PANELS_MSG": "У этой доски нет настроенных панелей. Добавьте несколько панелей."}}, "CALDAV": {"DIALOG_INITIAL": {"TITLE": "Настройка CalDav для проекта"}, "FORM": {"CALDAV_CATEGORY_FILTER": "Категория, по которой нужно отфильтровать проблемы (оставьте поле пустым)", "CALDAV_PASSWORD": "Ваш пароль CalDav", "CALDAV_RESOURCE": "Имя ресурса CalDav (календарь)", "CALDAV_URL": "CalDav URL (базовый URL)", "CALDAV_USER": "Ваше имя пользователя CalDav", "IS_TRANSITION_ISSUES_ENABLED": "Автоматически заполнять задачи CalDav по завершении задачи"}, "FORM_SECTION": {"HELP": "<p>Здесь вы можете настроить Super Productivity, чтобы отображать незавершенные задачи CalDav для конкретного проекта на панели создания задач в представлении ежедневного планирования. Они будут перечислены как предложения и предоставят ссылку на задачу, а также дополнительную информацию о ней.</p> <p>Кроме того, вы можете автоматически добавлять и синхронизировать все незавершенные задачи в журнал невыполненных задач.</p>", "TITLE": "CalDav"}, "ISSUE_CONTENT": {"ASSIGNEE": "Исполнитель", "AT": "в", "ATTACHMENTS": "Вложения", "CHANGED": "Изменено", "COMMENTS": "Комментарии", "COMPONENTS": "Компоненты", "DESCRIPTION": "Описание", "LABELS": "Метки", "LIST_OF_CHANGES": "Список изменений", "MARK_AS_CHECKED": "Отметить обновления как отмеченные", "ON": "вкл.", "RELATED": "Связанные", "STATUS": "Статус", "STORY_POINTS": "История очков", "SUB_TASKS": "Подзадачи", "SUMMARY": "Сводка", "WORKLOG": "<PERSON>у<PERSON><PERSON>л работы", "WRITE_A_COMMENT": "Написать комментарий"}, "S": {"CALENDAR_NOT_FOUND": "CalDav: Календарь \"{{calendarName}}\" не найден", "CALENDAR_READ_ONLY": "CalDav: Календарь \"{{calendarName}}\" доступен только для чтения", "ISSUE_NOT_FOUND": "CalDav: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, что <PERSON> \"{{issueId}}\" удален на сервере."}}, "CALENDARS": {"BANNER": {"ADD_AS_TASK": "Добавить как задачу", "FOCUS_TASK": "Задача в фокусе", "TXT": "<strong>{{title}}</strong> начинать в <strong>{{start}}</strong>!", "TXT_MULTIPLE": "<strong>{{title}}</strong> начинать в <strong>{{start}}</strong>!<br> (и {{nrOfOtherBanners}} ожидаются другие события)", "TXT_PAST": "<strong>{{title}}</strong> начат в <strong>{{start}}</strong>!", "TXT_PAST_MULTIPLE": "<strong>{{title}}</strong> начат в <strong>{{start}}</strong>!<br> (и {{nrOfOtherBanners}} ожидаются другие события)"}, "S": {"CAL_PROVIDER_ERROR": "Ошибка поставщика календаря: {{errTxt}}"}}, "CONFIG": {"S": {"UPDATE_SECTION": "Обновлены настройки для <strong>{{sectionKey}}</strong>"}}, "D_RATE": {"A_HOW": "Как и где выставлять оценки", "BTN_DONT_BOTHER": "Больше не беспокойте меня", "TITLE": "🙈 Пожалуйста, простите нас, но...", "TXT": "Вы бы очень помогли проекту, если бы <strong>дали ему хорошую оценку, если он вам нравится!</strong>"}, "DOMINA_MODE": {"FORM": {"HELP": "Повторяет настроенную фразу каждые Х секунд", "L_INTERVAL": "Интервал повторения фразы", "L_TEXT": "Текст", "L_TEXT_DESCRIPTION": "Н-р: \"Работай над ${currentTaskTitle}!\"", "L_VOICE": "Выбрать голос", "L_VOICE_DESCRIPTION": "Выберите голос", "TITLE": "Режим доминирования"}}, "DROPBOX": {"S": {"ACCESS_TOKEN_ERROR": "Dropbox: невозможно создать токен доступа из кода авторизации", "ACCESS_TOKEN_GENERATED": "Dropbox: токен доступа, сгенерирован из кода авторизации", "AUTH_ERROR": "Dropbox: предоставлен неверный токен доступа", "AUTH_ERROR_ACTION": "Сменить токен", "OFFLINE": "Dropbox: невозможно синхронизироваться в автономном режиме", "SYNC_ERROR": "Dropbox: ошибка при синхронизации", "UNABLE_TO_GENERATE_PKCE_CHALLENGE": "Dropbox: невозможно создать запрос PKCE."}}, "FINISH_DAY_BEFORE_EXIT": {"C": {"FINISH_DAY_BEFORE_EXIT": "В вашем списке на сегодня есть {{nr}} выполненных задач, еще не перемещенных в архив. Вы действительно хотите выйти, не закончив свой день?"}}, "FOCUS_MODE": {"B": {"SESSION_RUNNING": "Сессия фокуса запущена", "TO_FOCUS_OVERLAY": "Для наложения фокуса"}, "BACK_TO_PLANNING": "Вернуться к планированию", "CONGRATS": "Поздравляю с завершением этого сеанса!", "CONTINUE_FOCUS_SESSION": "Продолжить фокус-сеанс", "COUNTDOWN": "Обратный отсчет", "FINISH_TASK_AND_SELECT_NEXT": "Завершить задачу и выбрать следующую", "FLOWTIME": "Время потока", "FOR_TASK": "для задачи", "GET_READY": "Приготовьтесь к своему фокус-сеансу!", "GO_TO_PROCRASTINATION": "Получить помощь, когда прокрастинируешь", "GOGOGO": "Вперёд, вперёд, вперёд!!!", "NEXT": "Дальше", "ON": "на", "OPEN_ISSUE_IN_BROWSER": "Открыть проблему в Браузере", "POMODORO_BACK": "Назад", "POMODORO_DISABLE": "Отключить Pomodoro", "POMODORO_INFO": "Фокус-сеанс не может использоваться, когда включен таймер Pomodoro.", "PREP_GET_MENTALLY_READY": "Будьте морально готовы к тому, чтобы быть сосредоточенным и продуктивным", "PREP_SIT_UPRIGHT": "Сядьте (или встаньте) прямо", "PREP_STRETCH": "Сделайте небольшую растяжку", "SELECT_ANOTHER_TASK": "Выбрать другую задачу", "SELECT_TASK": "Выбрать задачу для фокусировки", "SESSION_COMPLETED": "Сессия фокуса завершена!", "SET_FOCUS_SESSION_DURATION": "Установить длительность фокус-сеанса", "SHOW_HIDE_NOTES_AND_ATTACHMENTS": "Показать/скрыть заметки о задачах и вложениях", "START_FOCUS_SESSION": "Начать фокус-сеанс", "START_NEXT_FOCUS_SESSION": "Начать следующий фокус-сеанс", "WORKED_FOR": "Вы работаете над"}, "GITEA": {"DIALOG_INITIAL": {"TITLE": "Настройка Gitea для проекта"}, "FORM": {"FILTER_USER": "Имя пользователя (например, чтобы найти ваши изменения)", "HOST": "Хост (н-р: https://try.gitea.io)", "REPO_FULL_NAME": "Имя пользователя или название организации/проекта", "REPO_FULL_NAME_DESCRIPTION": "Его можно найти как часть URL-адреса при просмотре проекта в браузере.", "SCOPE": "Скоуп", "SCOPE_ALL": "Все", "SCOPE_ASSIGNED": "Назначено мне", "SCOPE_CREATED": "Создано мной", "TOKEN": "Токен доступа"}, "FORM_SECTION": {"HELP": "<p>Здесь вы можете настроить SuperProductivity чтобы отображать проблемы Gitea для конкретного репозитория на панели создания задач в представлении ежедневного планирования. Они будут перечислены в качестве предложений и предоставят ссылку на проблему, а также дополнительную информацию о ней.</p> <p>Кроме того, вы можете автоматически добавлять и синхронизировать все открытые проблемы.</p></p>Чтобы получить доступ к закрытым репозиториям, вы можете предоставить токен доступа.", "TITLE": "<PERSON><PERSON><PERSON>"}, "ISSUE_CONTENT": {"ASSIGNEE": "Исполнитель", "AT": "в", "DESCRIPTION": "Описание", "LABELS": "Метки", "MARK_AS_CHECKED": "Отметить изменения как отмеченные", "PROJECT": "Проект", "STATUS": "Положение дел", "SUMMARY": "Краткое содержание", "WRITE_A_COMMENT": "Написать комментарий"}, "S": {"ERR_UNKNOWN": "Gitea: Неизвестная ошибка {{statusCode}} {{errorMsg}}. Превышен лимит ставок Api?"}}, "GITHUB": {"DIALOG_INITIAL": {"TITLE": "Настройка GitHub для проекта"}, "FORM": {"FILTER_USER": "Имя пользователя (чтобы отфильтровать изменения самостоятельно)", "INVALID_TOKEN_MESSAGE": "Не является действительным токеном GitHub. Он должен начинаться с \"ghp_\".", "IS_ASSIGNEE_FILTER": "Фильтровать назначенные мне проблемы", "REPO": "\"username/repository\" для git-репозитория, который вы хотите отслеживать", "TOKEN": "Токен доступа"}, "FORM_SECTION": {"HELP": "<p>Здесь вы можете настроить Super Productivity, чтобы отображать открытые проблемы GitHub для определенного репозитория на панели создания задач в представлении ежедневного планирования. Они будут перечислены в качестве предложений и предоставят ссылку на проблему, а также дополнительную информацию о ней.</p><p>Кроме того, вы можете автоматически добавлять и синхронизировать все открытые проблемы в своей невыполненной задаче.</p><p>Чтобы получить доступ к закрытым репозиториям, вы можете предоставить токен доступа. <a href='https://docs.github.com/en/free-pro-team@latest/developers/apps/scopes-for-oauth-apps'>Более подробную информацию о его возможностях можно найти здесь</a>.", "TITLE": "GitHub"}, "ISSUE_CONTENT": {"ASSIGNEE": "Исполнитель", "AT": "в", "DESCRIPTION": "Описание", "LABELS": "Метки", "LAST_COMMENT": "Последний комментарий", "LOAD_ALL_COMMENTS": "Загрузить все {{nr}} комментарии", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "Загрузить описание и все комментарии", "MARK_AS_CHECKED": "Отметить обновления как отмеченные", "STATUS": "Статус", "SUMMARY": "Сводка", "WRITE_A_COMMENT": "Оставить комментарий"}, "S": {"CONFIG_ERROR": "GitHub: ошибка сопоставления данных. Корректно ли название репозитория?", "ERR_UNKNOWN": "GitHub: неизвестная ошибка {{statusCode}} {{errorMsg}}"}}, "GITLAB": {"DIALOG_INITIAL": {"TITLE": "Настройка GitLab для проекта"}, "*********************": {"PAST_DAY_INFO": "Предварительно заполненная длительность содержит неотслеживаемые данные за прошлые дни.", "T_ALREADY_TRACKED": "Уже отслеживается", "T_TITLE": "Заголовок", "T_TO_BE_SUBMITTED": "Подлежащий подтверждению", "TITLE": "Отправляйте время, затраченное на решение проблем, в GitLab", "TOTAL_MSG": "Вы представите <em>{{totalTimeToSubmit}}</em> рабочего времени за сегодня в общей сложности на <em>{{nrOfTasksToSubmit}}</em> различным вопросам."}, "FORM": {"FILTER": "Пользовательский фильтр", "FILTER_DESCRIPTION": "См. https://docs.gitlab.com/ee/api/issues.html#list-issues. Несколько можно объединить с помощью &", "FILTER_USER": "Имя пользователя (чтобы отфильтровать изменения самостоятельно)", "GITLAB_BASE_URL": "Пользовательский базовый URL GitLab", "PROJECT": "Идентификатор проекта или имя \"username/repository\"", "PROJECT_HINT": "например, johanne<PERSON><PERSON>/super-productivity", "SCOPE": "Область", "SCOPE_ALL": "Все", "SCOPE_ASSIGNED": "Назначено мне", "SCOPE_CREATED": "Создано мной", "SOURCE": "Источник", "SOURCE_GLOBAL": "Все", "SOURCE_GROUP": "Группа", "SOURCE_PROJECT": "Проект", "SUBMIT_TIMELOGS": "Отправляйте временные журналы в Gitlab", "SUBMIT_TIMELOGS_DESCRIPTION": "Отображение диалогового окна отслеживания времени после нажатия на кнопку завершения дня", "TOKEN": "Токен доступа"}, "FORM_SECTION": {"HELP": "<p>Здесь вы можете настроить Super Productivity для отображения списка открытых проблем GitLab (будь то онлайн-версия или автономный экземпляр) для конкретного проекта на панели создания задач в представлении ежедневного планирования. Они будут перечислены в качестве предложений и предоставят ссылку на проблему, а также дополнительную информацию о ней.</p><p>Кроме того, вы можете автоматически добавлять и синхронизировать все открытые проблемы со списком задач.</p>", "TITLE": "GitLab"}, "ISSUE_CONTENT": {"ASSIGNEE": "Исполнитель", "AT": "в", "DESCRIPTION": "Описание", "LABELS": "Метки", "MARK_AS_CHECKED": "Отметить обновления как отмеченные", "PROJECT": "проект", "STATUS": "Статус", "SUMMARY": "Сводка", "WRITE_A_COMMENT": "Оставить комментарий"}, "S": {"ERR_UNKNOWN": "GitLab: неизвестная ошибка {{statusCode}} {{errorMsg}}"}}, "ISSUE": {"CROSS_ORIGIN_BROWSER_WARNING": "Скорее всего, эта интеграция не работает в вашем браузере. Пожалуйста, загрузите версию Super Productivity для рабочего стола или Android!", "DEFAULT": {"ISSUE_STR": "вопрос", "ISSUES_STR": "вопросы"}, "DEFAULT_PROJECT_DESCRIPTION": "Проект, назначенный на задания, созданные из проблем.", "DEFAULT_PROJECT_LABEL": "Проект суперпродуктивности по умолчанию", "HOW_TO_GET_A_TOKEN": "Как получить токен?", "ISSUE_CONTENT": {"ASSIGNEE": "Исполнитель", "AT": "в", "ATTACHMENTS": "Вложения", "AUTHOR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CATEGORY": "Категория", "CHANGED": "Изменено", "COMMENTS": "Комментарии", "COMPONENTS": "Компоненты", "DESCRIPTION": "Описание", "DONE_RATIO": "Соотношение выполненных работ", "DUE_DATE": "Срок оплаты", "LABELS": "Метки", "LAST_COMMENT": "Последний комментарий", "LIST_OF_CHANGES": "Список изменений", "LOAD_ALL_COMMENTS": "Загрузить все {{nr}} комментариев", "LOAD_DESCRIPTION_AND_ALL_COMMENTS": "Загрузить описание и все комментарии", "LOCATION": "Местоположение", "MARK_AS_CHECKED": "Отметить обновления как проверенные", "ON": "вкл.", "PRIORITY": "ПРИОРИТЕТ", "RELATED": "Связанные", "START": "Начать", "STATUS": "Статус", "STORY_POINTS": "История очков", "SUB_TASKS": "Подзадачи", "SUMMARY": "Резюме", "TIME_SPENT": "Потраченное время", "TYPE": "Тип", "VERSION": "Версия", "WORKLOG": "<PERSON>у<PERSON><PERSON>л работы", "WRITE_A_COMMENT": "Написать комментарий"}, "S": {"ERR_GENERIC": "{{issueProviderName}} Ошибка: {{errTxt}}", "ERR_NETWORK": "{{issue<PERSON><PERSON>ider<PERSON>ame}}: запрос не выполнен из-за сетевой ошибки на стороне клиента.", "ERR_NOT_CONFIGURED": "{{issue<PERSON>rovider<PERSON>ame}}: неправильно настроен", "IMPORTED_MULTIPLE_ISSUES": "{{issueProviderName}}: импортировано {{nr}} новых {{issuesStr}} в очередь", "IMPORTED_SINGLE_ISSUE": "{{issueProviderName}}: импортировано {{issueStr}} \"{{issueTitle}}\" в очередь", "ISSUE_DELETED_OR_CLOSED": "{{issueProviderName}}: {{issueStrC}} \"{{issueTitle}}\" кажется удаленным или закрытым", "ISSUE_NO_UPDATE_REQUIRED": "{{issue<PERSON><PERSON>ider<PERSON>ame}}: обновление не требуется", "ISSUE_UPDATE_MULTIPLE": "{{issueProviderName}}: обновленные данные для {{nr}} {{issuesStr}}", "ISSUE_UPDATE_SINGLE": "{{issueProviderName}}: обновленные данные для \"{{issueTitle}}\"", "MANUAL_UPDATE_ISSUE_SUCCESS": "{{issueProviderName}}: обновленные данные для \"{{issueTitle}}\"", "MISSING_ISSUE_DATA": "{{issueProviderName}}: обнаружены задачи с отсутствующими данными {{issueStr}} . Перезарядка.", "NEW_COMMENT": "{{issueProviderName}}: новый комментарий для \"{{issueTitle}}\"", "POLLING_BACKLOG": "{{issueProviderName}}: опрос нового {{issuesStr}}", "POLLING_CHANGES": "{{issueProviderName}}: изменения в опросе для {{issuesStr}}"}}, "JIRA": {"BANNER": {"BLOCK_ACCESS_MSG": "Jira: Чтобы предотвратить отключение API, доступ был заблокирован Super Productivity. Вы, вероятно, должны проверить настройки Jira!", "BLOCK_ACCESS_UNBLOCK": "Разблокировать"}, "CFG_CMP": {"ALWAYS_ASK": "Всегда открывать диалог", "DO_NOT": "Не переходить", "DONE": "Статус для завершения задачи", "ENABLE": "Включить интеграцию Jira", "ENABLE_TRANSITIONS": "Включить обработку переходов", "IN_PROGRESS": "Статус для запуска задачи", "LOAD_SUGGESTIONS": "Загрузить предложения", "MAP_CUSTOM_FIELDS": "Карта пользовательских полей", "MAP_CUSTOM_FIELDS_INFO": "К сожалению, некоторые данные Jira сохраняются в пользовательских полях, которые различны для каждой установки. Если вы хотите включить эти данные, вам нужно выбрать подходящее поле для них.", "OPEN": "Состояние для приостановки задачи", "SELECT_ISSUE_FOR_TRANSITIONS": "Выберите проблему, чтобы загрузить доступные переходы", "STORY_POINTS": "Story Points", "TRANSITION": "Обработка переходных процессов"}, "DIALOG_CONFIRM_ASSIGNMENT": {"MSG": "<strong>{{summary}}</strong> в настоящее время назначено <strong>{{assignee}}</strong>. Вы хотите назначить это себе?", "OK": "Да"}, "DIALOG_INITIAL": {"TITLE": "Настройка Jira для проекта"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "Выберите статус для назначения", "CURRENT_ASSIGNEE": "Текущий исполнитель:", "CURRENT_STATUS": "Текущее состояние:", "TASK_NAME": "Название задачи:", "TITLE": "Jira: обновить статус", "UPDATE_STATUS": "Обновить статус"}, "DIALOG_WORKLOG": {"CHECKBOXES": {"ALL_TIME": "Всегда использовать по умолчанию все время, потраченное на задачу", "ALL_TIME_MINUS_LOGGED": "Всегда используйте только потраченное время минус время, зарегистрированное по умолчанию", "TIME_SPENT_TODAY": "Всегда использовать по умолчанию только время, потраченное сегодня", "TIME_SPENT_YESTERDAY": "Всегда использовать по умолчанию только время, потраченное вчера"}, "CURRENTLY_LOGGED": "Текущее зарегистрированное время:", "INVALID_DATE": "Введенное значение не является датой!", "SAVE_WORKLOG": "Сохранить рабочий журнал", "STARTED": "Началось", "SUBMIT_WORKLOG_FOR": "Отправить рабочий журнал Jira для", "TIME_SPENT": "Время потрачено", "TIME_SPENT_TOOLTIP": "Добавить разное время", "TITLE": "Jira: отправить рабочий журнал"}, "FORM_ADV": {"AUTO_ADD_BACKLOG_JQL_QUERY": "JQL используется для автоматического добавления задач в список задач", "IS_ADD_WORKLOG_ON_SUB_TASK_DONE": "Открыть диалоговое окно для отправки рабочего журнала в Jira после выполнения подзадачи", "IS_CHECK_TO_RE_ASSIGN_TICKET_ON_TASK_START": "Проверьте, назначена ли текущая проблема, над которой ведется работа, текущему пользователю", "IS_WORKLOG_ENABLED": "Открыть диалоговое окно для отправки рабочего журнала в Jira, когда задача выполнена", "SEARCH_JQL_QUERY": "JQL Query для ограничения задач поисковика", "WORKLOG_DEFAULT_ALL_TIME": "Заполнить все время, потраченное на задачу", "WORKLOG_DEFAULT_ALL_TIME_MINUS_LOGGED": "Укажите все потраченное время за вычетом времени в журнале", "WORKLOG_DEFAULT_TIME_MODE": "Значение времени по умолчанию для диалога", "WORKLOG_DEFAULT_TODAY": "Заполните только время, потраченное сегодня", "WORKLOG_DEFAULT_YESTERDAY": "Заполните только время, потраченное вчера"}, "FORM_CRED": {"ALLOW_SELF_SIGNED": "Разрешить самоподписанный сертификат", "HOST": "<PERSON>о<PERSON><PERSON> (наприм<PERSON><PERSON>, http://my-host.de:1234)", "PASSWORD": "Токен / Пароль", "USE_PAT": "Использовать токен персонального доступа вместо пароля", "USER_NAME": "Адрес электронной почты / Имя пользователя", "WONKY_COOKIE_MODE": "Резервная аутентификация Wonky Cookie (только настольное приложение)"}, "FORM_SECTION": {"ADV_CFG": "Расширенный настройки", "HELP_ARR": {"H1": "Базовая конфигурация", "H2": "Настройки рабочего журнала", "H3": "Переходы по умолчанию", "P1_1": "Укажите логин (можно найти на странице своего профиля) и <a target=\"_blank\" href=\"https://confluence.atlassian.com/cloud/api-tokens-938839638.html\" target=\"_blank\">API-токен</a> или пароль, если по какой-либо причине вы не можете его сгенерировать. Обратите внимание, что новые версии jira иногда работают только с токеном.", "P1_2": "Вам также необходимо указать JQL-запрос, который используется для предложений по добавлению задач из Jira. Если вам нужна помощь, просмотрите эту ссылку <a target=\"_blank\" href=\"https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html\" target=\"_blank\">https://confluence.atlassian.com/jirasoftwarecloud/advanced-searching-764478330.html</a>.", "P1_3": "Вы также можете настроить, если вы хотите автоматически (например, каждый раз, когда вы посещаете представление планирования), добавлять все новые задачи, указанные в пользовательском запросе JQL, в очередь.", "P1_4": "Другой вариант - «Проверить, назначен ли текущий билет текущему пользователю». Если эта опция включена и вы начинаете, будет проверена, назначена ли вам эта заявка в Jira, если не появится диалоговое окно, в котором вы можете назначить заявку для себя.", "P2_1": "Есть несколько вариантов, чтобы определить, когда и как вы хотите отправить рабочий журнал. Включение <em>«Открыть диалоговое окно рабочего журнала для добавления рабочего журнала в Jira после выполнения задачи»</em> открывает диалоговое окно для добавления рабочего журнала каждый раз, когда вы отмечаете задачу Jira как выполненную. Так что имейте в виду, что рабочие журналы будут добавляться поверх всего отслеживаемого до сих пор. Поэтому, если вы пометите задачу как выполненную во второй раз, вы, возможно, не захотите снова отправлять полное рабочее время для этой задачи.", "P2_2": "<em>«Открыть диалог рабочего журнала, когда подзадача выполнена, а не для задач с самими подзадачами»</em> открывает диалог рабочего журнала каждый раз, когда вы помечаете подзадачу проблемы Jira как выполненную. Поскольку вы уже отслеживаете свое время с помощью подзадач, диалоговое окно не открывается, если вы пометите само задание Jira как выполненное.", "P2_3": "<em>'Автоматически отправлять обновления в рабочий журнал без диалога'</em> делает то, что говорит. Поскольку пометка задачи как выполненной несколько раз приводит к тому, что все рабочее время отслеживается дважды, это не рекомендуется.", "P3_1": "Здесь вы можете перенастроить ваши переходы по умолчанию. Jira обеспечивает широкую конфигурацию переходов, которые обычно вступают в действие в виде различных столбцов на вашей agile-доске Jira. Мы не можем делать предположения о том, куда и когда переносить ваши задачи, поэтому вам нужно установить это вручную."}}, "ISSUE_CONTENT": {"ASSIGNEE": "Исполнитель", "AT": "В", "ATTACHMENTS": "Вложения", "CHANGED": "Изменено", "COMMENTS": "Комментарии", "COMPONENTS": "Компоненты", "DESCRIPTION": "Описание", "LIST_OF_CHANGES": "Список изменений", "MARK_AS_CHECKED": "Отметить обновления как отмеченные", "ON": "на", "RELATED": "Связанный", "STATUS": "Статус", "STORY_POINTS": "Очки истории", "SUB_TASKS": "Подзадачи", "SUMMARY": "Сводка", "WORKLOG": "<PERSON><PERSON><PERSON><PERSON><PERSON> событий", "WRITE_A_COMMENT": "Оставить комментарий"}, "S": {"ADDED_WORKLOG_FOR": "Jira: добавлен рабочий журнал для {{issueKey}}", "EXTENSION_NOT_LOADED": "Расширение Super Productivity Extension не загружено. Перезагрузка страницы может помочь", "INSUFFICIENT_SETTINGS": "Недостаточно настроек для Jira", "INVALID_RESPONSE": "Jira: ответ содержал недопустимые данные", "ISSUE_NO_UPDATE_REQUIRED": "<PERSON>ra: \"{{issueText}}\" уже обновлен", "MANUAL_UPDATE_ISSUE_SUCCESS": "Jira: обновлены данные для \"{{issueText}}\"", "MISSING_ISSUE_DATA": "Jira: Найдены задачи с отсутствующими данными о проблеме. Перезагрузка.", "NO_AUTO_IMPORT_JQL": "Jira: для автоматического импорта не определен поисковый запрос", "NO_VALID_TRANSITION": "Jira: Не настроен правильный переход", "TIMED_OUT": "Jira: время запроса истекло", "TRANSITION": "Jira: установите проблему \"{{issueKey}}\" на \"{{name}}\"", "TRANSITION_SUCCESS": "Jira: Задайте вопрос {{issueKey}} в <strong>{{chosenTransition}}</strong>", "TRANSITIONS_LOADED": "Jira: Переходы загружены. Используйте выбранные ниже, чтобы назначить их", "UNABLE_TO_REASSIGN": "Jira: Невозможно переназначить билет самому себе, потому что вы не указали имя пользователя. Пожалуйста, посетите настройки."}, "STEPPER": {"CREDENTIALS": "Полномочия", "DONE": "Все готово.", "LOGIN_SUCCESS": "Успешный вход в систему!", "TEST_CREDENTIALS": "Проверка учетных данных", "WELCOME_USER": "Добро пожаловать {{user}}!"}}, "MARKDOWN_PASTE": {"CONFIRM_ADD_TO_SUB_TASK_NOTES": "Добавить вставленный список markdown в заметки подзадачи \"{{parentTaskTitle}}\"?", "CONFIRM_PARENT_TASKS": "Создать <strong>{{tasksCount}} новые задачи</strong> из вставленного списка markdown?", "CONFIRM_PARENT_TASKS_WITH_SUBS": "Создать <strong>{{tasksCount}} новые задачи и {{subTasksCount}} подзадачи</strong> из вставленного списка markdown?", "CONFIRM_SUB_TASKS": "Создать {{tasksCount}} новые подзадачи из вставленного списка markdown?", "CONFIRM_SUB_TASKS_WITH_PARENT": "Создать <strong>{{tasksCount}} новые подзадачи под \"{{parentTaskTitle}}\"</strong> из вставленного списка markdown?", "DIALOG_TITLE": "Обнаружен вставленный список Markdown!"}, "METRIC": {"BANNER": {"CHECK": "Я сделал это!"}, "CMP": {"AVG_BREAKS_PER_DAY": "Среднее количество перерывов в день", "AVG_TASKS_PER_DAY_WORKED": "Среднее количество заданий в день", "AVG_TIME_SPENT_ON_BREAKS": "Среднее время, потраченное на перерывах", "AVG_TIME_SPENT_PER_DAY": "Среднее время, потреченное за день", "AVG_TIME_SPENT_PER_TASK": "Среднее время, затрачиваемое на задание", "COUNTING_SUBTASKS": "(подсчет подзадач)", "DAYS_WORKED": "Рабочие дни", "GLOBAL_METRICS": "Глобальные показатели", "IMPROVEMENT_SELECTION_COUNT": "Сколько раз был выбран фактор улучшения", "MOOD_PRODUCTIVITY_OVER_TIME": "Настроение и производительность относительно времени", "NO_ADDITIONAL_DATA_YET": "Никаких дополнительных данных еще не собрано. Для этого воспользуйтесь формой на ежедневной сводной панели «Оценка».", "OBSTRUCTION_SELECTION_COUNT": "Сколько раз был выбран препятствующий фактор", "SIMPLE_CLICK_COUNTERS_OVER_TIME": "Счетчики Кликов относительно времени", "SIMPLE_COUNTERS": "Простые счетчики", "SIMPLE_STOPWATCH_COUNTERS_OVER_TIME": "Счетчики Секундомера относительно времени", "TASKS_DONE_CREATED": "Задачи (сделано / создано)", "TIME_ESTIMATED": "Расчетное время", "TIME_SPENT": "Время потрачено"}, "EVAL_FORM": {"ADD_NOTE_FOR_TOMORROW": "Добавить заметку на завтра", "DISABLE_REPEAT_EVERY_DAY": "Отключить ежедневное повторение", "ENABLE_REPEAT_EVERY_DAY": "Повторять каждый день", "HELP_H1": "Почему я должен волноваться?", "HELP_LINK_TXT": "Перейти в раздел метрик", "HELP_P1": "Время для небольшой самооценки! Ваши ответы здесь сохранены и предоставят вам немного статистики о том, как вы работаете в разделе метрик. Кроме того, предложения на завтра появятся над списком задач на следующий день.", "HELP_P2": "Это предназначено не столько для вычисления точных показателей или повышения эффективности работы машины, сколько для улучшения восприятия вашей работы. Может быть полезно оценить болевые точки в вашей повседневной жизни, а также найти факторы, которые вам помогут. Надеюсь, если вы будете немного систематичны, это поможет лучше понять их и улучшить то, что вы можете.", "IMPROVEMENTS": "Что улучшило вашу производительность?", "IMPROVEMENTS_TOMORROW": "Что вы могли бы сделать, чтобы улучшить завтра?", "MOOD": "Как вы себя чувствуете?", "MOOD_HINT": "1: Ужасно - 10: Велико<PERSON><PERSON><PERSON>но", "NOTES": "Заметки на завтра", "OBSTRUCTIONS": "Что мешало вашей производительности?", "PRODUCTIVITY": "Насколько эффективно вы работали?", "PRODUCTIVITY_HINT": "1: еще даже не начал - 10: чрезвычайно эффективный"}, "S": {"SAVE_METRIC": "Метрика успешно сохранена"}}, "NOTE": {"D_ADD": {"NOTE_LABEL": "Введите текст, чтобы сохранить как заметку ..."}, "D_FULLSCREEN": {"VIEW_PARSED": "Просмотр в виде markdown (без редактирования)", "VIEW_SPLIT": "Просмотр полученного и исходного markdown в разделенном представлении", "VIEW_TEXT_ONLY": "Просмотр в виде исходного текста"}, "NOTE_CMP": {"DISABLE_PARSE": "Отключить Markdown для предварительного просмотра", "ENABLE_PARSE": "Включить Markdown"}, "NOTES_CMP": {"ADD_BTN": "Добавить новую заметку", "DROP_TO_ADD": "Зайдите сюда, чтобы добавить новую заметку", "NO_NOTES": "В настоящее время нет заметок"}, "S": {"NOTE_ADDED": "Примечание сохранено"}}, "OPEN_PROJECT": {"CFG_CMP": {"ALWAYS_ASK": "Всегда открывать диалог", "DO_NOT": "Не изменять", "DONE": "Статус завершения задачи", "ENABLE": "Включить интеграцию Openproject", "ENABLE_TRANSITIONS": "Включить обработку переходов", "IN_PROGRESS": "Статус при начале задачи", "OPEN": "Статус при паузе задачи", "PROGRESS_ON_SAVE": "Прогресс по умолчанию при сохранении", "SELECT_ISSUE_FOR_TRANSITIONS": "Выберите проблему, чтобы загрузить доступные переходы", "TRANSITION": "Обработка переходов"}, "DIALOG_INITIAL": {"TITLE": "Настроить OpenProject для проекта"}, "DIALOG_TRACK_TIME": {"ACTIVITY": "Деятельность", "CURRENTLY_LOGGED": "Текущее зарегистрированное время:", "INVALID_DATE": "Введенное значение не является датой!", "POST_TIME": "Время публикации", "STARTED": "Началось", "SUBMIT_TIME_FOR": "Отправить время в OpenProject для", "TIME_SPENT": "Время потрачено", "TITLE": "OpenProject: отправить рабочий журнал"}, "DIALOG_TRANSITION": {"CHOOSE_STATUS": "Выберите статус для присвоения", "CURRENT_ASSIGNEE": "Текущий назначенный:", "CURRENT_STATUS": "Текущий статус:", "PERCENTAGE_DONE": "Процесс:", "TASK_NAME": "Название задачи:", "TITLE": "OpenProject: Статус обновления", "UPDATE_STATUS": "Обновить статус"}, "FORM": {"FILTER_USER": "Имя пользователя (чтобы отфильтровать изменения самостоятельно)", "HOST": "Хост (например: https://www.openproject.org/)", "IS_SHOW_TIME_TRACKING_DIALOG": "Показать диалоговое окно отслеживания времени для отчета в OpenProject", "IS_SHOW_TIME_TRACKING_DIALOG_DESCRIPTION": "Требуется включить модуль учета времени для проекта OpenProject", "IS_SHOW_TIME_TRACKING_DIALOG_FOR_EACH_SUB_TASK": "Показывать диалоговое окно учета времени после завершения подзадач", "PROJECT_ID": "Идентификатор проекта", "PROJECT_ID_DESCRIPTION": "Можно найти как часть URL-адреса при просмотре проекта в браузере.", "SCOPE": "Область", "SCOPE_ALL": "Все", "SCOPE_ASSIGNED": "Назначено мне", "SCOPE_CREATED": "Создано мной", "TOKEN": "Токен доступа"}, "FORM_SECTION": {"HELP": "<p>Здесь вы можете настроить Super Productivity для вывода списка открытых рабочих пакетов OpenProject. Обратите внимание, что для того, чтобы это работало в браузере, вам, вероятно, потребуется настроить CORS для вашего сервера OpenProject, чтобы разрешить доступ с app.super-productivity.com</p>", "TITLE": "OpenProject"}, "ISSUE_CONTENT": {"ASSIGNEE": "Уполномоченный", "ATTACHMENTS": "Вложения", "DESCRIPTION": "Описание", "MARK_AS_CHECKED": "Отметить обновления как отмеченные", "STATUS": "Статус", "SUMMARY": "Сводка", "TYPE": "Тип", "UPLOAD_ATTACHMENT": "Загрузить в задачу"}, "ISSUE_STRINGS": {"ISSUE_STR": "комплекс работ", "ISSUES_STR": "комплексы работ"}, "S": {"ERR_NO_FILE": "Файл не выбран", "ERR_UNKNOWN": "OpenProject: неизвестная ошибка {{statusCode}} {{errorMsg}}. Правильно ли настроен CORS для сервера?", "POST_TIME_SUCCESS": "OpenProject: Успешно создана запись времени для {{issueTitle}}", "TRANSITION": "OpenProject: Установить значение {{name}} для проблемы \"{{issueKey}}\"", "TRANSITION_SUCCESS": "OpenProject: Установить значение <strong>{{chosenTransition}}</strong> для проблемы {{issueKey}}", "TRANSITIONS_LOADED": "Переходы загружены. Используйте выбранные для назначения их"}}, "PLANNER": {"D": {"ADD_PLANNED": {"ADD_TO_TODAY": "Добавить на Сегодня", "RE_PLAN_ALL": "Перенести все", "TITLE": "Добавить запланированные задачи на сегодня"}}, "EDIT_REPEATED_TASK": "Редактирование повторного задания '{{taskName}}'", "NO_TASKS": "Задач нет", "PLAN_VIEW": {"NO_SCHEDULED_ITEMS": "Нет запланированных дел"}, "S": {"REMOVED_PLAN_DATE": "Удалена плановая дата для задачи '{{taskTitle}}'", "TASK_ALREADY_PLANNED": "Задание уже запланировано на {{date}}", "TASK_PLANNED_FOR": "Задача запланирована на {{date}}"}, "TASK_DRAWER": "Выдвижной ящик"}, "POMODORO": {"BACK_TO_WORK": "Возвращайтесь к работе!", "BREAK_IS_DONE": "Ваш перерыв закончен!", "ENJOY_YOURSELF": "Наслаждайся, двигайся, возвращайся:", "FINISH_SESSION_X": "Вы успешно завершили сеанс <strong>{{nr}}</strong>!", "NOTIFICATION": {"BREAK_TIME": "Помидорка: Время для перерыва{{nr}}!", "BREAK_X_START": "Помодоро: Перерыв {{nr}} начался!", "NO_TASKS": "Вам нужно добавить задачи, прежде чем таймер Pomodoro сможет запуститься.", "SESSION_X_START": "Помодоро: Сессия {{nr}} началась!"}, "S": {"RESET": "Сбросить на начало сессию Помидорки", "SESSION_SKIP": "Пропустить текущую сессию Помидорки", "SESSION_X_START": "Помодоро: Сессия {{nr}} началась!"}, "SKIP_BREAK": "Пропустить перерыв", "START_BREAK": "Начать перерыв"}, "PROCRASTINATION": {"BACK_TO_WORK": "Вернуться к работе!", "COMP": {"INTRO": "Люди склонны к прокрастинации обычно слишком строги к себе. Постарайтесь не делать этого. Это улучшит ваше чувство собственного достоинства, вызовет положительные эмоции и, конечно же, может помочь вам преодолеть прокрастинацию. Попробуйте небольшое упражнение:", "L1": "Присядьте ненадолго и потянитесь, если хотите. Немного успокойтесь", "L2": "Старайтесь прислушиваться к возникающим мыслям и чувствам", "L3": "Отвечаете ли вы себе так, как отвечали бы своему другу?", "L4": "Если ответ отрицательный, представьте своего друга в вашей ситуации. Что бы вы ему посоветовали? Что бы вы сделали для них?", "OUTRO": "Другие упражнения <a target=\"_blank\" href=\"https://drsoph.com/blog/2018/9/17/3-exercises-in-self-compassion\" target=\"_blank\">можно найти здесь</a> или в <a target=\"_blank\" href=\"https://www.google.com/search?q=self+compassion+exercises&oq=self+compassion+excers&aqs=chrome.1.69i57j0l5.4303j0j7&sourceid=chrome&ie=UTF-8\" target=\"_blank\">Google</a>.", "TITLE": "Сострадание к себе"}, "CUR": {"INTRO": "Прокрастинация - интересный феномен, не так ли? И при этом она бессмысленна. Совсем не в ваших долгосрочных интересах. Но все равно все так делают. Наслаждайтесь и исследуйте!", "L1": "Какие чувства вызывает у вас искушение медлить?", "L2": "Где вы чувствуете их в своем теле?", "L3": "Что они вам напоминают?", "L4": "Что происходит с мыслью о промедлении, когда вы наблюдаете это? Это усиливается? Рассеивается? Вызывает другие эмоции?", "L5": "Как изменяются ощущения в вашем теле, если вы продолжаете фокусироваться на этих ощущениях?", "PROCRASTINATION_TRIGGERS_TEXT": "Еще один очень эффективный метод - записать, что вызвало у вас желание откладывать дела на потом. Например, лично у меня часто возникает желание быстро перейти на reddit или мой любимый новостной сайт всякий раз, когда в поле зрения попадает окно моего браузера. С тех пор как я начал записывать свои причины в простой текстовый документ, я осознал, насколько укоренился этот шаблон, и это помогло мне поэкспериментировать с различными контрмерами.", "PROCRASTINATION_TRIGGERS_TITLE": "Запишите свои причины прокрастинации", "TITLE": "Любопытство"}, "H1": "Не будьте так требовательны к себе", "INTRO": {"AVOIDING": "Уклонение от задачи", "FEAR": "Страх неудачи", "STRESSED": "Переживает из-за того, что не успевает что-то сделать", "TITLE": "Вступление"}, "P1": "Прежде всего расслабьтесь! Каждый время от времени отлынивает от работы. И если вы не делаете то, что запланировали, вы можете хотя бы наслаждаться этим! Советуем посмотреть полезные советы в разделах ниже.", "P2": "Помните: прокрастинация - это проблема регулирования эмоций, а не проблема управления временем.", "REFRAME": {"INTRO": "Подумайте о том, что может быть позитивным в задаче.", "TITLE": "Рефрейминг", "TL1": "Что может быть интересного в этом?", "TL2": "Что получится, если вы завершите это?", "TL3": "Как вы будете относиться к этому, если завершите?"}, "SPLIT_UP": {"INTRO": "Разделите задачу на как можно больше маленьких кусочков.", "OUTRO": "Готово? Тогда подумай об этом. Что было бы - строго теоретически - первым делом, которое вы сделали бы <i>, если бы</i> вы начали работать над задачей? Просто подумай об этом...", "TITLE": "Разделите эту задачу!"}}, "PROJECT": {"D_CREATE": {"CREATE": "Создать проект", "EDIT": "Редактировать проект", "SETUP_CALDAV": "Настройка интеграции CalDav", "SETUP_GIT": "Настройка интеграции GitHub", "SETUP_GITEA_PROJECT": "Настройка интеграции с Gitea", "SETUP_GITLAB": "Настройка интеграции GitLab", "SETUP_JIRA": "Настройка интеграции Jira", "SETUP_OPEN_PROJECT": "Настройка интеграции OpenProject", "SETUP_REDMINE_PROJECT": "Настройка интеграции с Redmine"}, "D_DELETE": {"MSG": "Вы уверены, что хотите удалить проект \"{{title}}\"?"}, "FORM_BASIC": {"L_ENABLE_BACKLOG": "Включить бэклог проекта", "L_IS_HIDDEN_FROM_MENU": "Скрыть проект из меню", "L_TITLE": "Название проекта", "TITLE": "Базовые настройки"}, "FORM_THEME": {"D_IS_DARK_THEME": "Не будет использоваться, если система поддерживает глобальный темный режим.", "HELP": "Настройки темы для вашего проекта.", "L_BACKGROUND_IMAGE_DARK": "URL фонового изображения (темная тема)", "L_BACKGROUND_IMAGE_LIGHT": "URL фонового изображения (светлая тема)", "L_COLOR_ACCENT": "Цвет акцента", "L_COLOR_PRIMARY": "Основной цвет", "L_COLOR_WARN": "Цвет предупреждения / ошибки", "L_HUE_ACCENT": "Порог для темного текста на акцентном цветном фоне", "L_HUE_PRIMARY": "Порог для темного текста на основном цветном фоне", "L_HUE_WARN": "Порог для темного текста на цветном фоне предупреждения", "L_IS_AUTO_CONTRAST": "Автоматическая настройка цветов текста для лучшей читаемости", "L_IS_DISABLE_BACKGROUND_GRADIENT": "Отключить цветной фоновый градиент", "L_IS_REDUCED_THEME": "Используйте уменьшенный пользовательский интерфейс (без рамок вокруг задач)", "L_THEME_COLOR": "Цвет темы", "L_TITLE": "Заголовок", "TITLE": "Тема"}, "S": {"ARCHIVED": "Архивный проект", "CREATED": "Создан проект <strong>{{title}}</strong>. Вы можете выбрать его из меню в левом верхнем углу.", "DELETED": "Удаленный проект", "E_EXISTS": "Проект \"{{title}}\" уже существует", "E_INVALID_FILE": "Неверные данные для файла проекта", "ISSUE_PROVIDER_UPDATED": "Обновлены настройки проекта для <strong>{{issueProviderKey}}</strong>", "UNARCHIVED": "Разархивированный проект", "UPDATED": "Обновлены настройки проекта"}}, "QUICK_HISTORY": {"NO_DATA": "Нет данных за текущий год", "PAGE_TITLE": "Быстрая история", "WEEK_TITLE": "Неделя {{nr}} ({{timeSpent}})"}, "REDMINE": {"DIALOG_INITIAL": {"TITLE": "Настройка Redmine для проекта"}, "FORM": {"API_KEY": "ключ доступа к API", "HOST": "Хост (например: https://redmine.org)", "PROJECT_ID": "Идентификатор проекта", "PROJECT_ID_DESCRIPTION": "Можно найти как часть URL-адреса при просмотре проекта в браузере.", "SCOPE": "Скоуп", "SCOPE_ALL": "Все", "SCOPE_ASSIGNED": "Назначено мне", "SCOPE_CREATED": "Создано мной"}, "FORM_SECTION": {"HELP": "<p>Здесь вы можете настроить SuperProductivity, чтобы отображать открытые проблемы Redmine (будь то онлайн-версия или автономный экземпляр) для конкретного проекта на панели создания задач в представлении ежедневного планирования. Они будут перечислены в качестве предложений и предоставят ссылку на проблему, а также дополнительную информацию о ней.</p><p>Кроме того, вы можете автоматически добавлять и синхронизировать все открытые проблемы со списком задач.</p>", "TITLE": "Redmine"}, "ISSUE_CONTENT": {"AUTHOR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "Описание", "MARK_AS_CHECKED": "Отметить обновления как отмеченные", "PRIORITY": "Приоритет", "STATUS": "Статус"}, "S": {"ERR_UNKNOWN": "Redmine: Неизвестная ошибка {{statusCode}} {{errorMsg}}."}}, "REMINDER": {"COUNTDOWN_BANNER": {"HIDE": "Скрыть", "START_NOW": "Начать сейчас", "TXT": "<strong>{{title}}</strong> начало в <strong>{{start}}</strong>!", "TXT_MULTIPLE": "<strong>{{title}}</strong> начало в <strong>{{start}}</strong>!<br> (и {{nrOfOtherBanners}} предстоят другие задачи)"}, "S_ACTIVE_TASK_DUE": "Срок выполнения задания, над которым вы сейчас работаете, наступил!<br/> ({{title}})", "S_REMINDER_ERR": "Ошибка интерфейса напоминания"}, "SCHEDULE": {"CONTINUED": "продолжение", "D_INITIAL": {"TEXT": "<p>Идея временной шкалы состоит в том, чтобы лучше понять, как запланированные задачи выполняются с течением времени. Он автоматически создается на основе ваших задач и различает две разные вещи: <strong>запланированные задачи</strong>, которые отображаются в запланированное время, и <strong>обычные задачи</strong> , которые должны выполняться вокруг этих фиксированных событий. Все задачи учитывают оценки времени, которые вы им назначили.</p><p>В дополнение к этому вы также можете указать время начала и окончания работы. Если настроены, обычные задачи никогда не будут отображаться за пределами этих. Обратите внимание, что график включает только ближайшие 30 дней.</p>", "TITLE": "Временная шкала"}, "END": "Конец работы", "LUNCH_BREAK": "Обеденный перерыв", "NO_TASKS": "На данный момент нет задач. Пожалуйста, добавьте несколько задач с помощью кнопки + на верхней панели.", "NOW": "Сей<PERSON><PERSON>с", "PLAN_END_DAY": "Запланировать на конец дня", "PLAN_START_DAY": "Запланировать на начало дня", "START": "Начало работы", "TASK_PROJECTION_INFO": "Будущая проекция запланированной повторяемой задачи"}, "SEARCH_BAR": {"INFO": "Щелкните на значок списка для поиска заархивированных задач.", "INFO_ARCHIVED": "Щелкните на значок архива для поиска обычных задач.", "NO_RESULTS": "Задачи, соответствующие вашему запросу, не найдены", "PLACEHOLDER": "Найдите задачу или описание задачи", "PLACEHOLDER_ARCHIVED": "Поиск заархивированных задач", "TOO_MANY_RESULTS": "Слишком много результатов, сузьте область поиска"}, "SIMPLE_COUNTER": {"D_CONFIRM_REMOVE": {"MSG": "Удаление простого счетчика, также удалит все прошлые данные, отслеженные на нем. Вы уверены, что хотите продолжить?", "OK": "Сделай это!"}, "D_EDIT": {"CURRENT_STREAK": "Текущая серия", "DAILY_GOAL": "Ежедневная цель", "DAYS": "<PERSON><PERSON>и", "L_COUNTER": "подсчитывать"}, "FORM": {"ADD_NEW": "Добавить простой счетчик", "HELP": "Здесь вы можете настроить простые кнопки, которые появятся в правом верхнем углу. Они могут быть либо таймерами, либо простым счетчиком, который подсчитывается нажатием на него.", "L_COUNTDOWN_DURATION": "Продолжительность обратного отсчета", "L_DAILY_GOAL": "Ежедневная цель для успешной полосы", "L_ICON": "Значок", "L_ICON_ON": "Значок при переключении", "L_IS_ENABLED": "Включено", "L_TITLE": "Заголовок", "L_TRACK_STREAKS": "Полосы треков", "L_TYPE": "Тип", "L_WEEKDAYS": "Дни недели для проверки наличия полосы", "TITLE": "Простые счетчики", "TYPE_CLICK_COUNTER": "Счетчик кликов", "TYPE_REPEATED_COUNTDOWN": "Повторный обратный отсчет", "TYPE_STOPWATCH": "Секундомер"}, "S": {"GOAL_REACHED_1": "Вы достигли своей цели на сегодня!", "GOAL_REACHED_2": "Продолжительность текущей полосы:"}}, "SYNC": {"A": {"ARCHIVE_ONLY_UPLOADED": "Ваши данные были загружены только частично. Пожалуйста, повторите попытку позже! В противном случае вы не сможете синхронизировать свои данные с другими устройствами.", "POSSIBLE_LEGACY_DATA": "В Super Productivity улучшена синхронизация, теперь используются два отдельных файла, а не один, что позволяет передавать гораздо меньше данных. Рекомендуется обновить все экземпляры Super Productivity и сначала синхронизировать данные из того экземпляра приложения, в котором данные являются самыми свежими. Если это данные с вашего локального устройства, то, пожалуйста, проигнорируйте это предупреждение и просто загрузите их, подтвердив в следующем диалоговом окне.", "REMOTE_MODEL_VERSION_NEWER": "Удаленная версия модели новее локальной. Пожалуйста, обновите свое локальное приложение до последней версии!"}, "C": {"EMPTY_SYNC": "Вы пытаетесь синхронизировать пустой объект данных. Если вы пытаетесь настроить синхронизацию из нового экземпляра приложения, просто нажмите ОК, чтобы загрузить данные с сервера. В противном случае, проверьте свои данные.", "FORCE_UPLOAD": "Все равно загрузить локальные данные?", "FORCE_UPLOAD_AFTER_ERROR": "Произошла ошибка при загрузке ваших локальных данных. Пробовать принудительно обновить?", "MIGRATE_LEGACY": "При импорте обнаружены устаревшие данные. Вы хотите попробовать их перенести?", "NO_REMOTE_DATA": "Удаленных данных не найдено. Загрузить локальный на удаленный?", "TRY_LOAD_REMOTE_AGAIN": "Попробовать еще раз загрузить данные из удаленного источника?", "UNABLE_TO_LOAD_REMOTE_DATA": "Не удается загрузить данные с удаленного компьютера. Вы хотите попробовать перезаписать удаленные данные своими локальными данными? При этом все удаленные данные будут потеряны."}, "D_AUTH_CODE": {"FOLLOW_LINK": "Откройте следующую ссылку и скопируйте предоставленный там код авторизации в поле ввода ниже.", "GET_AUTH_CODE": "Получить код авторизации", "L_AUTH_CODE": "Введите код авторизации", "TITLE": "Логин: {{provider}}"}, "D_CONFLICT": {"LAMPORT_CLOCK": "Ревизия", "LAST_CHANGE": "Последнее изменение:", "LAST_SYNC": "Последняя синхронизация:", "LOCAL": "Локальный", "LOCAL_REMOTE": "Локальный -> Удаленный", "REMOTE": "Удаленный", "TEXT": "<p>Обновление из Dropbox. Как локальные, так и удаленные данные кажутся измененными.</p>", "TIMESTAMP": "Временная метка", "TITLE": "Dropbox: Конфликтующие данные", "USE_LOCAL": "Использовать локальный", "USE_REMOTE": "Использовать удаленный"}, "D_DECRYPT_ERROR": {"BTN_OVER_WRITE_REMOTE": "Изменение и перезапись удаленного репозитория", "CHANGE_PW_AND_DECRYPT": "Изменить и попытаться расшифровать", "P1": "Ваши данные зашифрованы, и расшифровка не удалась. Пожалуйста, введите правильный пароль!", "P2": "Вы также можете изменить пароль, который перезапишет все удаленные данные.", "PASSWORD": "Пароль"}, "D_INCOMPLETE_SYNC": {"BTN_CLOSE_APP": "Закрыть приложение", "BTN_DOWNLOAD_BACKUP": "Загрузить локальную резервную копию", "BTN_FORCE_UPLOAD": "Принудительная локальная загрузка", "P1": "Данные удаленной синхронизации непоследовательны!", "P2": "Затронутая модель:", "P3": "У вас есть 2 варианта:", "P4": "1. Перейдите на другое устройство и попытайтесь выполнить полную синхронизацию там.", "P5": "2. Перезапишите удаленные данные локальными данными. Все удаленные изменения будут потеряны!", "P6": "Рекомендуется создать резервную копию данных, которые вы перезаписываете!!", "T1": "Последняя синхронизация не завершена!", "T2": "Ваши архивные данные не были должным образом загружены во время последней синхронизации:", "T3": "У вас есть 2 варианта:", "T4": "1. Перейдите на другое устройство и завершите синхронизацию на нем.", "T5": "2. Или же вы можете перезаписать удаленные данные своими локальными. Все удаленные изменения будут\nпотеряны!", "T6": "Рекомендуется создать резервную копию данных, которые вы перезаписываете!!!"}, "D_INITIAL_CFG": {"SAVE_AND_ENABLE": "Сохранить и включить синхронизацию", "TITLE": "Настроить синхронизацию"}, "D_PERMISSION": {"DISABLE_SYNC": "Отключить синхронизацию", "PERM_FILE": "Предоставить разрешение", "TEXT": "<p>Ваше разрешение на локальную синхронизацию файлов было отозвано.</p>", "TITLE": "Синхронизация: отказано в разрешении доступа к локальному файлу"}, "FORM": {"DROPBOX": {"L_ACCESS_TOKEN": "Токен доступа (генерируется из кода авторизации)"}, "GOOGLE": {"L_SYNC_FILE_NAME": "Синхронизировать имя файла"}, "L_ENABLE_COMPRESSION": "Включить сжатие (более быстрая передача данных)", "L_ENABLE_ENCRYPTION": "Включите сквозное шифрование (экспериментальное) - сделайте ваши данные недоступными для вашего поставщика синхронизации.", "L_ENABLE_SYNCING": "Включить синхронизацию", "L_ENCRYPTION_NOTES": "ВАЖНЫЕ ЗАМЕЧАНИЯ: Чтобы все заработало, вам нужно будет установить тот же пароль на других ваших устройствах перед следующей синхронизацией. Пожалуйста, выберите надежный пароль. Пожалуйста, также обратите внимание, что <strong>вы не сможете получить доступ к своим данным, если забудете этот пароль. Восстановление невозможно</strong>, поскольку ключ есть только у вас. Вероятно, шифрование будет достаточно надежным, чтобы противостоять большинству злоумышленников, но гарантии нет.</strong>", "L_ENCRYPTION_PASSWORD": "Пароль для шифрования (НЕ ЗАБУДЬТЕ)", "L_SYNC_INTERVAL": "Интервал синхронизации", "L_SYNC_PROVIDER": "Провайдер синхронизации", "LOCAL_FILE": {"L_SYNC_FILE_PATH_PERMISSION_VALIDATION": "Требуется разрешение на доступ к файлам", "L_SYNC_FOLDER_PATH": "Путь к папке синхронизации"}, "TITLE": "Синхронизация", "WEB_DAV": {"CORS_INFO": "<strong>Экспериментальный!!</strong> Для работы, вам необходимо отключить или ограничить CORS для вашего экземпляра Nextcloud, что может негативно повлиять на безопасность! Пожалуйста, <a href='https://github.com/nextcloud/server/issues/3131'>обратитесь к этой теме</a> для получения дополнительной информации. Используйте на свой риск!", "L_BASE_URL": "Базовый URL", "L_PASSWORD": "Пароль", "L_SYNC_FOLDER_PATH": "Путь к папке синхронизации", "L_USER_NAME": "Имя пользователя"}}, "S": {"ALREADY_IN_SYNC": "Уже синхронизированы", "ALREADY_IN_SYNC_NO_LOCAL_CHANGES": "Никаких локальных изменений – уже синхронизировано", "BTN_CONFIGURE": "Настроить", "BTN_FORCE_OVERWRITE": "Принудительная перезапись", "ERROR_DATA_IS_CURRENTLY_WRITTEN": "Удаленные данные в настоящее время находятся в процессе записи", "ERROR_FALLBACK_TO_BACKUP": "Что-то пошло не так при импорте данных. Откат к локальной резервной копии.", "ERROR_INVALID_DATA": "Ошибка при синхронизации. Неверные данные", "ERROR_NO_REV": "Нет допустимого значения rev для удаленного файла", "ERROR_UNABLE_TO_READ_REMOTE_DATA": "Ошибка при синхронизации. Не удается прочитать удаленные данные. Возможно, вы включили шифрование, и ваш локальный пароль не совпадает с тем, который использовался для шифрования удаленных данных?", "IMPORTING": "Импорт данных", "INCOMPLETE_CFG": "Ошибка аутентификации для синхронизации. Пожалуйста, проверьте свою конфигурацию!", "INITIAL_SYNC_ERROR": "Не удалось выполнить первоначальную синхронизацию", "SUCCESS_DOWNLOAD": "Синхронизация данных с удаленного управления", "SUCCESS_IMPORT": "Данные импортированы", "SUCCESS_VIA_BUTTON": "Данные успешно синхронизированы", "UNKNOWN_ERROR": "Неизвестная ошибка при синхронизации. Пожалуйста, проверьте console.", "UPLOAD_ERROR": "Неизвестная ошибка загрузки (правильные настройки?): {{err}}"}}, "TAG": {"D_CREATE": {"CREATE": "Создать тег", "EDIT": "Изменить тег"}, "D_DELETE": {"CONFIRM_MSG": "Вы действительно хотите удалить тег \"{{tagName}}\"? Он будет удален из всех задач. Это не может быть отменено."}, "D_EDIT": {"ADD": "Добавить теги для \"{{title}}\"", "EDIT": "Изменить теги для \"{{title}}\"", "LABEL": "Теги"}, "FORM_BASIC": {"L_COLOR": "Цвет (если используется неопределенный основной цвет темы)", "L_ICON": "Значок", "L_TITLE": "Название тега", "TITLE": "Базовые настройки"}, "S": {"UPDATED": "Настройки тегов были обновлены"}, "TTL": {"ADD_NEW_TAG": "Добавить новую метку"}}, "TASK": {"ADD_TASK_BAR": {"ADD_EXISTING_TASK": "Добавить существующую задачу \"{{taskTitle}}\"", "ADD_ISSUE_TASK": "Добавить задачу #{{issueNr}} из {{issueType}}", "ADD_TASK_TO_BOTTOM_OF_BACKLOG": "Добавить задачу в конец списка невыполненных задач", "ADD_TASK_TO_BOTTOM_OF_TODAY": "Добавить задачу в конец списка", "ADD_TASK_TO_TOP_OF_BACKLOG": "Добавить задачу в начало списка невыполненных задач", "ADD_TASK_TO_TOP_OF_TODAY": "Добавить задачу в начало списка", "CREATE_TASK": "Создать новую задачу", "EXAMPLE": "Пример: \"Название задачи +проект #тег1 #тег2 10m/3h\"", "START": "Нажмите Enter еще раз, чтобы начать", "TOGGLE_ADD_TO_BACKLOG_TODAY": "Переключить добавление задачи в список бэклог / на сегодня", "TOGGLE_ADD_TOP_OR_BOTTOM": "Переключатель добавления задачи в начало или конец списка"}, "ADDITIONAL_INFO": {"ADD_ATTACHMENT": "Добавить вложение", "ADD_SUB_TASK": "Добавить подзадачу", "ATTACHMENTS": "Вложения {{nr}}", "DUE": "Запланировано на", "FROM_PARENT": "(от родителя)", "LOCAL_ATTACHMENTS": "Локальные вложения", "NOTES": "Заметки", "PARENT": "Родитель", "REMINDER": "Напоминание", "REPEAT": "Повторение", "SCHEDULE_TASK": "Расписание задачи", "SUB_TASKS": "Подзадачи ({{nr}})", "TIME": "Время", "TITLE_PLACEHOLDER": "Введите заголовок"}, "B": {"ADD_HALF_HOUR": "Добавить полчаса", "ESTIMATE_EXCEEDED": "Оценка времени превышена для \"{{title}}\""}, "CMP": {"ADD_SUB_TASK": "Добавить подзадачу", "ADD_TO_MY_DAY": "Добавить в мой день", "ADD_TO_PROJECT": "Добавить в проект", "CONVERT_TO_PARENT_TASK": "Преобразовать в родительскую задачу", "DELETE": "Удалить задачу", "DELETE_REPEAT_INSTANCE": "Удалить задачу", "DROP_ATTACHMENT": "Нажмите сюда, чтобы прикрепить к \"{{title}}\"", "EDIT_SCHEDULED": "Изменить напоминание", "EDIT_TAGS": "Редактировать теги", "EDIT_TASK_TITLE": "Изменить заголовок", "FOCUS_SESSION": "Начать фокус-сессию", "MARK_DONE": "Отметить как выполненное", "MARK_UNDONE": "Отметить как отмененное", "MOVE_TO_BACKLOG": "Переместить в бэклог", "MOVE_TO_OTHER_PROJECT": "Переместить в другой проект", "MOVE_TO_REGULAR": "Перейти к сегодняшнему списку", "MOVE_TO_TOP": "Переместиться в начало списка", "OPEN_ATTACH": "Прикрепить файл или ссылку", "OPEN_ISSUE": "Открыть вопрос в новой вкладке браузера", "OPEN_TIME": "Оцените время / Добавьте потраченное время", "REMOVE_FROM_MY_DAY": "Удалить из моего дня", "REPEAT_EDIT": "Изменить повторную задачу", "SCHEDULE": "Расписание задачи", "SHOW_UPDATES": "Показать обновления", "TOGGLE_ATTACHMENTS": "Показать / Скрыть вложения", "TOGGLE_DETAIL_PANEL": "Показать / Скрыть дополнительную информацию", "TOGGLE_DONE": "Пометить как выполнено / отменить", "TOGGLE_SUB_TASK_VISIBILITY": "Переключить видимость подзадачи", "TOGGLE_TAGS": "Переключать метки", "TRACK_TIME": "Время начала отслеживания", "TRACK_TIME_STOP": "Пауза отслеживания времени", "UNSCHEDULE_TASK": "Задача вне расписания", "UPDATE_ISSUE_DATA": "Обновить данные о проблеме"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAG": {"MSG": "Вы хотите создать новый тэг {{tagsTxt}}?", "OK": "Создать тэг"}, "D_CONFIRM_SHORT_SYNTAX_NEW_TAGS": {"MSG": "Вы хотите создать новый тэг {{tagsTxt}}?", "OK": "Создать тэги"}, "D_REMINDER_VIEW": {"ADD_ALL_TO_TODAY": "Добавить все на сегодня", "ADD_TO_TODAY": "Добавить сегодня", "DONE": "Готово", "DUE_TASK": "Запланированная задача", "DUE_TASKS": "Запланированные задачи", "FOR_CURRENT": "Задача должна быть выполнена. Вы хотите начать работать над ней?", "FOR_OTHER": "Задача должна быть выполнена. Вы хотите начать работать над ней?", "FROM_PROJECT": "Из проекта", "FROM_TAG": "Из тега: \"{{title}}\"", "RESCHEDULE_EDIT": "Выбрать дату...", "RESCHEDULE_UNTIL_TOMORROW": "До завтра", "SNOOZE": "Отло<PERSON>ить", "SNOOZE_ALL": "Отложить все", "START": "Начать задание", "SWITCH_CONTEXT_START": "Переключить контекст и начать", "UNSCHEDULE": "Внеплановые", "UNSCHEDULE_ALL": "Отменить расписание всех"}, "D_SCHEDULE_TASK": {"MOVE_TO_BACKLOG": "Переместить задачу в список задач до запланированного времени", "QA_NEXT_MONTH": "На след. месяц", "QA_NEXT_WEEK": "На след. неделю", "QA_REMOVE_TODAY": "Удалить задание из сегодняшнего дня", "QA_TODAY": "На сегодня", "QA_TOMORROW": "На завтра", "REMIND_AT": "Напомнить", "RO_1H": "За 1 час до начала", "RO_5M": "За 5 минут до начала", "RO_10M": "За 10 минут до начала", "RO_15M": "За 15 минут до начала", "RO_30M": "За 30 минут до начала", "RO_NEVER": "Никогда", "RO_START": "Когда это начинается", "SCHEDULE": "Запланировать", "UNSCHEDULE": "Отменить планирование"}, "D_TIME": {"ADD_FOR_OTHER_DAY": "Добавить время, потраченное на другой день", "DELETE_FOR": "Удалить запись за день", "ESTIMATE": "Оценка", "TIME_SPENT": "Потрачено", "TIME_SPENT_ON": "Затраченное время {{date}}", "TITLE": "Времени потрачено / Оценки"}, "D_TIME_FOR_DAY": {"ADD_ENTRY_FOR": "Добавить новую запись для {{date}}", "DATE": "Дата новой записи", "HELP": "Примеры:<br> 30m => 30 минут<br> 2h => 2 часа<br> 2h 30m => 2 часа 30 минут", "TINE_SPENT": "Времени потрачено", "TITLE": "Назначить на день"}, "N": {"ESTIMATE_EXCEEDED": "Время превышено!", "ESTIMATE_EXCEEDED_BODY": "Вы превысили оценочное время для \"{{title}}\"."}, "S": {"CANNOT_ASSIGN_PROJECT_FOR_REPEATABLE_TASK": "Невозможно назначить проект с помощью краткого синтаксиса для повторяющихся задач!", "CREATED_FOR_PROJECT": "Перемещена задача \"{{taskTitle}}\" в проект \"{{projectTitle}}\"", "CREATED_FOR_PROJECT_ACTION": "Перейти к проекту", "DELETED": "Удалена задача \"{{title}}\"", "FOUND_MOVE_FROM_BACKLOG": "Существующее задание <strong>{{title}}</strong> перемещено в текущий список заданий", "FOUND_MOVE_FROM_OTHER_LIST": "Задача <strong>{{title}}</strong> из <strong>{{contextTitle}}</strong> добавлена в текущий список", "FOUND_RESTORE_FROM_ARCHIVE": "Задача <strong>{{title}}</strong> связана с задачей из архива восстановлена", "LAST_TAG_DELETION_WARNING": "Вы пытаетесь удалить последний тег непроектной задачи. Это не разрешено!", "MOVED_TO_ARCHIVE": "Перемещено {{nr}} задач в архив", "MOVED_TO_PROJECT": "Перемещена задача \"{{taskTitle}}\" в проект \"{{projectTitle}}\"", "MOVED_TO_PROJECT_ACTION": "Перейти к проекту", "REMINDER_ADDED": "Запла<PERSON><PERSON><PERSON><PERSON><PERSON>на задача \"{{title}}\"", "REMINDER_DELETED": "Удалено напоминание для задания", "REMINDER_UPDATED": "Обновлено напоминание для задачи \"{{title}}\"", "TASK_CREATED": "Создано задание \"{{title}}\""}, "SELECT_OR_CREATE": "Выберите или создайте задачу", "SUMMARY_TABLE": {"ESTIMATE": "Оценить", "SPENT_TODAY": "Потрачено сегодня", "SPENT_TOTAL": "Потрачено всего", "TASK": "Задача", "TOGGLE_DONE": "Пометить как выполненное / отменить"}}, "TASK_REPEAT": {"ADD_INFO_PANEL": {"CUSTOM": "Выборочная настройка", "CUSTOM_AND_TIME": "Пользовательский, {{timeStr}}", "CUSTOM_WEEKLY": "{{daysStr}}", "CUSTOM_WEEKLY_AND_TIME": "{{daysStr}}, {{timeStr}}", "DAILY": "Каждый день", "DAILY_AND_TIME": "Каждый день, {{timeStr}}", "EVERY_X_DAILY": "Каждые {{x}} дней", "EVERY_X_DAILY_AND_TIME": "Каждые {{x}} д<PERSON><PERSON><PERSON>, {{timeStr}}", "EVERY_X_MONTHLY": "Каждый {{x}} месяц", "EVERY_X_MONTHLY_AND_TIME": "Каждые {{x}} месяца,  {{timeStr}}", "EVERY_X_YEARLY": "Каждые {{x}} лет", "EVERY_X_YEARLY_AND_TIME": "Каждые {{x}} лет, {{timeStr}}", "MONDAY_TO_FRIDAY": "Пн-Пт", "MONDAY_TO_FRIDAY_AND_TIME": "Пн-Пт, {{timeStr}}", "MONTHLY_CURRENT_DATE": "Ежемесячно {{dateDayStr}} числа", "MONTHLY_CURRENT_DATE_AND_TIME": "Ежемесячно {{dateDayStr}} числа, {{timeStr}}", "WEEKLY_CURRENT_WEEKDAY": "Еженедельно {{weekdayStr}}", "WEEKLY_CURRENT_WEEKDAY_AND_TIME": "Еженедельно {{weekdayStr}}, {{timeStr}}", "YEARLY_CURRENT_DATE": "Ежегодно {{dayAndMonthStr}}", "YEARLY_CURRENT_DATE_AND_TIME": "Ежегодно {{dayAndMonthStr}}, {{timeStr}}"}, "D_CONFIRM_MOVE_TO_PROJECT": {"MSG": "Для этой повторяемой задачи созданы экземпляры {{tasksNr}}. Вы хотите переместить их все в проект \"{{projectName}}\"?", "OK": "Обновите все экземпляры"}, "D_CONFIRM_REMOVE": {"MSG": "Удаление повторяющейся конфигурации преобразует все предыдущие экземпляры этой задачи в обычные задачи. Вы уверены, что хотите продолжить", "OK": "Удалить полностью"}, "D_CONFIRM_UPDATE_INSTANCES": {"CANCEL": "Только будущие задачи", "MSG": "Для этой повторяющейся задачи созданы экземпляры {{tasksNr}}. Вы хотите обновить все из них, установив новые значения по умолчанию, или только будущие задачи?", "OK": "Обновите все экземпляры"}, "D_EDIT": {"ADD": "Добавить повторную настройку задачи", "ADVANCED_CFG": "Расширенная конфигурация", "EDIT": "Изменить повторную задачу", "HELP1": "Повторяющиеся задачи предназначены для ежедневной рутинной работы, например: «Организация», «Ежедневное собрание», «Проверка кода», «Проверка писем» или аналогичные задачи, которые могут возникать снова и снова.", "HELP2": "После настройки повторяющаяся задача будет воссоздана каждый день, выбранный ниже, как только вы откроете свой проект, и будет автоматически помечена как выполненная в конце дня. Они будут обрабатываться как разные экземпляры. Таким образом, вы можете свободно добавлять подзадачи и т.д.", "HELP3": "Задачи, импортированные из Jira или Git, не могут повторяться. Все напоминания также будут удалены при повторении задания.", "HELP4": "Примечание о поле заказа: относится к повторяющимся задачам порядка создания. Действует только для повторяющихся задач, которые создаются одновременно. Меньшее значение означает, что задача будет выше в списке, меньшее значение - ниже. Значение больше 0 означает, что элементы создаются в конце обычных задач.", "TAG_LABEL": "Теги для добавления"}, "F": {"C_DAY": "Дня", "C_MONTH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "C_WEEK": "Недели", "C_YEAR": "<PERSON>е<PERSON>", "DEFAULT_ESTIMATE": "Оценка по умолчанию", "FRIDAY": "Пятница", "IS_ADD_TO_BOTTOM": "Переместить задачу в конец списка", "MONDAY": "Понедельник", "NOTES": "Заметка по умолчанию", "ORDER": "порядок", "ORDER_DESCRIPTION": "Порядок создания повторяющихся задач. Влияет только на повторяющиеся задачи, созданные одновременно. Меньшее значение означает, что задача будет создана выше в списке, меньшее значение - ниже. Значение больше 0 означает, что элементы создаются в конце обычных задач.", "Q_CUSTOM": "Выборочная настройка", "Q_DAILY": "Каждый день", "Q_MONDAY_TO_FRIDAY": "С понедельника по пятницу", "Q_MONTHLY_CURRENT_DATE": "Каждый месяц {{dateDayStr}} числа", "Q_WEEKLY_CURRENT_WEEKDAY": "Каждую неделю {{weekdayStr}}", "Q_YEARLY_CURRENT_DATE": "Каждый год {{dayAndMonthStr}}", "QUICK_SETTING": "Настройка повторения", "REMIND_AT": "Напомнить в", "REMIND_AT_PLACEHOLDER": "Выберите, когда напоминать", "REPEAT_CYCLE": "Цикл повторения", "REPEAT_EVERY": "Повторять каждые", "SATURDAY": "Суббота", "START_DATE": "Дата начала", "START_TIME": "Запланированное время начала", "START_TIME_DESCRIPTION": "Например. 15:00. Оставьте поле пустым для задачи на весь день", "SUNDAY": "Воскресенье", "THURSDAY": "Четверг", "TITLE": "Название для задания", "TUESDAY": "Вторник", "WEDNESDAY": "Среда"}}, "TASK_VIEW": {"CUSTOMIZER": {"ENTER_PROJECT": "Введите проект", "ENTER_TAG": "Введите тег", "ESTIMATED_TIME": "Оценочное время", "FILTER_BY": "Фильтр по", "FILTER_DEFAULT": "Без фильтра", "FILTER_ESTIMATED_TIME": "Оценочное время", "FILTER_PROJECT": "Проект", "FILTER_SCHEDULED_DATE": "Запланированная дата", "FILTER_TAG": "Тег", "FILTER_TIME_SPENT": "Потраченное время", "GROUP_BY": "Группировать по", "GROUP_DEFAULT": "Без группы", "GROUP_PROJECT": "Проект", "GROUP_SCHEDULED_DATE": "Запланированная дата", "GROUP_TAG": "Тег", "RESET_ALL": "Сбросить все", "SCHEDULED_DEFAULT": "Любая дата", "SCHEDULED_NEXT_MONTH": "Следующий месяц", "SCHEDULED_NEXT_WEEK": "Следующая неделя", "SCHEDULED_THIS_MONTH": "Этот месяц", "SCHEDULED_THIS_WEEK": "Эта неделя", "SCHEDULED_TODAY": "Сегодня", "SCHEDULED_TOMORROW": "Завтра", "SORT_BY": "Сортировать по", "SORT_CREATION_DATE": "Дата создания", "SORT_DEFAULT": "По умолчанию", "SORT_NAME": "Имя", "SORT_SCHEDULED_DATE": "Запланированная дата", "TIME_1HOUR": "> 1 час", "TIME_2HOUR": "> 2 часа", "TIME_10MIN": "> 10 минут", "TIME_30MIN": "> 30 минут", "TIME_DEFAULT": "Любая продолжительность", "TIME_SPENT": "Потраченное время", "TITLE": "Настроить вид задач"}}, "TIME_TRACKING": {"B": {"ALREADY_DID": "Я уже сделал", "SNOOZE": "Отло<PERSON>ить {{time}}"}, "B_TTR": {"ADD_TO_TASK": "Добавить в задачу", "MSG": "Вы не отслеживали время для {{time}}"}, "D_IDLE": {"ADD_ENTRY": "Добавить запись для отслеживания", "BREAK": "Перерыв", "CREATE_AND_TRACK": "<em>Создать</em> и отследить до:", "IDLE_FOR": "Вы были без дела:", "RESET_BREAK_REMINDER_TIMER": "Сбросить таймер перерыва", "SIMPLE_CONFIRM_COUNTER_CANCEL": "Пропустить", "SIMPLE_CONFIRM_COUNTER_OK": "Отследить", "SIMPLE_COUNTER_CONFIRM_TXT": "Вы выбрали \"Пропустить\", но активировали кнопки простого счетчика ({{nr}}). Хотите отследить в него время простоя?", "SIMPLE_COUNTER_TOOLTIP": "Нажмите, чтобы отслеживать до {{title}}", "SIMPLE_COUNTER_TOOLTIP_DISABLE": "Нажмите, чтобы НЕ отслеживать до {{title}}", "SKIP": "Пропустить", "SPLIT_TIME": "Разделить время на несколько задач и/или перерывов", "TASK": "Задача", "TRACK_TO": "Отследить до:"}, "D_TRACKING_REMINDER": {"CREATE_AND_TRACK": "<em>Создать</em> и отслеживать", "IDLE_FOR": "Вы были без дела:", "NOTIFICATION_TITLE": "Следите за своим временем!", "TASK": "Задача", "TRACK_TO": "Отследить до:", "UNTRACKED_TIME": "Не отслеженное время:"}}, "WORKLOG": {"CMP": {"DAYS_WORKED": "Дней работы:", "MONTH_WORKED": "Месяцев работы:", "REPEATING_TASK": "Повторяющееся задание", "RESTORE_TASK_FROM_ARCHIVE": "Восстановить задачу из архива", "TASKS": "Зада<PERSON>и", "TOTAL_TIME": "Всего потрачено времени:", "WEEK_NR": "Неделя {{nr}}", "WORKED": "Проработано"}, "D_CONFIRM_RESTORE": "Вы уверены, что хотите переместить задачу <strong>\"{{title}}\"</strong> в свой текущий список задач?", "D_EXPORT_TITLE": "Экспорт журнала работ {{start}}-{{end}}", "D_EXPORT_TITLE_SINGLE": "Экспорт журнала работ {{day}}", "EXPORT": {"ADD_COL": "Добавить столбец", "COPY_TO_CLIPBOARD": "Скопировать в буфер обмена", "DONT_ROUND": "не округлять", "EDIT_COL": "Редактировать столбец", "GROUP_BY": "Группировать по", "O": {"DATE": "Дата", "ENDED_WORKING": "Работа закончена", "ESTIMATE_AS_CLOCK": "Оценить как часы (например, 5:23)", "ESTIMATE_AS_MILLISECONDS": "Оценить как миллисекунды", "ESTIMATE_AS_STRING": "Оценить как строку (например, 5h 23m)", "FULL_HALF_HOURS": "полных 1/2 часа", "FULL_HOURS": "полных часов", "FULL_QUARTERS": "полных 1/4 часа", "NOTES": "Описание задач", "PARENT_TASK": "Родительское задание", "PARENT_TASK_TITLES_ONLY": "Только названия родительских задач", "PROJECTS": "Имена проектов", "STARTED_WORKING": "Начало работы", "TAGS": "Теги", "TASK_SUBTASK": "Задача / Подзадача", "TIME_AS_CLOCK": "Время как часы (например, 5:23)", "TIME_AS_MILLISECONDS": "Время в миллисекундах", "TIME_AS_STRING": "Время как строка (например, 5h 23m)", "TITLES_AND_SUB_TASK_TITLES": "Заголовки и названия подзадач", "WORKLOG": "<PERSON><PERSON><PERSON><PERSON><PERSON> событий"}, "OPTIONS": "Опции", "ROUND_END_TIME_TO": "Округлить время окончания до", "ROUND_START_TIME_TO": "Округлить время начала до", "ROUND_TIME_WORKED_TO": "Округлить время работы до", "SAVE_TO_FILE": "Сохранить в файл", "SEPARATE_TASKS_BY": "Отдельные задачи по", "SHOW_AS_TEXT": "Показать как текст"}, "WEEK": {"EXPORT": "Экспорт данных за неделю", "NO_DATA": "На этой неделе пока нет заданий.", "TITLE": "Заголовок"}}}, "FILE_IMEX": {"DIALOG_CONFIRM_URL_IMPORT": {"INITIATED_MSG": "Автоматический импорт данных был инициирован.", "SOURCE_URL_DOMAIN": "Исходный домен", "TITLE": "Подтвердите импорт данных из URL", "WARNING_MSG": "Продолжение приведет к перезаписи ваших текущих данных и конфигурации приложения содержимым из указанного URL. Это действие невозможно отменить.", "WARNING_TITLE": "Предупреждение"}, "EXPORT_DATA": "Экспорт данных", "IMPORT_FROM_FILE": "Импорт из файла", "IMPORT_FROM_URL": "Импорт из URL", "IMPORT_FROM_URL_DIALOG_DESCRIPTION": "Пожалуйста, введите полный URL файла резервной копии JSON Super Productivity, который вы хотите импортировать.", "IMPORT_FROM_URL_DIALOG_TITLE": "Импорт из URL", "OPEN_IMPORT_FROM_URL_DIALOG": "Импорт из URL", "PRIVACY_EXPORT": "Экспортировать обезличенные данные (отправить <EMAIL> для отладки)", "S_BACKUP_DOWNLOADED": "Резервная копия загружена в папку с документами Android", "S_ERR_IMPORT_FAILED": "Ошибка импорта данных", "S_ERR_INVALID_DATA": "Ошибка импорта: неверный JSON", "S_ERR_INVALID_URL": "Ошибка импорта: указан недопустимый URL", "S_ERR_NETWORK": "Ошибка импорта: ошибка сети при получении данных с URL", "S_IMPORT_FROM_URL_ERR_DECODE": "Ошибка: не удалось декодировать параметр URL для импорта. Пожалуйста, убедитесь, что он правильно отформатирован.", "URL_PLACEHOLDER": "Введите URL для импорта"}, "G": {"ADD": "Добавить", "ADVANCED_CFG": "Расширенная настройка", "CANCEL": "Отменить", "CLOSE": "Закрыть", "CONFIRM": "Подтвердить", "DELETE": "Удалить", "DISMISS": "Отклонить", "DO_IT": "Сделать это!", "DURATION_DESCRIPTION": "например \"5h 23m\", что равно 5 часам и 23 минутам", "EDIT": "Редактировать", "ENABLED": "Включено", "EXAMPLE_VAL": "Нажмите, чтобы редактировать", "EXTENSION_INFO": "Пожалуйста, <a target=\"_blank\" href=\"https://chrome.google.com/webstore/detail/super-productivity/ljkbjodfmekklcoibdnhahlaalhihmlb\"> загрузите расширение Chrome</a> , чтобы обеспечить связь с Jira Api и Idle Time Handling. Обратите внимание, что это не работает для мобильных устройств.", "HIDE": "Скрыть", "ICON_INP_DESCRIPTION": "Также поддерживаются все эмодзи в формате utf-8!", "INBOX_PROJECT_TITLE": "Входящие", "LOGIN": "Авторизоваться", "LOGOUT": "Выйти", "MINUTES": "{{m}} минут", "MOVE_BACKWARD": "Двигаться назад", "MOVE_FORWARD": "Двигаться вперед", "NEXT": "Дальше", "NO_CON": "Вы в настоящее время не в сети. Пожалуйста, переподключитесь к интернету.", "NONE": "Никто", "OK": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OVERDUE": "Просроченные", "PREVIOUS": "Предыдущий", "REMOVE": "Удалить", "RESET": "Сброс", "SAVE": "Сохранить", "SUBMIT": "Подтвердить", "TITLE": "Заголовок", "TODAY_TAG_TITLE": "Сегодня", "TRACKING_INTERVAL_DESCRIPTION": "Отслеживайте время, используя этот интервал в миллисекундах. Возможно, вы захотите изменить это значение, чтобы сократить количество операций записи на диск. Смотрите проблему #2355.", "UNDO": "Отменить", "UPDATE": "Обновить", "WITHOUT_PROJECT": "Без проекта", "YESTERDAY": "Вчера"}, "GCF": {"AUTO_BACKUPS": {"HELP": "Автоматически сохранять все данные в папке вашего приложения, чтобы они были готовы на случай, если что-то пойдет не так", "LABEL_IS_ENABLED": "Включить автоматическое резервное копирование", "LOCATION_INFO": "Резервные копии сохраняются в:", "TITLE": "Автоматическое резервное копирование"}, "CALENDARS": {"BROWSER_WARNING": "<b>Скорее всего, это НЕ будет работать с браузерной версией Super Productivity.<br /> Пожалуйста, <a href=\"https://super-productivity.com/download/\">загрузите настольную версию</a>, чтобы использовать эту функцию!</b>", "CAL_PATH": "Url-адрес iCal", "CAL_PROVIDERS": "Поставщик календаря (экспериментально и опционально)", "CHECK_UPDATES": "Проверять обновления каждые Х", "DEFAULT_PROJECT": "Проект по умолчанию для добавленных задач", "HELP": "Вы можете интегрировать календари для напоминаний и добавлять их задачи в Super Productivity. Интеграция осуществляется в формате iCal. Для этого ваши календари должны быть доступны либо через Интернет, либо через файловую систему.", "SHOW_BANNER_THRESHOLD": "Показывать уведомления Х перед событием (очистить для отключения)"}, "EVALUATION": {"IS_HIDE_EVALUATION_SHEET": "Скрыть оценочный лист на ежедневной сводке", "TITLE": "Оценка и Метрики"}, "FOCUS_MODE": {"HELP": "Режим фокусировки открывает экран, свободный от отвлекающих факторов, который поможет вам сосредоточиться на текущей задаче.", "L_ALWAYS_OPEN_FOCUS_MODE": "Всегда открывайте режим фокусировки при отслеживании", "L_SKIP_PREPARATION_SCREEN": "Пропустить подготовительный экран (н-р растяжку)", "TITLE": "Режим фокуса"}, "IDLE": {"HELP": "<div><p>Когда обработка режима ожидания включена, через определенное время открывается диалог, чтобы проверить, хотите ли вы отслеживать свое время бездействия, и если да, то по какой задаче.</p></div>", "IS_ENABLE_IDLE_TIME_TRACKING": "Включить обработку режима ожидания", "IS_ONLY_OPEN_IDLE_WHEN_CURRENT_TASK": "Вызывать диалог режима ожидания только тогда, когда выбрана текущая задача", "MIN_IDLE_TIME": "Активировать режим ожидания после X", "TITLE": "Режим ожидания"}, "IMEX": {"HELP": "<p>Здесь вы можете экспортировать все свои данные как <strong>JSON</strong> для резервных копий, а также использовать их в другом контексте (например, вы можете экспортировать свои проекты в браузер и импортировать их в настольную версию). </p> <p>При импорте ожидается, что допустимый JSON будет скопирован в текстовую область. <strong>ПРИМЕЧАНИЕ. После нажатия кнопки импорта все ваши текущие настройки и данные будут перезаписаны!</strong></p>", "TITLE": "Импорт/Экспорт"}, "KEYBOARD": {"ADD_NEW_NOTE": "Добавить новую заметку", "ADD_NEW_TASK": "Добавить новую задачу", "APP_WIDE_SHORTCUTS": "Глобальные горячие клавиши (для всего приложения)", "COLLAPSE_SUB_TASKS": "Свернуть подзадачи", "EXPAND_SUB_TASKS": "Развернуть подзадачи", "GLOBAL_ADD_NOTE": "Добавить новую заметку", "GLOBAL_ADD_TASK": "Добавить новую задачу", "GLOBAL_SHOW_HIDE": "Показать/Скрыть Super Productivity", "GLOBAL_TOGGLE_TASK_START": "Переключить отслеживание времени для последней активной задачи", "GO_TO_DAILY_AGENDA": "Перейти к повестке дня", "GO_TO_FOCUS_MODE": "Перейти в режим фокусировки", "GO_TO_SCHEDULE": "Перейти на временную шкалу", "GO_TO_SCHEDULED_VIEW": "Перейти к запланированным задачам", "GO_TO_SETTINGS": "Перейдите в настройки", "GO_TO_WORK_VIEW": "Перейти к рабочему виду", "HELP": "<p>Здесь вы можете настроить все сочетания клавиш.</p> <p>Нажмите на ввод текста и введите нужную комбинацию клавиш. Нажмите Enter, чтобы сохранить, и Escape, чтобы прервать.</p> <p>Существует три типа сочетаний клавиш:</p> <ul> <li> <strong>Глобальные сочетания клавиш:</strong> Когда приложение запущено, оно будет запускать действие из любого другого приложения. </li> <li> <strong>Ярлыки уровня приложения:</strong> Срабатывает со всех экранов приложения, но не при редактировании текстового поля. </li> <li> <strong>Ярлыки на уровне задач:</strong> Они сработают только в том случае, если вы выбрали задачу с помощью мыши или клавиатуры, и, как правило, запускают действие, связанное именно с этой задачей. </li> </ul>", "MOVE_TASK_DOWN": "Переместить задачу вниз в списке", "MOVE_TASK_TO_BOTTOM": "Переместите задачу в конец списка", "MOVE_TASK_TO_TOP": "Переместить задачу в начало списка", "MOVE_TASK_UP": "Переместить задачу вверх в списке", "MOVE_TO_BACKLOG": "Переместить задачу в список задач", "MOVE_TO_REGULARS_TASKS": "Переместить задачу в список задач на сегодня", "OPEN_PROJECT_NOTES": "Показать/Скрыть заметки проекта", "SAVE_NOTE": "Сохранить заметку", "SELECT_NEXT_TASK": "Выберите следующую задачу", "SELECT_PREVIOUS_TASK": "Выберите предыдущее задание", "SHOW_SEARCH_BAR": "Показать панель поиска", "SYSTEM_SHORTCUTS": "Глобальные горячие клавиши (общесистемные)", "TASK_ADD_ATTACHMENT": "Прикрепить файл или ссылку", "TASK_ADD_SUB_TASK": "Добавить подзадачу", "TASK_DELETE": "Удалить задачу", "TASK_EDIT_TAGS": "Редактировать теги", "TASK_EDIT_TITLE": "Изменить заголовок", "TASK_MOVE_TO_PROJECT": "Открыть задание на перемещение в меню проекта", "TASK_OPEN_CONTEXT_MENU": "Открыть контекстное меню задачи", "TASK_OPEN_ESTIMATION_DIALOG": "Изменить оценку / затраченное время", "TASK_PLAN_FORDAY": "План на день", "TASK_SCHEDULE": "Расписание задачи", "TASK_SHORTCUTS": "Зада<PERSON>и", "TASK_SHORTCUTS_INFO": "Следующие сочетания клавиш применяются для текущей выбранной задачи (выбирается с помощью вкладки или мыши).", "TASK_TOGGLE_DETAIL_PANEL_OPEN": "Показать/Скрыть дополнительную информацию о задаче", "TASK_TOGGLE_DONE": "Переключить Готово", "TITLE": "Горячие клавиши", "TOGGLE_BACKLOG": "Показать/Скрыть невыполненные задачи", "TOGGLE_BOOKMARKS": "Показать/Скрыть панель закладок", "TOGGLE_ISSUE_PANEL": "Показать/скрыть панель выдачи", "TOGGLE_PLAY": "Запустить/Остановить задачу", "TOGGLE_SIDE_NAV": "Показать и сфокусировать / Скрыть боковую навигацию", "TOGGLE_TASK_VIEW_CUSTOMIZER_PANEL": "Переключить панель фильтрации/группировки/сортировки", "TRIGGER_SYNC": "Синхронизация запуска (если настроена)", "ZOOM_DEFAULT": "Масштаб по умолчанию (только для ПК)", "ZOOM_IN": "Увеличить (только для ПК)", "ZOOM_OUT": "Уменьшить (только для ПК)"}, "LANG": {"AR": "Арабский", "CZ": "Чешский", "DE": "Немецкий", "EN": "Английский", "ES": "Испанский", "FA": "Персидский", "FR": "Французский", "HR": "Хорватский", "ID": "Индонезийский", "IT": "Итальянский", "JA": "Японский", "KO": "Корейский", "LABEL": "Пожалуйста, выберите язык", "NB": "Норвежский букмол", "NL": "Голландский", "PL": "Польский", "PT": "Португальский", "RU": "Русский", "SK": "Словацкий", "TITLE": "Язык", "TR": "Турецкий", "UK": "Украинский", "ZH": "Китайский (упрощенный)", "ZH_TW": "Китайский (традиционный)"}, "MISC": {"DEFAULT_PROJECT": "Проект по умолчанию, используемый для задач, если ни один не указан", "FIRST_DAY_OF_WEEK": "Первый день недели", "HELP": "<p><strong>Не видите уведомления на рабочем столе?</strong> Для Windows вы можете проверить \"Система > Уведомления и действия\" и проверить, включены ли необходимые уведомления.</p>", "IS_AUTO_ADD_WORKED_ON_TO_TODAY": "Автоматически добавлять тег \"today\" для работы над задачами", "IS_AUTO_MARK_PARENT_AS_DONE": "Отметить родительскую задачу как выполненную, когда все подзадачи выполнены", "IS_CONFIRM_BEFORE_EXIT": "Подтверждать выход из приложения", "IS_CONFIRM_BEFORE_EXIT_WITHOUT_FINISH_DAY": "Подтвердите перед выходом из приложения, не закончив первый день", "IS_DARK_MODE": "Темный режим", "IS_DISABLE_ANIMATIONS": "Отключить все анимации", "IS_HIDE_NAV": "Скрыть навигацию, пока основной заголовок не наведен (только на рабочем столе)", "IS_MINIMIZE_TO_TRAY": "Сворачивать в трей (только для ПК)", "IS_SHOW_TIP_LONGER": "Показывать советы по продуктивности при запуске приложения немного дольше", "IS_TRAY_SHOW_CURRENT_COUNTDOWN": "Показать текущее время обратного отсчета в трее / меню состояния (только для настольных Mac)", "IS_TRAY_SHOW_CURRENT_TASK": "Показать текущую задачу в трее / меню состояния (только на рабочем столе)", "IS_TURN_OFF_MARKDOWN": "Отключить Markdown для заметок", "IS_USE_MINIMAL_SIDE_NAV": "Использовать минимальную панель навигации (отображаются только значки).", "START_OF_NEXT_DAY": "Время начала следующего дня", "START_OF_NEXT_DAY_HINT": "с какого момента (в часах) хотите отсчитывать начало дня. по умолчанию полночь, что равно 0.", "TASK_NOTES_TPL": "Шаблон описания задачи", "TITLE": "Разные настройки"}, "POMODORO": {"BREAK_DURATION": "Продолжительность коротких перерывов", "CYCLES_BEFORE_LONGER_BREAK": "Длинный перерыв после X рабочих сессий", "DURATION": "Продолжительность рабочих сессий", "HELP": "<p>Таймер Pomodoro можно настроить с помощью нескольких настроек. Продолжительность каждого рабочего сеанса, продолжительность обычных перерывов, количество рабочих сессий, которые должны быть выполнены до запуска более длительного перерыва, и продолжительность этого более длительного перерыва.</p> <p>Вы также можете установить, хотите ли вы отображать ваши отвлекающие моменты во время перерывов на помодоро.</p> <p>Параметр «Отслеживание времени паузы при pomodoro break» также будет отслеживать ваши перерывы как рабочее время, затраченное на задачу. </p> <p>Включение «Приостановить сессию pomodoro, когда нет активной задачи» также приостановит сессию pomodoro, когда вы приостановите задачу.</p>", "IS_ENABLED": "Включить тай<PERSON><PERSON><PERSON>dor<PERSON>", "IS_MANUAL_CONTINUE": "Вручную подтвердить начало следующей сессии Pomodoro", "IS_MANUAL_CONTINUE_BREAK": "Вручную подтвердить начало следующего перерыва", "IS_PLAY_SOUND": "Воспроизвести звук, когда сессия завершена", "IS_PLAY_SOUND_AFTER_BREAK": "Воспроизводить звуковое уведомление, когда перерыв сделан", "IS_PLAY_TICK": "Слушать звук часов каждую секунду", "IS_STOP_TRACKING_ON_BREAK": "Остановить время отслеживания для задачи во время перерыва", "LONGER_BREAK_DURATION": "Продолжительность более длительных перерывов", "TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "REMINDER": {"COUNTDOWN_DURATION": "Показывать баннер X перед фактическим напоминанием", "IS_COUNTDOWN_BANNER_ENABLED": "Показывать баннер обратного отсчета до того, как появятся напоминания", "TITLE": "Напоминания"}, "SCHEDULE": {"HELP": "Функция временной шкалы должна предоставить вам быстрый обзор того, как ваши запланированные задачи выполняются с течением времени. Вы можете найти её в левом меню в разделе <a href='#/schedule'>«Временная шкала»</a>.", "L_IS_LUNCH_BREAK_ENABLED": "Разрешить перерыв на обед", "L_IS_WORK_START_END_ENABLED": "Ограничить поток незапланированных задач определенным временем работы", "L_LUNCH_BREAK_END": "Окончание обеда", "L_LUNCH_BREAK_START": "Начало обеда", "L_WORK_END": "Конец рабочего дня", "L_WORK_START": "Начало рабочего дня", "LUNCH_BREAK_START_END_DESCRIPTION": "например 13:00", "TITLE": "Временная шкала", "WORK_START_END_DESCRIPTION": "например 17:00"}, "SHORT_SYNTAX": {"HELP": "<p>Здесь вы можете управлять параметрами короткого синтаксиса при создании задачи</p>", "IS_ENABLE_DUE": "Включите короткий синтаксис (@<Due time>)", "IS_ENABLE_PROJECT": "Включить короткий синтаксис проекта (+<Project name>)", "IS_ENABLE_TAG": "Включить короткий синтакси<PERSON> тегов (#<Tag>)", "TITLE": "Краткий синтаксис"}, "SOUND": {"BREAK_REMINDER_SOUND": "Звук напоминания о перерыве", "DONE_SOUND": "Звук выполненной задачи", "IS_INCREASE_DONE_PITCH": "Увеличивать высоту звука для каждой выполненной задачи", "TITLE": "Звук", "TRACK_TIME_SOUND": "Звуковое напоминание о времени отслеживания", "VOLUME": "Громкость"}, "TAKE_A_BREAK": {"ADD_NEW_IMG": "Добавить мотивационное изображение", "FULL_SCREEN_BLOCKER_DURATION": "Длительность отображения полноэкранного окна (только на ПК)", "HELP": "<div><p>Позволяет настроить повторяющееся напоминание, когда вы работали в течение определенного времени без перерыва.</p><p>Вы можете изменить отображаемое сообщение. ${duration} будет заменено временем, проведенным без перерыва.</p></div>", "IS_ENABLED": "Включить напоминание о перерыве", "IS_FOCUS_WINDOW": "Фокусироваться на окне приложения, когда напоминание активно (только на ПК)", "IS_FULL_SCREEN_BLOCKER": "Отображать сообщение в полноэкранном режиме (только на ПК)", "IS_LOCK_SCREEN": "Блокировка экрана во время перерыва (только на ПК)", "MESSAGE": "Сообщение о перерыве", "MIN_WORKING_TIME": "Уведомить о перерыве после X работы без перерыва", "MOTIVATIONAL_IMGS": "Мотивационное изображение (используйте веб URL)", "NOTIFICATION_TITLE": "Сделай перерыв!", "SNOOZE_TIME": "Время перед повторным сообщением при одкладывании перерыва", "TITLE": "Напоминание о перерыве"}, "TIME_TRACKING": {"HELP": "Напоминание об отслеживании времени - это баннер, который появляется на случай, если вы забыли начать отслеживать время.", "L_DEFAULT_ESTIMATE": "Оценка времени по умолчанию для новых задач", "L_DEFAULT_ESTIMATE_SUB_TASKS": "Оценка времени по умолчанию для новых подзадач", "L_IS_AUTO_START_NEXT_TASK": "Начать отслеживать следующую задачу, пометив текущую как выполненную", "L_IS_NOTIFY_WHEN_TIME_ESTIMATE_EXCEEDED": "Уведомлять о превышении расчетного времени", "L_IS_TRACKING_REMINDER_ENABLED": "Включить напоминание об отслеживании", "L_IS_TRACKING_REMINDER_FOCUS_WINDOW": "Фокусировка окна приложения при активном напоминании (только для настольных компьютеров)", "L_IS_TRACKING_REMINDER_NOTIFY": "Уведомлять о том, что отображается напоминание об отслеживании времени", "L_IS_TRACKING_REMINDER_SHOW_ON_MOBILE": "Показывать напоминание об отслеживании в мобильном приложении", "L_TRACKING_INTERVAL": "Интервал отслеживания времени (ЭКСПЕРИМЕНТАЛЬНЫЙ)", "L_TRACKING_REMINDER_MIN_TIME": "Время ожидания перед показом баннера с напоминанием об отслеживании", "TITLE": "Отслеживание времени"}}, "GLOBAL_RELATIVE_TIME": {"FUTURE": {"A_DAY": "Через день", "A_MINUTE": "через минуту", "A_MONTH": "Через месяц", "A_YEAR": "через год", "AN_HOUR": "через час", "DAYS": "через {{count}} дней", "FEW_SECONDS": "через несколько секунд", "HOURS": "через {{count}} часов", "MINUTES": "через {{count}} минут", "MONTHS": "через {{count}} месяцев", "YEARS": "через {{count}} лет"}, "PAST": {"A_DAY": "день назад", "A_MINUTE": "минуту назад", "A_MONTH": "месяц назад", "A_YEAR": "год назад", "AN_HOUR": "час назад", "DAYS": "{{count}} дней назад", "FEW_SECONDS": "несколько секунд назад", "HOURS": "{{count}} часов назад", "MINUTES": "{{count}} минут назад", "MONTHS": "{{count}} месяцев назад", "YEARS": "{{count}} лет назад"}}, "GLOBAL_SNACK": {"COPY_TO_CLIPPBOARD": "Скопировано в буфер обмена", "ERR_COMPRESSION": "Ошибка для интерфейса сжатия", "FILE_DOWNLOADED": "{{fileName}} скачано", "FILE_DOWNLOADED_BTN": "Открыть папку", "NAVIGATE_TO_TASK_ERR": "Не удалось сосредоточиться на задаче. Удалить?", "PERSISTENCE_DISALLOWED": "Данные не будут сохранены постоянно. Имейте в виду, что это может привести к потере данных!!", "PERSISTENCE_ERROR": "Ошибка при запросе на сохранение данных: {{err}}", "RUNNING_X": "Запуск \"{{str}}\".", "SHORTCUT_WARN_OPEN_BOOKMARKS_FROM_TAG": "{{keyCombo}} нажата, но гоячая клавиша для открытия закладок доступена только в контексте проекта."}, "GPB": {"ASSETS": "Загрузка ресурсов ...", "DBX_DOWNLOAD": "Dropbox: Скачивание файла...", "DBX_GEN_TOKEN": "Dropbox: Создание токена...", "DBX_META": "Dropbox: Получение мета-файла...", "DBX_UPLOAD": "Dropbox: Загрузка файла...", "GITHUB_LOAD_ISSUE": "GitHub: Загрузка данных о проблеме ...", "JIRA_LOAD_ISSUE": "Jira: Загрузка данных о проблеме...", "SYNC": "Синхронизация данных...", "UNKNOWN": "Загрузка удаленных данных", "WEB_DAV_DOWNLOAD": "WebDAV: Скачивание данных...", "WEB_DAV_UPLOAD": "WebDAV: Загрузка данных..."}, "MH": {"ADD_NEW_TASK": "Добавить новую задачу", "ALL_PLANNED_LIST": "Повторяющиеся / запланированные", "BOARDS": "Доски", "CREATE_PROJECT": "Создать проект", "CREATE_TAG": "Создать тег", "DELETE_PROJECT": "Удалить проект", "DELETE_TAG": "Удалить тег", "ENTER_FOCUS_MODE": "Войти в режим фокуса", "GO_TO_TASK_LIST": "Перейти к списку задач", "HELP": "Помощь", "HM": {"CALENDARS": "Как подключить календари", "CONTRIBUTE": "Вклад", "GET_HELP_ONLINE": "Получить помощь онлайн", "KEYBOARD": "Инструкция: Продвинутая клавиатура", "REDDIT_COMMUNITY": "Сообщество Reddit", "REPORT_A_PROBLEM": "Сообщить о проблеме", "START_WELCOME": "Начать приветственный тур", "SYNC": "Как настроить синхронизацию"}, "METRICS": "Метрика", "NO_PROJECT_INFO": "Нет доступных проектов. Вы можете создать новый проект, нажав кнопку \"Создать проект\".", "NO_TAG_INFO": "В настоящее время тегов нет. Вы можете добавить теги, введя \"#НазваниеТега\" при добавлении или редактировании задач.", "NOTES": "Заметки", "NOTES_PANEL_INFO": "Заметки могут быть показаны только в режиме просмотра расписания и обычного списка задач.", "PLANNER": "Пла<PERSON><PERSON><PERSON><PERSON>щик", "PROCRASTINATE": "Прокрастинировать", "PROJECT_MENU": "Меню проекта", "PROJECT_SETTINGS": "Настройки проекта", "PROJECTS": "Проекты", "QUICK_HISTORY": "Быстрая история", "SCHEDULE": "Расписание", "SEARCH": "Поиск", "SETTINGS": "Настройки", "SHOW_SEARCH_BAR": "Показать панель поиска", "TAGS": "Теги", "TASK_LIST": "Список задач", "TASKS": "Зада<PERSON>и", "TOGGLE_SHOW_BOOKMARKS": "Показать / Скрыть закладки", "TOGGLE_SHOW_ISSUE_PANEL": "Показать/скрыть панель выдачи", "TOGGLE_SHOW_NOTES": "Показать / Скрыть заметки проекта", "TOGGLE_TRACK_TIME": "Время начала/остановки отслеживания", "TRIGGER_SYNC": "Синхронизировать вручную", "WORKLOG": "<PERSON><PERSON><PERSON><PERSON><PERSON> событий"}, "MIGRATE": {"C_DOWNLOAD_BACKUP": "Вы хотите загрузить резервную копию устаревших данных (можно использовать с более старыми версиями Super Productivity)?", "DETECTED_LEGACY": "Обнаружены устаревшие данные. Мы перенесем его за вас!", "E_MIGRATION_FAILED": "Миграция не удалась! с ошибкой:", "E_RESTART_FAILED": "Автоматический перезапуск не удался. Пожалуйста, перезапустите приложение вручную!", "SUCCESS": "Миграция завершена! Перезапуск приложения сейчас..."}, "PDS": {"ADD_TASKS_FROM_TODAY": "Добавляйте задачи с сегодняшнего дня", "BACK": "Подождите, я кое-что забыл!", "BREAK_LABEL": "Перерывы (кол-во / время)", "CELEBRATE": "Найдите минутку, чтобы <i>отпраздновать!</i>", "CLEAR_ALL_CONTINUE": "Очистить все завершённые и продолжить", "D_CONFIRM_APP_CLOSE": {"CANCEL": "Нет, просто очистить задачи", "MSG": "Ваша работа выполнена. Пора идти домой!", "OK": "Да, да! Выключай!"}, "ESTIMATE_TOTAL": "Общая оценка:", "EVALUATE_DAY": "Оценить", "EXPORT_TASK_LIST": "Экспорт списка задач", "NO_TASKS": "На этот день нет задач", "PLAN_TOMORROW": "<PERSON><PERSON><PERSON><PERSON>", "REVIEW_TASKS": "Обзор", "ROUND_5M": "Округлить все задачи до 5 минут", "ROUND_15M": "Округлить все задачи до 15 минут", "ROUND_30M": "Округлить все задачи до 30 минут", "ROUND_TIME_SPENT": "Округлить потраченное время", "ROUND_TIME_SPENT_TITLE": "Округление затраченного времени на всех задачах. Будьте осторожны! Вы не сможете это отменить!", "ROUND_TIME_WARNING": "!!! Будьте осторожны, это не может быть отменено !!!", "ROUND_UP_5M": "Округлить все задачи до 5 минут", "ROUND_UP_15M": "Округлить все задачи до 15 минут", "ROUND_UP_30M": "Округлить все задачи до 30 минут", "SAVE_AND_GO_HOME": "Сохранить и идти домой", "SAVE_AND_GO_HOME_TOOLTIP": "Переместить все выполненные задачи в архив, синхронизировать и закрыть приложение.", "START_END": "Начало - Конец", "SUMMARY_FOR": "Ежедневная сводка для {{dayStr}}", "TASKS_COMPLETED": "Выполненные задачи", "TIME_SPENT_AND_ESTIMATE_LABEL": "Затраченное / Оченочное время", "TIME_SPENT_ESTIMATE_TITLE": "Затраченное время: Общее время, затраченное сегодня. Архивные задачи не учтены. - Оценочное время: оценочное время для задач, отработанных сегодня, минус время, проведенное с ними в другие дни.", "TIME_SPENT_TODAY_BY_TAG": "Потраченное время сегодня по тэгам", "WEEK": "Неделя"}, "PM": {"TITLE": "Метрики проекта"}, "PS": {"GLOBAL_SETTINGS": "Глобальные настройки", "ISSUE_INTEGRATION": "Интеграция задач", "PRIVACY_POLICY": "Политика конфиденциальности", "PRODUCTIVITY_HELPER": "Помощник производительности", "PROJECT_SETTINGS": "Настройки проекта", "PROVIDE_FEEDBACK": "Обратная связь", "SYNC_EXPORT": "Синхронизация и экспорт", "TAG_SETTINGS": "Специальные настройки тегов", "TOGGLE_DARK_MODE": "Переключить темный режим"}, "SCHEDULE": {"NO_REPEATABLE_TASKS": "На данный момент нет повторяющихся задач. Вы можете запланировать задачу, выбрав \"Повторение\" в боковой панели задач. Чтобы открыть её, нажмите на самую правую иконку, которая появляется при наведении на задачу (или просто нажмите на задачу в смартфоне).", "NO_SCHEDULED": "В настоящее время нет запланированных задач. Вы можете запланировать задачу, выбрав «Запланировать задачу» в контекстном меню задачи. Чтобы открыть его, нажмите на 3 маленькие точки справа от задачи.", "NO_SCHEDULED_TITLE": "Запланированные задачи на день", "REPEATED_TASKS": "Повторяющиеся задачи", "SCHEDULED_TASKS": "Запланированные задачи", "SCHEDULED_TASKS_WITH_TIME": "Запланированные задачи с напоминанием", "START_TASK": "Запустите задачу сейчас и удалите напоминание"}, "THEMES": {"amber": "янтарный", "blue": "синий", "blue-grey": "серо-голубой", "cyan": "циан", "deep-orange": "темно-оранжевый цвет", "deep-purple": "темно-фиолетовый", "green": "зеленый", "indigo": "индиго", "light-blue": "светло-синий", "light-green": "светло-зеленый", "lime": "лайм", "pink": "розовый", "purple": "пурпурный", "SELECT_THEME": "Выберите тему", "teal": "чирок", "yellow": "жел<PERSON>ый"}, "V": {"E_1TO10": "Пожалуйста, введите значение от 1 до 10", "E_DATETIME": "Введенное значение не является датой и временем!", "E_DURATION": "Пожалуйста, введите действительную продолжительность (например, 1 час)", "E_MAX": "Не должно быть больше, чем {{val}}", "E_MAX_LENGTH": "Должно быть не более {{val}} символов", "E_MIN": "Должно быть меньше, чем {{val}}", "E_MIN_LENGTH": "Должно быть длиной не менее {{val}} символов", "E_PATTERN": "Неправильный ввод", "E_REQUIRED": "Это поле обязательно к заполнению"}, "WW": {"ADD_MORE": "Добавить еще", "ADD_SCHEDULED_FOR_TOMORROW": "Добавить задачи, запланированные на завтра ({{nr}})", "ADD_SOME_TASKS": "Добавьте несколько задач, чтобы спланировать свой день!", "DONE_TASKS": "Выполненные задачи", "DONE_TASKS_IN_ARCHIVE": "Нет выполненных задач, но некоторые из них уже заархивированы.", "ESTIMATE_REMAINING": "Осталось примерно:", "FINISH_DAY": "Закончить день", "FINISH_DAY_FOR_PROJECT": "Закончить день для этого проекта", "FINISH_DAY_FOR_TAG": "Закончить день для этого тега", "FINISH_DAY_TOOLTIP": "Оцените свой день, переместите все выполненные задачи в архив (по желанию) и/или  спланируйте следующий день.", "HELP_PROCRASTINATION": "Помогите, я прокрастинирую!", "MOVE_DONE_TO_ARCHIVE": "Переместить в архив", "NO_DONE_TASKS": "В настоящее время нет выполненных задач", "NO_PLANNED_TASK_ALL_DONE": "все задачи выполнены", "NO_PLANNED_TASKS": "Нет запланированных задач", "READY_TO_WORK": "Готов к работе!", "RESET_BREAK_TIMER": "Сброс таймера без перерыва", "TIME_ESTIMATED": "Расчетное время:", "TODAY_REMAINING": "Оставшиеся на сегодня:", "WITHOUT_BREAK": "Без перерыва:", "WORKING_TODAY": "Работаю сегодня:", "WORKING_TODAY_ARCHIVED": "Время, потраченное сегодня на выполнение архивных задач"}}