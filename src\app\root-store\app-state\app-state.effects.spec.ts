// import { TestBed } from '@angular/core/testing';
// import { provideMockActions } from '@ngrx/effects/testing';
// import { Observable } from 'rxjs';
//
// import { AppStateEffects } from './app-state.effects';
//
// describe('AppStateEffects', () => {
//   let actions$: Observable<any>;
//   let effects: AppStateEffects;
//
//   beforeEach(() => {
//     TestBed.configureTestingModule({
//       providers: [
//         AppStateEffects,
//         provideMockActions(() => actions$)
//       ]
//     });
//
//     effects = TestBed.inject(AppStateEffects);
//   });
//
//   it('should be created', () => {
//     expect(effects).toBeTruthy();
//   });
// });
