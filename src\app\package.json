{"name": "configurator-test-ui", "private": true, "description_1": "This is a special package.json file that is not used by package managers.", "description_2": "It is used to tell the tools and bundlers whether the code under this directory is free of code with non-local side-effect. Any code that does have non-local side-effects can't be well optimized (tree-shaken) and will result in unnecessary increased payload size.", "description_3": "It should be safe to set this option to 'false' for new applications, but existing code bases could be broken when built with the production config if the application code does contain non-local side-effects that the application depends on.", "description_4": "To learn more about this file see: https://angular.io/config/app-package-json.", "NOTE": "Setting sideEffects to true because of some weird behavior in production builds, see https://github.com/johannesjo/super-productivity/pull/501#issuecomment-694488795", "sideEffects": true}