name: Manual Build Windows Release & Mac
on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Define branch name'
        required: true
        default: 'master'

jobs:
  windows-bin:
    runs-on: windows-latest

    steps:
      - uses: actions/setup-node@v3
        with:
          node-version: 20
      # required because setting via env.TZ does not work on windows
      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      # work around for npm installs from git+https://github.com/johannesjo/J2M.git
      - name: Reconfigure git to use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/
      - name: Get npm cache directory
        id: npm-cache-dir
        run: |
          echo "::set-output name=dir::$(npm config get cache)"
      - uses: actions/cache@v4
        id: npm-cache # use this to check for `cache-hit` ==> if: steps.npm-cache.outputs.cache-hit != 'true'
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Workaround for nx issue
        run: npm install @nx/nx-win32-x64-msvc

      - name: Install npm Packages
        #  if: steps.npm-cache.outputs.cache-hit != 'true'
        run: npm i

      - name: Build Frontend & Electron TS
        run: npm run buildAllElectron:noTests:prod

      - name: Build Electron app
        uses: johannesjo/action-electron-builder@v1
        with:
          build_script_name: empty
          github_token: ${{ secrets.github_token }}
          release: false

      - name: 'Upload Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: WinBuildStuff
          path: .tmp/app-builds/*.exe

  mac-bin:
    runs-on: macos-latest

    steps:
      - uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: Echo is Release
        run: echo "IS_RELEASE $IS_RELEASE, ${{ startsWith(github.ref, 'refs/tags/v') }}"
        env:
          IS_RELEASE: ${{ startsWith(github.ref, 'refs/tags/v') }}

      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      # work around for npm installs from git+https://github.com/johannesjo/J2M.git
      - name: Reconfigure git to use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/

      - name: Get npm cache directory
        id: npm-cache-dir
        run: |
          echo "::set-output name=dir::$(npm config get cache)"
      - uses: actions/cache@v4
        id: npm-cache # use this to check for `cache-hit` ==> if: steps.npm-cache.outputs.cache-hit != 'true'
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Workaround for nx issue and dmg licence issue
        run: npm install @nx/nx-darwin-arm64 dmg-license

      - name: Install npm Packages
        #  if: steps.npm-cache.outputs.cache-hit != 'true'
        run: npm i

      - run: 'echo "$PROVISION_PROFILE" | base64 --decode > embedded.provisionprofile'
        shell: bash
        env:
          PROVISION_PROFILE: ${{secrets.dl_provision_profile}}

      - name: Prepare for app notarization
        # Import Apple API key for app notarization on macOS
        run: |
          mkdir -p ~/private_keys/
          echo '${{ secrets.mac_api_key }}' > ~/private_keys/AuthKey_${{ secrets.mac_api_key_id }}.p8

      - name: Lint
        run: npm run lint:ci

      - name: Test Unit
        run: npm run test

      #      - uses: browser-actions/setup-chrome@v1
      #        id: setup-chrome
      #      - run: |
      #          echo Installed chromium version: ${{ steps.setup-chrome.outputs.chrome-version }}
      #          ${{ steps.setup-chrome.outputs.chrome-path }} --version
      # Disabled because not working atm: https://github.com/johannesjo/super-productivity/actions/runs/5924016145/job/16060737982
      #      - name: Test E2E
      #        run: npm run e2e

      - name: Build Frontend & Electron
        run: npm run build

      - name: Build/Release Electron app
        uses: johannesjo/action-electron-builder@v1
        with:
          build_script_name: empty
          github_token: ${{ secrets.github_token }}
          mac_certs: ${{ secrets.mac_certs }}
          mac_certs_password: ${{ secrets.mac_certs_password }}
          release: ${{ startsWith(github.ref, 'refs/tags/v') }}
          # macOS notarization API key
        env:
          API_KEY_ID: ${{ secrets.mac_api_key_id }}
          API_KEY_ISSUER_ID: ${{ secrets.mac_api_key_issuer_id }}
          APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.APPLE_APP_SPECIFIC_PASSWORD }}
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}

      # - name: notary log
      #  if: always()
      # run: ls -la && cat notarization-error.log
      - name: 'Upload Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: dmg
          path: .tmp/app-builds/*.dmg
