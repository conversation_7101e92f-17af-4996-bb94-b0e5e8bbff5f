@import '../../../../common';

.dialog-help-wrapper {
  :host-context([dir='rtl']) & {
    direction: rtl;
  }
}

.wrap-time {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  margin-bottom: var(--s);

  @include mq(xs, max) {
    margin-left: -20px;
    margin-right: -20px;
  }
}

.other-days {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.other-day {
  display: flex;
  align-items: center;
}

.side-info {
  margin-top: var(--s);
}
