# Thank you for considering to contribute!
I love Super Productivity. It's my favorite side project and I use it every day to plan my tasks and to track my time. But my skill set and also my perspective after using it for over two years are limited. I need your help!

## Things that would help

### Feedback
* What do you like and what not?
* What should be improved?
* What essential features are missing?
* If you don't, why won't you use the app?
* Reporting bugs

### Design & UX
I am not a designer. So there is probably a lot not to like. It would be absolutely great if a professional could have a look or two.
* Are the general concepts working? What should be improved?
* Providing better icons

### Features/Coding
* Implementing GitLab support
* Implementing support for private GitHub repositories
* Improved data syncing, 
  * syncing smaller chunks instead of the complete data
  * support to save the data to your own cloud storage (own cloud, Dropbox, etc.)
  * I am a fan of owning your data. The ideal would be to achieve a completely syncable [unhosted web app](https://unhosted.org/)

### Translations
* I ran the app through google translate. So there is at least one reason why they suck...

